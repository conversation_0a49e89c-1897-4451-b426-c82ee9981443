import json
import math
import networkx as nx
import pandas as pd
import os
import traceback

def load_data_from_json(file_path, filtered_pools=None):
    """
    Load data from a JSON file
    
    Args:
        file_path: Path to the JSON file
        filtered_pools: Optional set of pool addresses to filter by
    """
    with open(file_path, 'r') as file:
        data = json.load(file)
    
    # If filtered_pools is provided, only return pools in the filter list
    if filtered_pools:
        filtered_data = []
        for pool in data:
            try:
                pool_data = json.loads(pool) if isinstance(pool, str) else pool
                pool_address = pool_data['poolState']['poolState']['poolAddress'].lower()
                if pool_address in filtered_pools:
                    filtered_data.append(pool)
            except (KeyError, json.JSONDecodeError):
                continue
        return filtered_data
    
    return data

def compute_exchange_rate(reserveA, reserveB, trade_amount):
    if trade_amount >= reserveA:
        return None # Invalid trade, prevents division by zero

    new_reserveA = reserveA + trade_amount  # Adding traded amount to reserveA
    new_reserveB = (reserveA * reserveB) / new_reserveA  # Solve for new reserveB using AMM formula
    new_rate = new_reserveB / new_reserveA  # Apply fee and compute rate
    return new_rate#, new_reserveA, new_reserveB

def load_token_prices(price_file_path):
    """
    Load token prices from the quotes file
    Returns a dictionary mapping token address to its WETH price
    """
    try:
        with open(price_file_path, 'r') as file:
            price_data = json.load(file)
        
        # Create price mapping
        token_prices = {}
        for token in price_data:
            address = token['address'].lower()
            # Use avg_quote as the price, default to 0 if not available
            try:
                price = float(token['quotes']['avg_quote'])
            except (KeyError, ValueError, TypeError):
                price = 0
            token_prices[address] = price
            
        return token_prices
    except Exception as e:
        print(f"Error loading token prices: {e}")
        return {}

def calculate_exchange_rates_v2(pools, eth_trade_amount, token_prices):
    """
    Compute the new exchange rate after a trade.
    eth_trade_amount: The trade amount in ETH (e.g., 0.05 ETH)
    token_prices: Dictionary mapping token addresses to their WETH prices
    """
    results = []
    skipped_pools = 0

    for pool in pools:
        try:
            pool_data = json.loads(pool) if isinstance(pool, str) else pool

            if pool_data['poolType'] == 'uniswap_v2_like_pool':
                pool_static_info = pool_data['poolState']['poolStaticInfo']
                if pool_static_info['dexExchange'] == 'uniswap':
                    pool_state = pool_data['poolState']['poolState']
                    
                    token0 = pool_static_info['token0'].lower()
                    token1 = pool_static_info['token1'].lower()
                    
                    # Get token prices in WETH
                    token0_price_in_eth = token_prices.get(token0, 0)
                    token1_price_in_eth = token_prices.get(token1, 0)

                    token_balance0 = float(pool_state['tokenBalance0'])/10**int(pool_static_info['token0Decimals'])
                    token_balance1 = float(pool_state['tokenBalance1'])/10**int(pool_static_info['token1Decimals'])

                    if token_balance0 > 0 and token_balance1 > 0:
                        # Calculate trade amounts based on token prices in ETH
                        if token0_price_in_eth > 0:
                            trade_amount0 = eth_trade_amount / token0_price_in_eth
                        else:
                            print(f"Token0 price is 0 for pool {pool_state['poolAddress']}")
                            trade_amount0 = 0
                            skipped_pools += 1
                            continue
                            
                        if token1_price_in_eth > 0:
                            trade_amount1 = eth_trade_amount / token1_price_in_eth
                        else:
                            print(f"Token1 price is 0 for pool {pool_state['poolAddress']}")
                            trade_amount1 = 0
                            skipped_pools += 1
                            continue

                        # Calculate rates using the ETH-equivalent amounts
                        rate_token0_to_token1 = compute_exchange_rate(token_balance0, token_balance1, trade_amount0) if trade_amount0 > 0 else None
                        rate_token1_to_token0 = compute_exchange_rate(token_balance1, token_balance0, trade_amount1) if trade_amount1 > 0 else None
                    else:
                        rate_token0_to_token1 = rate_token1_to_token0 = None
                    
                    result = {
                        'pool_id': pool_state['poolAddress'],
                        'token0_symbol': token0,
                        'token1_symbol': token1, 
                        'token0_balance': token_balance0,
                        'token1_balance': token_balance1,
                        'rate_token0_to_token1': rate_token0_to_token1,
                        'rate_token1_to_token0': rate_token1_to_token0,
                        'swap_fee': pool_static_info['swapFee']
                    }
                    results.append(result)
        except Exception as e:
            skipped_pools += 1
            continue
            
    return results, skipped_pools


def calculate_exchange_rates_v3(pools, k=1):
    """
    Compute the exchange rate using Uniswap V3's sqrtPriceX96 and tick data.
    
    This function processes Uniswap V3 pools to extract exchange rates at:
    1. Current price point (1 pair)
    2. k ticks to the left of current price (k pairs)
    3. k ticks to the right of current price (k pairs)

    In total, each pool will have exactly 2*k+1 pseudo pairs:
    - 1 pair at the current price
    - k pairs at lower prices (left ticks)
    - k pairs at higher prices (right ticks)
    
    Each pseudo pair consists of two tokens with pool and tick information in their identifiers.
    
    Args:
        pools: List of pool data from Uniswap V3
        k: Number of ticks to consider in each direction from current price
        
    Returns:
        results: List of exchange rates for all pools and their ticks
        skipped_pools: Number of pools skipped due to errors
        pair_pools: Dictionary mapping token pairs to their pool addresses
    """
    results = []
    skipped_pools = 0
    
    # Track pools by token pair - this helps identify when multiple pools exist for the same token pair
    pair_pools = {}  # Maps token pair to list of pools
    
    # ===== FIRST PASS: Group pools by token pair =====
    # This helps us determine which pools share the same token pair
    for pool in pools:
        try:
            pool_data = json.loads(pool) if isinstance(pool, str) else pool
            
            if pool_data['poolType'] == 'uniswap_v3_like_pool':
                pool_static_info = pool_data['poolState']['poolStaticInfo']
                if pool_static_info['dexExchange'] == 'uniswap':
                    pool_state = pool_data['poolState']['poolState']
                    pool_address = pool_state['poolAddress']
                    
                    # Extract token addresses
                    token0 = pool_static_info['token0'].lower()
                    token1 = pool_static_info['token1'].lower()
                    
                    # Create a unique pair identifier (sort tokens to ensure consistency)
                    # This ensures that token0_token1 and token1_token0 are treated as the same pair
                    pair_key = "_".join(sorted([token0, token1]))
                    
                    if pair_key not in pair_pools:
                        pair_pools[pair_key] = []
                    
                    # Store the pool address as the identifier
                    pair_pools[pair_key].append(pool_address)
        except Exception:
            continue
    
    # ===== SECOND PASS: Process each pool and create exchange rates =====
    for pool in pools:
        try:
            pool_data = json.loads(pool) if isinstance(pool, str) else pool
            
            if pool_data['poolType'] == 'uniswap_v3_like_pool':
                pool_static_info = pool_data['poolState']['poolStaticInfo']
                if pool_static_info['dexExchange'] == 'uniswap':
                    pool_state = pool_data['poolState']['poolState']
                    pool_address = pool_state['poolAddress']
                    
                    # Extract token addresses
                    token0 = pool_static_info['token0'].lower()
                    token1 = pool_static_info['token1'].lower()
                    
                    # Create a unique pair identifier
                    pair_key = "_".join(sorted([token0, token1]))
                    
                    # Pool-specific suffix for pool-specific nodes (not base tokens)
                    # Use last 6 characters of pool address to keep names shorter
                    pool_suffix = "_{0}".format(pool_address[-6:])
                    
                    # ===== EXTRACT CURRENT PRICE AND POOL PARAMETERS =====
                    
                    # Get current tick and tick spacing
                    current_tick = int(pool_state.get('currentTick', 0))
                    tick_spacing = int(pool_static_info.get('tickSpacing', 1))
                    
                    # Get sqrtPriceX96 for current price
                    # Uniswap V3 stores price as a square root with 96 bits of precision
                    sqrt_price_x96 = int(pool_state['sqrtPriceX96'])
                    sqrt_price = sqrt_price_x96 / (2 ** 96)  # Convert to floating point
                    current_price = sqrt_price ** 2  # Calculate actual token0 -> token1 price
                    
                    # Skip pools with zero price
                    if current_price == 0:
                        skipped_pools += 1
                        continue
                    
                    # Get swap fee (stored as parts per million)
                    swap_fee = int(pool_static_info['swapFee']) / 1e6  # Convert to percentage
                    
                    # Get liquidity
                    liquidity = int(pool_state.get('liquidity', 0))
                    
                    # Get tick bitmap (contains information about initialized ticks)
                    tick_bitmap = pool_state.get('tickBitMap', [])
                    
                    # ===== CREATE POOL-SPECIFIC TOKENS FOR CURRENT PRICE (1 PAIR) =====
                    
                    # Create pool-specific token identifiers with pool suffix and current tick
                    pool_token0 = f"{token0}{pool_suffix}_tick{current_tick}"
                    pool_token1 = f"{token1}{pool_suffix}_tick{current_tick}"
                    
                    # Create base result with current price between pool-specific tokens
                    # This is 1 of the 2*k+1 pairs (the current price pair)
                    pool_result = {
                        'pool_id': pool_address,
                        'token0_symbol': pool_token0,
                        'token1_symbol': pool_token1,
                        'rate_token0_to_token1': current_price,
                        'rate_token1_to_token0': 1 / current_price,
                        'swap_fee': swap_fee,
                        'original_token0': token0,
                        'original_token1': token1,
                        'pair_key': pair_key,
                        'pool_address': pool_address,
                        'total_pools_for_pair': len(pair_pools[pair_key]),
                        'tick': current_tick,
                        'liquidity': liquidity,
                        'is_pool_specific': True,
                        'pair_type': 'current'  # Mark as current price pair
                    }
                    results.append(pool_result)
                    
                    # Connect base tokens to pool-specific tokens with zero cost
                    # Base token0 to pool-specific token0
                    base_to_pool0 = {
                        'pool_id': pool_address,
                        'token0_symbol': token0,  # Base token without suffix
                        'token1_symbol': pool_token0,  # Pool-specific token with suffix
                        'rate_token0_to_token1': 1.0,  # No price impact
                        'rate_token1_to_token0': 1.0,
                        'swap_fee': 0,  # No fee
                        'original_token0': token0,
                        'original_token1': token0,
                        'pair_key': f"{token0}_{token0}_pool",
                        'pool_address': pool_address,
                        'total_pools_for_pair': 1,
                        'tick': current_tick,
                        'liquidity': liquidity,
                        'is_base_to_pool_connection': True
                    }
                    results.append(base_to_pool0)
                    # print('debug0: ', base_to_pool0)
                    
                    # Base token1 to pool-specific token1
                    base_to_pool1 = {
                        'pool_id': pool_address,
                        'token0_symbol': token1,  # Base token without suffix
                        'token1_symbol': pool_token1,  # Pool-specific token with suffix
                        'rate_token0_to_token1': 1.0,  # No price impact
                        'rate_token1_to_token0': 1.0,
                        'swap_fee': 0,  # No fee
                        'original_token0': token1,
                        'original_token1': token1,
                        'pair_key': f"{token1}_{token1}_pool",
                        'pool_address': pool_address,
                        'total_pools_for_pair': 1,
                        'tick': current_tick,
                        'liquidity': liquidity,
                        'is_base_to_pool_connection': True
                    }
                    results.append(base_to_pool1)
                    # print('debug1: ', base_to_pool1)
                    
                    # ===== PROCESS NEARBY TICKS =====
                    
                    # Find k initialized ticks that are nearby
                    sorted_ticks = []
                    
                    # First collect all initialized ticks with liquidity
                    for tick_entry in tick_bitmap:
                        if len(tick_entry) == 2:  # Ensure proper format
                            tick_idx = int(tick_entry[0])
                            tick_data = tick_entry[1]
                            
                            # Only consider initialized ticks with liquidity
                            # Ticks without liquidity don't affect pricing
                            if tick_data.get('initialized', False) and int(tick_data.get('liquidityGross', 0)) > 0:
                                # Calculate distance from current tick
                                distance = abs(tick_idx - current_tick)
                                sorted_ticks.append((distance, tick_idx, tick_data))
                    
                    # Sort by distance from current tick
                    sorted_ticks.sort()  # Sort by distance (first element in tuple)
                    
                    # Take k ticks to the left and k ticks to the right of current tick
                    # Left ticks represent lower prices, right ticks represent higher prices
                    left_ticks = []
                    right_ticks = []
                    
                    for _, tick_idx, tick_data in sorted_ticks:
                        if tick_idx < current_tick and len(left_ticks) < k:
                            left_ticks.append((tick_idx, tick_data))
                        elif tick_idx > current_tick and len(right_ticks) < k:
                            right_ticks.append((tick_idx, tick_data))
                    
                    # If we don't have enough initialized ticks, create synthetic ones
                    # This ensures we always have exactly k ticks in each direction
                    while len(left_ticks) < k:
                        # Create synthetic tick at regular intervals
                        synthetic_tick_idx = current_tick - (tick_spacing * (len(left_ticks) + 1))
                        synthetic_tick_data = {'liquidityGross': '0', 'initialized': True, 'synthetic': True}
                        left_ticks.append((synthetic_tick_idx, synthetic_tick_data))
                    
                    while len(right_ticks) < k:
                        # Create synthetic tick at regular intervals
                        synthetic_tick_idx = current_tick + (tick_spacing * (len(right_ticks) + 1))
                        synthetic_tick_data = {'liquidityGross': '0', 'initialized': True, 'synthetic': True}
                        right_ticks.append((synthetic_tick_idx, synthetic_tick_data))
                    
                    # ===== CREATE TICK NODES AND CONNECTIONS (2*k PAIRS) =====
                    
                    # Process each direction (left = lower prices, right = higher prices)
                    for direction, ticks in [("left", left_ticks), ("right", right_ticks)]:
                        for i, (tick_idx, tick_data) in enumerate(ticks):
                            # Create unique identifiers for tick-based nodes
                            # Include both pool address and tick to ensure uniqueness across pools
                            tick_token0 = f"{token0}{pool_suffix}_tick{tick_idx}"
                            tick_token1 = f"{token1}{pool_suffix}_tick{tick_idx}"
                            
                            # Calculate price at this tick using the Uniswap V3 formula
                            # Price = 1.0001^tick
                            tick_sqrt_price = 1.0001 ** (tick_idx / 2)
                            tick_price = tick_sqrt_price ** 2
                            
                            # Get liquidity at this tick
                            tick_liquidity = int(tick_data.get('liquidityGross', 0))
                            
                            # Create result for this tick with actual price
                            # This represents the exchange rate at this specific tick
                            # This is one of the 2*k pairs (either left or right tick pair)
                            tick_result = {
                                'pool_id': pool_address,
                                'token0_symbol': tick_token0,
                                'token1_symbol': tick_token1,
                                'rate_token0_to_token1': tick_price,
                                'rate_token1_to_token0': 1 / tick_price,
                                'swap_fee': swap_fee,
                                'original_token0': token0,
                                'original_token1': token1,
                                'pair_key': pair_key,
                                'pool_address': pool_address,
                                'total_pools_for_pair': len(pair_pools[pair_key]),
                                'tick': tick_idx,
                                'liquidity': tick_liquidity,
                                'is_tick_node': True,
                                'pair_type': 'left' if direction == 'left' else 'right',  # Mark as left or right tick pair
                                'synthetic': tick_data.get('synthetic', False)  # Mark if this is a synthetic tick
                            }
                            results.append(tick_result)
                            # print('debug2: ', tick_result)
                            
                    
        except Exception as e:
            skipped_pools += 1
            continue
    
    return results, skipped_pools

def create_token_graph(exchange_rates, filtered_v2_pools, token_prices=None, default_price=1.0):
    """
    Create a token exchange graph with weighted edges and directional constraints
    
    This function builds a directed graph where:
    - Nodes represent tokens (both base tokens and their pseudo variants)
    - Edges represent possible exchanges between tokens with directional constraints:
      (1) For left tick: only have edges towards left (b-b1-a1-a), all edges unidirectional
      (2) For right tick: only have edges towards right (a-a1-b1-b), all edges unidirectional
      (3) For current tick: have edges toward both directions
    - For V2 pools, pseudo nodes have a _v2 suffix
    
    Args:
        exchange_rates: List of exchange rate dictionaries from calculate_exchange_rates_v3
        filtered_v2_pools: List of V2 pool data to add to the graph
        token_prices: Optional dictionary mapping token addresses to their ETH prices
        default_price: Default price to use if token price is not found
    Returns:
        G: NetworkX DiGraph representing the token exchange network
    """
    G = nx.DiGraph()
    log_base = math.e  # Using natural logarithm for edge weights
    
    # Track tokens and their pseudo nodes
    base_tokens = {}  # Maps original token to its base node (without suffix)
    token_pseudo_nodes = {}  # Maps original token to list of its pseudo nodes
    node_pair_types = {}  # Maps node to its pair_type (left, right, current)
    
    # ===== FIRST PASS: Identify base tokens and their pseudo nodes from V3 pools =====
    for rate in exchange_rates:
        token0 = rate['original_token0']
        token1 = rate['original_token1']
        token0_node = rate['token0_symbol']
        token1_node = rate['token1_symbol']
        pair_type = rate.get('pair_type', 'current')
        
        # Track all pseudo nodes for each base token
        if token0 not in token_pseudo_nodes:
            token_pseudo_nodes[token0] = set()
        if token1 not in token_pseudo_nodes:
            token_pseudo_nodes[token1] = set()
        
        # Add pseudo nodes to their respective base tokens
        if '_' in token0_node:  # This is a pseudo node
            token_pseudo_nodes[token0].add(token0_node)
            node_pair_types[token0_node] = pair_type

        if '_' in token1_node:  # This is a pseudo node
            token_pseudo_nodes[token1].add(token1_node)
            node_pair_types[token1_node] = pair_type
        # Identify base tokens (those without any suffix)
        if token0 not in base_tokens and '_' not in token0_node:
            base_tokens[token0] = token0_node
        if token1 not in base_tokens and '_' not in token1_node:
            base_tokens[token1] = token1_node
    
    # ===== SECOND PASS: Add nodes to the graph =====
    # Add all base tokens and pseudo nodes to the graph
    for token, node in base_tokens.items():
        G.add_node(node, is_base=True, token=token)
    
    for token, pseudo_nodes in token_pseudo_nodes.items():
        for node in pseudo_nodes:
            # Extract tick information from the node name
            tick = None
            if '_tick' in node:
                try:
                    tick_part = node.split('_tick')[1].split('_')[0]
                    tick = int(tick_part)
                except (IndexError, ValueError):
                    pass
            
            pair_type = node_pair_types.get(node, 'current')
            G.add_node(node, is_pseudo=True, token=token, tick=tick, pair_type=pair_type)
    
    # ===== THIRD PASS: Connect base tokens to their pseudo nodes =====
    # Connect each base token to all its pseudo nodes with directional constraints
    for token, base_node in base_tokens.items():
        if token in token_pseudo_nodes:
            for pseudo_node in token_pseudo_nodes[token]:
                pair_type = node_pair_types.get(pseudo_node, 'current')
                
                # Apply directional constraints based on pair_type
                if pair_type == 'current':
                    # Current tick: bidirectional connection
                    G.add_edge(base_node, pseudo_node, weight=0, pair_type='current')
                    G.add_edge(pseudo_node, base_node, weight=0, pair_type='current')
                    
                elif pair_type == 'left':
                    # Left tick: only allow movement from pseudo to base (towards left)
                    G.add_edge(pseudo_node, base_node, weight=0, pair_type='left')
                    
                elif pair_type == 'right':
                    # Right tick: only allow movement from base to pseudo (towards right)
                    G.add_edge(base_node, pseudo_node, weight=0, pair_type='right')
    
    # ===== FOURTH PASS: Connect pseudo nodes based on exchange rates and directional constraints =====
    for rate in exchange_rates:
        token0_node = rate['token0_symbol']
        token1_node = rate['token1_symbol']
        pair_type = rate.get('pair_type', 'current')  # Default to 'current' if not specified
        
        # Skip entries with missing rates
        if rate['rate_token0_to_token1'] is None or rate['rate_token1_to_token0'] is None:
            continue
        
        try:
            # Apply swap fee multiplier
            lambda_tax = float(rate['swap_fee'])
            
            # Calculate exchange rates with fees
            rate0to1 = (1 - lambda_tax) * rate['rate_token0_to_token1']
            rate1to0 = (1 - lambda_tax) * rate['rate_token1_to_token0']
            
            # Skip invalid rates
            if rate0to1 <= 0 or rate1to0 <= 0:
                continue
            
            # Calculate edge weights as negative log of exchange rate
            weight0to1 = -math.log(rate0to1, log_base)
            weight1to0 = -math.log(rate1to0, log_base)
            
            # Add nodes to the graph if they don't exist yet
            if token0_node not in G:
                G.add_node(token0_node)
            if token1_node not in G:
                G.add_node(token1_node)
            
            # ===== HANDLE DIFFERENT CONNECTION TYPES WITH DIRECTIONAL CONSTRAINTS =====
            
            # Case 1: Connection between pseudo nodes of different tokens at the same tick
            if ('_tick' in token0_node and '_tick' in token1_node and 
                rate['original_token0'] != rate['original_token1']):
                
                # Extract tick information to verify they're at the same tick
                try:
                    tick0 = int(token0_node.split('_tick')[1].split('_')[0])
                    tick1 = int(token1_node.split('_tick')[1].split('_')[0])
                    
                    if tick0 == tick1:  # Same tick, different tokens
                        # Apply directional constraints based on pair_type
                        if pair_type == 'current':
                            # Current tick: allow both directions
                            G.add_edge(token0_node, token1_node, 
                                      weight=weight0to1, 
                                      rate=rate0to1, 
                                      tick=tick0,
                                      pool_id=rate['pool_id'],
                                      pair_type='current')
                            
                            G.add_edge(token1_node, token0_node, 
                                      weight=weight1to0, 
                                      rate=rate1to0,
                                      tick=tick0,
                                      pool_id=rate['pool_id'],
                                      pair_type='current')
                            
                        elif pair_type == 'left':
                            # Left tick: only allow movement from token1 to token0 (b-b1-a1-a)
                            G.add_edge(token1_node, token0_node, 
                                      weight=weight1to0, 
                                      rate=rate1to0,
                                      tick=tick0,
                                      pool_id=rate['pool_id'],
                                      pair_type='left')
                            
                        elif pair_type == 'right':
                            # Right tick: only allow movement from token0 to token1 (a-a1-b1-b)
                            G.add_edge(token0_node, token1_node, 
                                      weight=weight0to1, 
                                      rate=rate0to1,
                                      tick=tick0,
                                      pool_id=rate['pool_id'],
                                      pair_type='right')
                except (IndexError, ValueError):
                    # If we can't parse the tick, fall back to default behavior
                    if pair_type == 'current':
                        G.add_edge(token0_node, token1_node, weight=weight0to1, pair_type='current')
                        G.add_edge(token1_node, token0_node, weight=weight1to0, pair_type='current')
                    elif pair_type == 'left':
                        G.add_edge(token1_node, token0_node, weight=weight1to0, pair_type='left')
                    elif pair_type == 'right':
                        G.add_edge(token0_node, token1_node, weight=weight0to1, pair_type='right')
            
            # Case 2: Connection between pseudo nodes of the same token at different ticks
            # These represent moving along the price curve - we skip these as they're handled by base-to-pseudo connections
            elif ('_tick' in token0_node and '_tick' in token1_node and 
                  rate['original_token0'] == rate['original_token1']):
                continue
            
            # Case 3: Connection between variant tokens of different tokens
            # These represent swaps in pools with multiple fee tiers
            elif ('_' in token0_node and '_' in token1_node and 
                  rate.get('is_variant_connection', False)):
                
                # Apply directional constraints based on pair_type
                if pair_type == 'current':
                    G.add_edge(token0_node, token1_node, 
                              weight=weight0to1,
                              rate=rate0to1,
                              pool_id=rate['pool_id'],
                              pair_type='current')
                    
                    G.add_edge(token1_node, token0_node, 
                              weight=weight1to0,
                              rate=rate1to0,
                              pool_id=rate['pool_id'],
                              pair_type='current')
                    
                elif pair_type == 'left':
                    G.add_edge(token1_node, token0_node, 
                              weight=weight1to0,
                              rate=rate1to0,
                              pool_id=rate['pool_id'],
                              pair_type='left')
                    
                elif pair_type == 'right':
                    G.add_edge(token0_node, token1_node, 
                              weight=weight0to1,
                              rate=rate0to1,
                              pool_id=rate['pool_id'],
                              pair_type='right')
            
            # Case 4: Any other connection between pseudo nodes
            # Use the specified rates with directional constraints
            elif '_' in token0_node and '_' in token1_node:
                if pair_type == 'current':
                    G.add_edge(token0_node, token1_node, weight=weight0to1, pair_type='current')
                    G.add_edge(token1_node, token0_node, weight=weight1to0, pair_type='current')
                elif pair_type == 'left':
                    G.add_edge(token1_node, token0_node, weight=weight1to0, pair_type='left')
                elif pair_type == 'right':
                    G.add_edge(token0_node, token1_node, weight=weight0to1, pair_type='right')
                
        except (ValueError, TypeError, ZeroDivisionError) as e:
            continue
    
    # ===== FIFTH PASS: Add V2 pools to the graph =====
    # Process V2 pools and add them to the graph with _v2 suffix
    for pool in filtered_v2_pools:
        try:
            token0 = pool['token0_symbol'].lower()
            token1 = pool['token1_symbol'].lower()
            
            # Skip entries with missing rates
            if pool['rate_token0_to_token1'] is None or pool['rate_token1_to_token0'] is None:
                continue
            
            # Create pseudo nodes for V2 pools
            token0_v2 = f"{token0}_v2"
            token1_v2 = f"{token1}_v2"
            
            # Apply swap fee
            lambda_tax = float(pool['swap_fee']) / 1e6  # Convert from ppm to decimal
            
            # Calculate exchange rates with fees
            rate0to1 = (1 - lambda_tax) * pool['rate_token0_to_token1']
            rate1to0 = (1 - lambda_tax) * pool['rate_token1_to_token0']
            
            # Skip invalid rates
            if rate0to1 <= 0 or rate1to0 <= 0:
                continue
            
            # Calculate edge weights
            weight0to1 = -math.log(rate0to1, log_base)
            weight1to0 = -math.log(rate1to0, log_base)
            
            # Add base tokens if they don't exist
            if token0 not in base_tokens:
                base_tokens[token0] = token0
                G.add_node(token0, is_base=True, token=token0)
            
            if token1 not in base_tokens:
                base_tokens[token1] = token1
                G.add_node(token1, is_base=True, token=token1)
            
            # Add V2 pseudo nodes
            G.add_node(token0_v2, is_pseudo=True, token=token0, is_v2=True)
            G.add_node(token1_v2, is_pseudo=True, token=token1, is_v2=True)
            
            # Connect base tokens to their V2 pseudo nodes (bidirectional with zero weight)
            G.add_edge(token0, token0_v2, weight=0, pair_type='v2')
            G.add_edge(token0_v2, token0, weight=0, pair_type='v2')
            G.add_edge(token1, token1_v2, weight=0, pair_type='v2')
            G.add_edge(token1_v2, token1, weight=0, pair_type='v2')
            
            # Connect V2 pseudo nodes to each other (bidirectional with calculated weights)
            G.add_edge(token0_v2, token1_v2, 
                      weight=weight0to1, 
                      rate=rate0to1, 
                      pool_id=pool['pool_id'],
                      pair_type='v2')
            
            G.add_edge(token1_v2, token0_v2, 
                      weight=weight1to0, 
                      rate=rate1to0,
                      pool_id=pool['pool_id'],
                      pair_type='v2')
            
        except (ValueError, TypeError, ZeroDivisionError, KeyError) as e:
            continue
    
    return G

def print_graph(token_graph, path):
    """Save the graph to files with node mapping"""
    # Ensure output directory exists
    os.makedirs(os.path.dirname(path) if os.path.dirname(path) else '.', exist_ok=True)
    
    # Sort nodes to ensure consistent node IDs
    sorted_nodes = sorted(token_graph.nodes())
    node_map = {node: idx for idx, node in enumerate(sorted_nodes)}
    indexed_graph = nx.DiGraph()

    for edge in token_graph.edges(data=True):
        source, target, data = edge
        indexed_graph.add_edge(node_map[source], node_map[target], 
                              weight=data['weight'])

    graph_file = '{}_token_graph.txt'.format(path)
    map_file = '{}_node_map.txt'.format(path)
    
    with open(graph_file, 'w') as f:
        for edge in indexed_graph.edges(data=True):
            f.write("{} {} {}\n".format(edge[0], edge[1], edge[2]['weight']))

    with open(map_file, 'w') as f:
        for node, idx in node_map.items():
            f.write("{} {}\n".format(idx, node))

    return graph_file, map_file
    
def load_ranked_pools(ranked_pools_file, top_k=None):
    """
    Load ranked pools from a text file and return the top k pools
    
    Args:
        ranked_pools_file: Path to the text file with ranked pools
        top_k: Number of top pools to return (None for all)
    
    Returns:
        Set of pool addresses (lowercase)
    """
    pools = []
    try:
        with open(ranked_pools_file, 'r') as file:
            for line in file:
                parts = line.strip().split()
                if len(parts) >= 2:
                    pool_address = parts[0].lower()
                    liquidity = float(parts[1])
                    pools.append((pool_address, liquidity))
    except Exception as e:
        print("Error loading ranked pools: {}".format(e))
        return set()
    
    # Sort by liquidity (descending)
    pools.sort(key=lambda x: x[1], reverse=True)
    
    # Take top k if specified
    if top_k is not None:
        pools = pools[:top_k]
    
    # Return set of pool addresses
    return set(pool[0] for pool in pools)

def main():
    """Main function to process pool data and create token graph"""
    try:
        block_number = 21974203
        # File paths
        v2_file_path = f'../data/prices/block-{block_number}/filtered-v2pools.json'
        v3_file_path = '../data/prices/block-{}/filtered-v3pools.json'.format(block_number)
        ranked_pools_file = '../data/prices/pruned_pools/{}_ranked_pool_addresses.txt'.format(block_number)
        price_file_path = f'../data/prices/block-{block_number}/tokens-quotes.json'
       
        # Parameters
        top_k = 300  # Number of top pools to use
        
        # Load token prices
        token_prices = load_token_prices(price_file_path)
        print(f"Loaded prices for {len(token_prices)} tokens")
        
        # Load top k ranked pools
        filtered_v3_pools = load_ranked_pools(ranked_pools_file, top_k)
        print("Loaded {} top pools from ranked pools file".format(len(filtered_v3_pools)))

        eth_trade_amount = 100  # Trade amount in ETH
        
        # Load and process v2 pools with token prices
        v2_data = load_data_from_json(v2_file_path)
        v2_pools, v2_skipped = calculate_exchange_rates_v2(v2_data, eth_trade_amount, token_prices)
        
        # Load and process v3 pools with filtering
        v3_data = load_data_from_json(v3_file_path, filtered_v3_pools)
        v3_pools, v3_skipped = calculate_exchange_rates_v3(v3_data)
        
        # Filter exchange rates to only include pools in the filtered_pools set
        filtered_v3_pools_data = []
        for rate in v3_pools:
            if rate['pool_address'].lower() in filtered_v3_pools:
                filtered_v3_pools_data.append(rate)
        
        print(f"Filtered from {len(v3_pools)} to {len(filtered_v3_pools_data)} exchange rates based on top {top_k} pools")
        
        # Filter v2 pools to only include those with the same token pairs as in filtered v3 pools
        v3_token_pairs = set()
        for rate in filtered_v3_pools_data:
            token0 = rate['original_token0'].lower()
            token1 = rate['original_token1'].lower()
            v3_token_pairs.add((token0, token1))
            v3_token_pairs.add((token1, token0))  # Add both directions
        
        filtered_v2_pools = []
        for pool in v2_pools:
            token0 = pool['token0_symbol'].lower()
            token1 = pool['token1_symbol'].lower()
            if (token0, token1) in v3_token_pairs or (token1, token0) in v3_token_pairs:
                filtered_v2_pools.append(pool)
        
        print(f"Filtered from {len(v2_pools)} to {len(filtered_v2_pools)} v2 pools based on token pairs in filtered v3 pools")
        
        # Create graph using the filtered pools and token prices for capacity calculation
        graph = create_token_graph(filtered_v3_pools_data, filtered_v2_pools, token_prices)
        nodes = len(graph.nodes())
        edges = len(graph.edges())
        
        # Create output directory
        os.makedirs('graphs', exist_ok=True)
        
        graph_file, map_file = print_graph(graph, 'graphs/{}_v3v2_top{}'.format(block_number, top_k))
        
        # Final summary output
        print("Processed {} v3 pools, skipped {}".format(len(filtered_v3_pools_data), v3_skipped))
        print("Processed {} v2 pools, skipped {}".format(len(filtered_v2_pools), v2_skipped))
        print("Created graph with {} nodes and {} edges".format(nodes, edges))
        print("Saved output to {} and {}".format(graph_file, map_file))
        
    except Exception as e:
        print("Error: {}".format(e))
        traceback.print_exc()

if __name__ == "__main__":
    main()
