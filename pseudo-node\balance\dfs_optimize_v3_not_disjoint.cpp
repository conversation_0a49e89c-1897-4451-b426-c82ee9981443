#include <vector>
#include <iostream>
#include <fstream>
#include <algorithm>
#include <set>
#include <limits>
#include <numeric>
#include <chrono>  // Add this for timing
#include <iomanip> // Add this for output formatting
#include <functional>
#include <map>

class PathEnumerator {
public:
    std::vector<std::vector<int>> all_paths;
    std::vector<double> path_costs;
    std::vector<size_t> valid_indices;
    // Add timing variables as class members
    std::chrono::milliseconds enum_duration{0};
    std::chrono::milliseconds selection_duration{0}; // Renamed from greedy_duration
    
    // 添加一个映射来存储等价节点关系
    std::map<std::pair<int, int>, bool> equivalent_nodes;

private:
    std::vector<std::vector<std::pair<int, double>>> adj;
    std::vector<bool> visited;
    std::vector<int> current_path;
    double current_path_cost;
    int max_length;
    std::vector<std::vector<int>> top_paths; // Renamed from greedy_paths
    std::vector<double> top_path_costs;      // Renamed from greedy_path_costs

    void dfs(int current, int target, int length) {
        if (length > max_length) return;
        
        current_path.push_back(current);
        visited[current] = true;

        if (current == target) {
            all_paths.push_back(current_path);
            path_costs.push_back(current_path_cost);
        } else {
            for (const auto& edge : adj[current]) {
                int next = edge.first;
                double weight = edge.second;
                if (!visited[next]) {
                    double prev_cost = current_path_cost;
                    current_path_cost += weight;
                    
                    // Only increment length if the edge weight is not 0
                    int new_length = length + (weight != 0 ? 1 : 0);
                    dfs(next, target, new_length);
                    
                    current_path_cost = prev_cost;
                }
            }
        }

        visited[current] = false;
        current_path.pop_back();
    }

    // This method is no longer needed since we don't require disjoint paths
    // bool areDisjoint(const std::vector<int>& path1, const std::vector<int>& path2) { ... }

    // Find k paths with most negative weight (lowest weight)
    std::pair<std::vector<std::vector<int>>, std::vector<double>> findTopKPaths(int k) {
        // Sort paths by weight (ascending order - most negative first)
        std::vector<size_t> indices(path_costs.size());
        std::iota(indices.begin(), indices.end(), 0);
        std::sort(indices.begin(), indices.end(),
                 [&](size_t i, size_t j) { return path_costs[i] < path_costs[j]; });
        
        // Select top k paths with lowest weights
        std::vector<std::vector<int>> result;
        std::vector<double> result_costs;
        
        for (size_t i = 0; i < indices.size() && result.size() < k; ++i) {
            result.push_back(all_paths[indices[i]]);
            result_costs.push_back(path_costs[indices[i]]);
        }

        return {result, result_costs};
    }

public:
    PathEnumerator(int n) : adj(n), visited(n, false), current_path_cost(0.0) {}

    void addEdge(int u, int v, double weight) {
        adj[u].push_back({v, weight});
    }

    std::pair<std::vector<std::vector<int>>, std::vector<double>> findAllPaths(int source, int target, int maxLen, int k) {
        auto enum_start = std::chrono::high_resolution_clock::now();
        
        max_length = maxLen;
        all_paths.clear();
        path_costs.clear();
        valid_indices.clear();
        current_path.clear();
        current_path_cost = 0.0;
        std::fill(visited.begin(), visited.end(), false);
        
        dfs(source, target, 0);
        
        auto enum_end = std::chrono::high_resolution_clock::now();
        enum_duration = std::chrono::duration_cast<std::chrono::milliseconds>(enum_end - enum_start);
        std::cout << "Time for enumerating all paths: " << enum_duration.count() << " ms\n";
        std::cout << "Total number of paths enumerated: " << all_paths.size() << "\n";

        auto selection_start = std::chrono::high_resolution_clock::now();
        
        // Find top k paths with most negative weight
        auto result = findTopKPaths(k);
        top_paths = result.first;
        top_path_costs = result.second;

        auto selection_end = std::chrono::high_resolution_clock::now();
        selection_duration = std::chrono::duration_cast<std::chrono::milliseconds>(selection_end - selection_start);
        std::cout << "Time for finding top " << k << " paths: " << selection_duration.count() << " ms\n";
        
        return result;
    }

    std::pair<std::vector<std::vector<int>>, std::vector<double>> getTopPaths() const {
        return {top_paths, top_path_costs};
    }

    // Add getter methods for durations if needed
    auto getEnumDuration() const { return enum_duration; }
    auto getSelectionDuration() const { return selection_duration; }

    // 添加一个方法来设置等价节点
    void addEquivalentNodes(int node1, int node2) {
        equivalent_nodes[{node1, node2}] = true;
    }
    
    // 从文件读取等价节点关系
    void readEquivalentNodesFromFile(const std::string& filename) {
        std::ifstream file(filename);
        if (!file.is_open()) {
            std::cerr << "Could not open equivalent nodes file: " << filename << std::endl;
            return;
        }
        
        int node1, node2;
        while (file >> node1 >> node2) {
            addEquivalentNodes(node1, node2);
        }
        
        file.close();
    }
};

int main() {
    // Remove the overall timing from here since we now have separate timings
    
    std::string filename;
    std::cout << "Enter the graph file name: ";
    std::cin >> filename;

    // First pass: count number of vertices
    std::ifstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Could not open file: " << filename << std::endl;
        return 1;
    }

    int max_vertex = -1;
    int from, to;
    double weight;
    while (file >> from >> to >> weight) {
        max_vertex = std::max(max_vertex, std::max(from, to));
    }
    file.close();

    // Create graph with appropriate size (add 1 because vertices are 0-based)
    PathEnumerator pe(max_vertex + 1);

    // Second pass: read edges
    file.open(filename);
    while (file >> from >> to >> weight) {
        pe.addEdge(from, to, weight);
    }
    file.close();

    // Get source and target vertices from user
    int source, target, max_length;
    std::cout << "Enter source vertex: ";
    std::cin >> source;
    std::cout << "Enter target vertex: ";
    std::cin >> target;
    std::cout << "Enter maximum path length: ";
    std::cin >> max_length;

    // Get additional parameter k
    int k;
    std::cout << "Enter number of paths (k): ";
    std::cin >> k;

    // 询问用户是否有等价节点文件
    std::string eq_filename;
    std::cout << "Enter equivalent nodes file name (or 'none' if none): ";
    std::cin >> eq_filename;
    
    if (eq_filename != "none") {
        pe.readEquivalentNodesFromFile(eq_filename);
    }

    // Find k paths with most negative weight
    std::pair<std::vector<std::vector<int>>, std::vector<double>> result = pe.findAllPaths(source, target, max_length, k);
    std::vector<std::vector<int>>& paths = result.first;
    std::vector<double>& costs = result.second;

    // Create output file name
    std::string outfile = "results/top_" + std::to_string(source) + "_" + 
                         std::to_string(target) + "_" + 
                         std::to_string(max_length) + "_" +
                         std::to_string(k) + ".txt";
    std::ofstream output(outfile);
    if (!output.is_open()) {
        std::cerr << "Could not create output file: " << outfile << std::endl;
        return 1;
    }

    // Write results with separate timings
    output << "Timing breakdown:\n";
    output << "Path enumeration time: " << pe.getEnumDuration().count() << " milliseconds\n";
    output << "Path selection time: " << pe.getSelectionDuration().count() << " milliseconds\n\n";
    
    // Add path counts information
    output << "Total paths enumerated: " << pe.all_paths.size() << "\n\n";
    
    // Print top k paths
    output << "Top " << paths.size() << " paths with most negative weight:\n\n";
    for (size_t i = 0; i < paths.size(); ++i) {
        output << "Path " << (i + 1) << " (total path weight: " << std::fixed << std::setprecision(6) << costs[i] << "):\n";
        for (int vertex : paths[i]) {
            output << vertex << " ";
        }
        output << "\n\n";
    }

    output.close();
    std::cout << "Results have been saved to: " << outfile << std::endl;

    return 0;
}