import pandas as pd
import json
import re
from typing import List, Dict, Tuple
import os

def extract_paths_from_results(file_path):
    """Extract paths from disjoint path results file."""
    with open(file_path, 'r') as f:
        text = f.read()
    
    greedy_paths = []
    greedy_weights = []
    
    greedy_section = re.search(r"Greedy solution found \d+ disjoint paths:(.*?)Maximum of path costs in greedy solution:", text, re.DOTALL)
    
    if greedy_section:
        path_blocks = re.findall(r"Path \d+ \(total path weight: ([-\d\.]+)\):\s*([\d\s]+)", greedy_section.group(1))
        
        for weight, path in path_blocks:
            greedy_paths.append(path.strip())
            greedy_weights.append(float(weight))
    
    optimal_paths = []
    optimal_weights = []
    
    optimal_section = re.search(r"Final optimal solution found \d+ disjoint paths:(.*?)Maximum of path costs in optimal solution:", text, re.DOTALL)
    
    if optimal_section and "found 0 disjoint paths" not in optimal_section.group(0):
        path_blocks = re.findall(r"Path \d+ \(total path weight: ([-\d\.]+)\):\s*([\d\s]+)", optimal_section.group(1))
        
        for weight, path in path_blocks:
            optimal_paths.append(path.strip())
            optimal_weights.append(float(weight))
    
    return {
        'greedy_paths': greedy_paths,
        'greedy_weights': greedy_weights,
        'optimal_paths': optimal_paths,
        'optimal_weights': optimal_weights
    }

def read_path_enumeration_results(file_path):
    """Extract paths from non-disjoint path file."""
    with open(file_path, 'r') as f:
        content = f.read()
    
    paths = []
    weights = []
    
    path_blocks = re.findall(r"Path \d+ \(total path weight: ([-\d\.]+)\):\s*([\d\s]+)", content)
    
    for weight, path in path_blocks:
        paths.append(path.strip())
        weights.append(float(weight))
    
    return {
        'paths': paths,
        'weights': weights
    }

def extract_pools_from_path(token_path, pools_data, pools_data_v2):
    """Extract pools and their types from a token path."""
    used_pools = []
    clean_path = []
    pool_types = []
    pool_addresses = []
    used_tick_ids = []
    
    for token in token_path:
        if '_' in token:
            continue
        else:
            clean_path.append(token)

    for i in range(len(token_path) - 1):
        current_token = token_path[i]
        next_token = token_path[i + 1]
        
        current_clean = current_token.split('_')[0] if '_' in current_token else current_token
        next_clean = next_token.split('_')[0] if '_' in next_token else next_token
        
        current_pool_id = None
        next_pool_id = None

        # Check for V2 pools
        if '_v2' in current_token and '_v2' in next_token:
            matching_pools = [
                p for p in pools_data_v2
                if (
                    (p["poolState"]["poolStaticInfo"]["token0"].lower() == current_clean.lower() and 
                     p["poolState"]["poolStaticInfo"]["token1"].lower() == next_clean.lower()) or
                    (p["poolState"]["poolStaticInfo"]["token0"].lower() == next_clean.lower() and 
                     p["poolState"]["poolStaticInfo"]["token1"].lower() == current_clean.lower())
                )
            ]
            
            if matching_pools:
                used_pools.append(matching_pools[0])
                pool_types.append('v2')
                pool_addresses.append(matching_pools[0]["poolAddress"].lower())
                used_tick_ids.append('v2')
                continue
        
        # Extract pool IDs if present
        if '_' in current_token:
            try:
                _, current_pool_id, tick = current_token.split('_')[0], current_token.split('_')[1], current_token.split('_')[2]
                tick_id = current_pool_id[-6:]+tick
                # tick_id = current_token.split('_')[2]
                
                if current_pool_id.startswith('tick'):
                    current_pool_id = None
            except:
                pass
                

        if '_' in next_token:
            try:
                _, next_pool_id, tick = next_token.split('_')[0], next_token.split('_')[1], next_token.split('_')[2]
                tick_id = next_pool_id[-6:]+tick
                # tick_id = next_token.split('_')[2]
                
                if next_pool_id.startswith('tick'):
                    next_pool_id = None
            except:
                pass
               

        # Check for V3 pools with matching pool IDs
        if current_pool_id and next_pool_id and current_pool_id == next_pool_id:
            pool = next(
                (
                    p for p in pools_data
                    if (
                        (p["poolState"]["poolStaticInfo"]["token0"].lower() == current_clean.lower() and 
                         p["poolState"]["poolStaticInfo"]["token1"].lower() == next_clean.lower()) or
                        (p["poolState"]["poolStaticInfo"]["token0"].lower() == next_clean.lower() and 
                         p["poolState"]["poolStaticInfo"]["token1"].lower() == current_clean.lower())
                    ) and current_pool_id in p["poolState"]["poolStaticInfo"]["poolAddress"].lower()
                ),
                None
            )
            if pool:
                used_pools.append(pool)
                pool_types.append('v3')
                used_tick_ids.append(tick_id)
                pool_addresses.append(pool["poolState"]["poolStaticInfo"]["poolAddress"].lower())
        
        elif current_pool_id or next_pool_id:
            pool_id = current_pool_id or next_pool_id
            pool = next(
                (
                    p for p in pools_data
                    if (
                        (p["poolState"]["poolStaticInfo"]["token0"].lower() == current_clean.lower() and 
                         p["poolState"]["poolStaticInfo"]["token1"].lower() == next_clean.lower()) or
                        (p["poolState"]["poolStaticInfo"]["token0"].lower() == next_clean.lower() and 
                         p["poolState"]["poolStaticInfo"]["token1"].lower() == current_clean.lower())
                    ) and pool_id in p["poolState"]["poolStaticInfo"]["poolAddress"].lower()
                ),
                None
            )
            if pool:
                used_pools.append(pool)
                pool_types.append('v3')
                used_tick_ids.append(tick_id)
                pool_addresses.append(pool["poolState"]["poolStaticInfo"]["poolAddress"].lower())
        else:
            # Check for any matching V3 pools
            matching_pools = [
                p for p in pools_data
                if (
                    (p["poolState"]["poolStaticInfo"]["token0"].lower() == current_clean.lower() and 
                     p["poolState"]["poolStaticInfo"]["token1"].lower() == next_clean.lower()) or
                    (p["poolState"]["poolStaticInfo"]["token0"].lower() == next_clean.lower() and 
                     p["poolState"]["poolStaticInfo"]["token1"].lower() == current_clean.lower())
                )
            ]
            
            if matching_pools:
                pool_suffix = None
                if '_' in token_path[i]:
                    _, pool_suffix = token_path[i].split('_')[0], token_path[i].split('_')[1]
                elif i+1 < len(token_path) and '_' in token_path[i+1]:
                    _, pool_suffix = token_path[i+1].split('_')[0], token_path[i+1].split('_')[1]
                
                if pool_suffix:
                    matching_pool = next(
                        (p for p in matching_pools 
                         if p["poolState"]["poolStaticInfo"]["poolAddress"].lower().endswith(pool_suffix.lower())),
                        None
                    )
                    if matching_pool:
                        used_pools.append(matching_pool)
                        pool_types.append('v3')
                        pool_addresses.append(matching_pool["poolState"]["poolStaticInfo"]["poolAddress"].lower())
                        continue
                
                used_pools.append(matching_pools[0])
                pool_types.append('v3')
                used_tick_ids.append(tick_id)
                pool_addresses.append(matching_pools[0]["poolState"]["poolStaticInfo"]["poolAddress"].lower())
    
    return clean_path, used_pools, pool_types, pool_addresses, used_tick_ids

def get_min_balance(pool_addresses, balance_dict):
    """Get the minimum balance for a list of pool addresses."""
    min_balance = float('inf')
    
    for pool_address in pool_addresses:
        if pool_address in balance_dict:
            min_balance = min(min_balance, balance_dict[pool_address])
        else:
            # If any pool is not found in balance_dict, set to 0
            return 0
    
    # If no pools or min_balance is still infinity, return 0
    if min_balance == float('inf'):
        return 0
    
    return min_balance

def analyze_paths(
    path_file: str,
    node_map_file: str,
    v3_pools_file: str,
    v2_pools_file: str,
    #balance_file: str,
    is_disjoint: bool = True,
    num_paths: int = None
) -> pd.DataFrame:
    """
    Analyze paths and return a DataFrame with path information.
    
    Args:
        path_file: Path to the file containing path results
        node_map_file: Path to the node map file
        v3_pools_file: Path to the V3 pools data file
        v2_pools_file: Path to the V2 pools data file
        balance_file: Path to the balance file
        is_disjoint: Whether the paths are disjoint
        num_paths: Number of paths to analyze (None for all)
    
    Returns:
        DataFrame with path information
    """
    # Read node map
    with open(node_map_file, 'r') as f:
        node_map = {}
        for line in f:
            index, token = line.strip().split()[0], line.strip().split()[1]
            node_map[int(index)] = token
    
    # Read pools data
    with open(v3_pools_file, "r") as file:
        pools_data = json.load(file)
    
    with open(v2_pools_file, "r") as file:
        pools_data_v2 = json.load(file)
    
    # Read balance data
    # with open(balance_file, 'r') as f:
    #     balance = f.read().splitlines()
    
    # balance_dict = {}
    # for line in balance:
    #     parts = line.split()
    #     if len(parts) >= 2:
    #         pool, balance = parts[0], parts[1]
    #         balance_dict[pool.lower()] = float(balance)
    
    # Extract paths
    if is_disjoint:
        paths_data = extract_paths_from_results(path_file)
        # Prefer optimal paths if available, otherwise use greedy paths
        if 'optimal_paths' in paths_data and len(paths_data['optimal_paths']) > 0:
            paths = paths_data['optimal_paths']
            weights = paths_data['optimal_weights']
        else:
            paths = paths_data['greedy_paths']
            weights = paths_data['greedy_weights']
    else:
        paths_data = read_path_enumeration_results(path_file)
        paths = paths_data['paths']
        weights = paths_data['weights']
    
    # Limit number of paths if specified
    if num_paths is not None:
        paths = paths[:num_paths]
        weights = weights[:num_paths]
    
    # Prepare data for DataFrame
    data = []
    
    for i, (path_str, weight) in enumerate(zip(paths, weights)):
        # Convert node IDs to token addresses
        token_path = [node_map[int(node_id)] for node_id in path_str.split()]
        
        # Extract pools and their types
        clean_path, used_pools, pool_types, pool_addresses, used_tick_ids = extract_pools_from_path(token_path, pools_data, pools_data_v2)
        
        # Get minimum balance
        # min_balance = get_min_balance(pool_addresses, balance_dict)
        
        # Add to data
        data.append({
            'path_id': i + 1,
            'path': ' -> '.join(clean_path),
            'weight': weight,
            # 'min_balance': min_balance,
            'involved_pool_addresses': pool_addresses,
            'used_tick_ids': used_tick_ids,
            'pools_type_list': pool_types
        })
    
    # Create DataFrame
    df = pd.DataFrame(data)
    
    return df

def main():
    # Example usage
    block_number = 21974203
    # graph_name = 'v3v2_top100'
    k = 1000
    name = f'top_1547_512_3_{k}'
    graph_name = 'pairs_capacity_usdt_ohm'
    
    path_file = f'../../enum/results/{name}.txt'
    # balance_file = f'../../data/prices/pruned_pools/{block_number}_ranked_pool_addresses_v2v3.txt'

    v2_pools_file = f'../../data/prices/block-{block_number}/filtered-v2pools.json'
    v3_pools_file = f'../../data/prices/block-{block_number}/filtered-v3pools.json'
    # price_file_path = f'../data/prices/block-{block_number}/tokens-quotes.json'
    node_map_file = f'../../graph_construction/graphs/{block_number}_{graph_name}_node_map.txt'
    
    # Set is_disjoint based on your path file format
    is_disjoint = False
    
    # Analyze paths
    df = analyze_paths(
        path_file=path_file,
        node_map_file=node_map_file,
        v3_pools_file=v3_pools_file,
        v2_pools_file=v2_pools_file,
        # balance_file=balance_file,
        is_disjoint=is_disjoint,
        num_paths=k  # Analyze top 10 paths
    )
    
    # Save to CSV
    output_dir = '../results/path_analysis_df'
    os.makedirs(output_dir, exist_ok=True)
    output_file = f'{output_dir}/path_analysis_{name}.csv'
    df.to_csv(output_file, index=False)
    
    print(f"Path analysis saved to {output_file}")
    
    # Display summary
    print("\nPath Analysis Summary:")
    print(f"Total paths analyzed: {len(df)}")
    print(f"Average path weight: {df['weight'].mean():.4f}")
    # print(f"Average min balance: {df['min_balance'].mean():.4f}")
    
    # Count pool types
    pool_type_counts = {}
    for types in df['pools_type_list']:
        for pool_type in types:
            pool_type_counts[pool_type] = pool_type_counts.get(pool_type, 0) + 1
    
    print("\nPool type distribution:")
    for pool_type, count in pool_type_counts.items():
        print(f"  {pool_type}: {count}")
    
    return df

if __name__ == "__main__":
    main()