import json
import math
import os
from collections import defaultdict
from decimal import Decimal, getcontext

# Set high precision for financial calculations
getcontext().prec = 60

def load_v3_pool_data(file_path):
    try:
        with open(file_path, 'r') as file:
            data = json.load(file)
        
        # Filter for V3 pools only
        v3_pools = []
        for pool in data:
            try:
                pool_data = json.loads(pool) if isinstance(pool, str) else pool
                if (pool_data.get('poolType') == 'uniswap_v3_like_pool' and 
                    pool_data['poolState']['poolStaticInfo']['dexExchange'] == 'uniswap'):
                    v3_pools.append(pool_data)
            except (KeyError, json.JSONDecodeError, TypeError):
                continue
        
        return v3_pools
    except Exception as e:
        print(f"Error loading V3 pool data: {e}")
        return []

def tick_to_price(tick):
    return 1.0001 ** tick

def calculate_price_difference_percentage(price1, price2):
    if price1 == 0 or price2 == 0:
        return float('inf')
    return abs(price1 - price2) / min(price1, price2)

def should_split_tick(current_tick, next_tick, current_pool_tick, max_price_diff_pct=0.05, max_distance_from_current=10000):
    # Don't split if ticks are adjacent
    if abs(next_tick - current_tick) <= 1:
        return False
    
    # Calculate distance from current pool price
    distance_from_current = min(
        abs(current_tick - current_pool_tick),
        abs(next_tick - current_pool_tick)
    )
    
    # Don't split if too far from current price
    if distance_from_current > max_distance_from_current:
        return False
    
    # Calculate price difference
    price1 = tick_to_price(current_tick)
    price2 = tick_to_price(next_tick)
    price_diff_pct = calculate_price_difference_percentage(price1, price2)
    
    # Split if price difference exceeds threshold
    return price_diff_pct > max_price_diff_pct

def split_tick_range(start_tick, end_tick, tick_spacing, max_splits=50):

    if start_tick >= end_tick:
        return [(start_tick, end_tick)]
    
    total_range = end_tick - start_tick
    
    # Calculate how many splits would be needed with original tick_spacing
    splits_needed = math.ceil(total_range / tick_spacing)
    
    # If we can cover the range within max_splits using original tick_spacing, use it
    if splits_needed <= max_splits:
        sub_intervals = []
        current_tick = start_tick
        
        while current_tick < end_tick:
            next_split_tick = min(current_tick + tick_spacing, end_tick)
            sub_intervals.append((current_tick, next_split_tick))
            current_tick = next_split_tick
        
        return sub_intervals
    
    # If we can't cover the range, calculate a larger interval that's a multiple of tick_spacing
    # We want to distribute max_splits evenly across the range
    optimal_interval = total_range / max_splits
    
    # Round up to the nearest multiple of tick_spacing
    interval_multiplier = math.ceil(optimal_interval / tick_spacing)
    actual_interval = interval_multiplier * tick_spacing
    
    # Generate sub-intervals with the calculated interval
    sub_intervals = []
    current_tick = start_tick
    split_count = 0
    
    while current_tick < end_tick and split_count < max_splits:
        next_split_tick = min(current_tick + actual_interval, end_tick)
        sub_intervals.append((current_tick, next_split_tick))
        current_tick = next_split_tick
        split_count += 1
    
    return sub_intervals

def create_sub_tick_data(original_tick_data, tick_index, is_original=True):

    sub_tick_data = {
        'liquidityGross': original_tick_data.get('liquidityGross', '0'),
        'liquidityNet': original_tick_data.get('liquidityNet', '0') if is_original else '0',
        'initialized': True,
    }
    return sub_tick_data

def process_pool_ticks(pool_data, max_price_diff_pct=0.05, max_distance_from_current=100000, max_splits_per_tick=50):
    try:
        pool_static_info = pool_data['poolState']['poolStaticInfo']
        pool_state = pool_data['poolState']['poolState']
        
        current_tick = int(pool_state.get('currentTick', 0))
        tick_spacing = int(pool_static_info.get('tickSpacing', 1))
        
        # Get original initialized ticks
        original_ticks = []
        tick_bitmap = pool_state.get('tickBitMap', [])
        
        for tick_entry in tick_bitmap:
            if len(tick_entry) == 2:
                tick_idx = int(tick_entry[0])
                tick_data = tick_entry[1]
                
                if tick_data.get('initialized', False) and int(tick_data.get('liquidityGross', 0)) > 0:
                    original_ticks.append((tick_idx, tick_data))
        
        # Sort ticks by index
        original_ticks.sort(key=lambda x: x[0])
        
        if len(original_ticks) < 2:
            return pool_data  # Can't split if less than 2 ticks
        
        # Process each tick interval
        new_tick_bitmap = []
        split_statistics = {
            'total_original_ticks': len(original_ticks),
            'total_intervals_checked': 0,
            'intervals_split': 0,
            'total_sub_ticks_created': 0
        }
        
        for i in range(len(original_ticks)):
            tick_idx, tick_data = original_ticks[i]
            
            # Add the original tick
            new_tick_bitmap.append([tick_idx, tick_data])
            
            # Check if we need to split the interval to the next tick
            if i < len(original_ticks) - 1:
                next_tick_idx, _ = original_ticks[i + 1]
                split_statistics['total_intervals_checked'] += 1
                
                # Determine if this interval should be split
                if should_split_tick(tick_idx, next_tick_idx, current_tick, 
                                   max_price_diff_pct, max_distance_from_current):
                    
                    # Split the interval
                    sub_intervals = split_tick_range(tick_idx, next_tick_idx, 
                                                   tick_spacing, max_splits_per_tick)
                    
                    if len(sub_intervals) > 1:  # Only if actually split
                        split_statistics['intervals_split'] += 1
                        
                        # Add sub-ticks for each sub-interval (except the first one which is the original)
                        for j, (start_sub_tick, end_sub_tick) in enumerate(sub_intervals[1:], 1):
                            if start_sub_tick < next_tick_idx:  # Don't duplicate the next original tick
                                sub_tick_data = create_sub_tick_data(tick_data, start_sub_tick, is_original=False)
                                new_tick_bitmap.append([start_sub_tick, sub_tick_data])
                                split_statistics['total_sub_ticks_created'] += 1
        
        # Update the pool data with new tick bitmap
        updated_pool_data = pool_data.copy()
        updated_pool_data['poolState']['poolState']['tickBitMap'] = new_tick_bitmap
        
        # Add processing metadata
        updated_pool_data['tick_processing_stats'] = split_statistics
        
        return updated_pool_data
    
    except Exception as e:
        print(f"Error processing pool {pool_data.get('poolState', {}).get('poolState', {}).get('poolAddress', 'unknown')}: {e}")
        return pool_data

def process_all_v3_pools(input_file_path, output_file_path, max_price_diff_pct=0.05, 
                        max_distance_from_current=10000, max_splits_per_tick=50):
    """
    Process all V3 pools in the input file and save processed data
    
    Args:
        input_file_path: Path to input V3 pools JSON file
        output_file_path: Path to save processed pools JSON file
        max_price_diff_pct: Maximum allowed price difference percentage for splitting
        max_distance_from_current: Maximum distance from current price to consider splitting
        max_splits_per_tick: Maximum number of splits per original tick
    
    Returns:
        Dictionary containing processing statistics
    """
    print(f"Loading V3 pool data from {input_file_path}...")
    v3_pools = load_v3_pool_data(input_file_path)
    print(f"Loaded {len(v3_pools)} V3 pools")
    
    if not v3_pools:
        print("No V3 pools found. Exiting.")
        return {}
    
    processed_pools = []
    overall_stats = {
        'total_pools_processed': 0,
        'total_pools_with_splits': 0,
        'total_original_ticks': 0,
        'total_intervals_checked': 0,
        'total_intervals_split': 0,
        'total_sub_ticks_created': 0
    }
    
    print("Processing pools...")
    for i, pool_data in enumerate(v3_pools):
        if i % 100 == 0:
            print(f"Processed {i}/{len(v3_pools)} pools...")
        
        processed_pool = process_pool_ticks(
            pool_data, 
            max_price_diff_pct=max_price_diff_pct,
            max_distance_from_current=max_distance_from_current,
            max_splits_per_tick=max_splits_per_tick
        )
        
        processed_pools.append(processed_pool)
        overall_stats['total_pools_processed'] += 1
        
        # Aggregate statistics
        if 'tick_processing_stats' in processed_pool:
            stats = processed_pool['tick_processing_stats']
            overall_stats['total_original_ticks'] += stats['total_original_ticks']
            overall_stats['total_intervals_checked'] += stats['total_intervals_checked']
            overall_stats['total_intervals_split'] += stats['intervals_split']
            overall_stats['total_sub_ticks_created'] += stats['total_sub_ticks_created']
            
            if stats['intervals_split'] > 0:
                overall_stats['total_pools_with_splits'] += 1
    
    # Save processed data
    print(f"Saving processed data to {output_file_path}...")
    os.makedirs(os.path.dirname(output_file_path), exist_ok=True)
    
    # Save as JSON objects directly (same format as input)
    with open(output_file_path, 'w') as f:
        json.dump(processed_pools, f, indent=2)
    
    # Save statistics
    stats_file_path = output_file_path.replace('.json', '_stats.json')
    with open(stats_file_path, 'w') as f:
        json.dump(overall_stats, f, indent=2)
    
    print("\nProcessing completed!")
    print(f"Total pools processed: {overall_stats['total_pools_processed']}")
    print(f"Pools with splits: {overall_stats['total_pools_with_splits']}")
    print(f"Total original ticks: {overall_stats['total_original_ticks']}")
    print(f"Total intervals checked: {overall_stats['total_intervals_checked']}")
    print(f"Total intervals split: {overall_stats['total_intervals_split']}")
    print(f"Total sub-ticks created: {overall_stats['total_sub_ticks_created']}")
    print(f"Processed data saved to: {output_file_path}")
    print(f"Statistics saved to: {stats_file_path}")
    
    return overall_stats

def main():
    """Main function to process V3 pool data"""
    # Configuration
    block_number = 21974203
    input_file_path = f'../data/prices/block-{block_number}/filtered-v3pools.json'
    output_file_path = f'../data/prices/block-{block_number}/processed-v3pools.json'
    
    # Processing parameters
    max_price_diff_pct = 0.05  # 5% price difference threshold
    max_distance_from_current = 10000  # Maximum tick distance from current price
    max_splits_per_tick = 50  # Maximum splits per original tick
    
    # Check if input file exists
    if not os.path.exists(input_file_path):
        print(f"Error: Input file {input_file_path} not found.")
        return
    
    # Process all pools
    stats = process_all_v3_pools(
        input_file_path=input_file_path,
        output_file_path=output_file_path,
        max_price_diff_pct=max_price_diff_pct,
        max_distance_from_current=max_distance_from_current,
        max_splits_per_tick=max_splits_per_tick
    )
    
    return stats

if __name__ == "__main__":
    main()