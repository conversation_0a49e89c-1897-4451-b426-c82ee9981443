import pandas as pd
import json
from typing import Dict, List, Any, <PERSON><PERSON>

def load_json_files(weights_file: str, capacities_file: str) -> Tuple[Dict, Dict]:
    """
    Load the weights and capacities JSON files.
    
    Args:
        weights_file: Path to the weights JSON file
        capacities_file: Path to the capacities JSON file
    
    Returns:
        Tuple of (weights_data, capacities_data)
    """
    with open(weights_file, 'r') as f:
        weights_data = json.load(f)
    
    with open(capacities_file, 'r') as f:
        capacities_data = json.load(f)
    
    return weights_data, capacities_data

def merge_ticks_by_threshold(ticks_weights: List[Dict], ticks_capacities: List[Dict], threshold: float) -> List[Dict]:
    """
    Merge ticks based on capacity threshold.
    Keep merging with nearby ticks until capacity reaches threshold or no more ticks available.
    Take the worse (bigger) weight value and sum the capacities.
    
    Args:
        ticks_weights: List of tick objects with weights
        ticks_capacities: List of tick objects with capacities
        threshold: Capacity threshold for merging
    
    Returns:
        List of merged tick objects with both weight and capacity
    """
    # Create a mapping from tick to weight and capacity
    weight_map = {tick['tick']: tick['weight'] for tick in ticks_weights}
    capacity_map = {tick['tick']: tick['capacity'] for tick in ticks_capacities}
    
    # Get all unique ticks and sort them
    all_ticks = set(weight_map.keys()) & set(capacity_map.keys())
    
    # Separate v2 and v3 ticks for proper sorting
    v2_ticks = [tick for tick in all_ticks if str(tick).startswith('v2_')]
    v3_ticks = [tick for tick in all_ticks if not str(tick).startswith('v2_')]
    
    # Sort v2 ticks by their numeric part
    v2_ticks.sort(key=lambda x: int(str(x).split('_')[1]) if '_' in str(x) else float('inf'))
    
    # Sort v3 ticks numerically
    v3_ticks.sort(key=lambda x: int(x) if str(x).isdigit() or (str(x).lstrip('-').isdigit()) else float('inf'))
    
    # Process v2 and v3 ticks separately
    merged_ticks = []
    
    for tick_list, pool_type in [(v2_ticks, 'v2'), (v3_ticks, 'v3')]:
        if not tick_list:
            continue
            
        i = 0
        while i < len(tick_list):
            current_tick = tick_list[i]
            current_capacity = capacity_map[current_tick]
            current_weight = weight_map[current_tick]
            merged_with_ticks = []
            
            # Keep merging until capacity reaches threshold or no more ticks
            j = i + 1
            while current_capacity < threshold and j < len(tick_list):
                next_tick = tick_list[j]
                next_capacity = capacity_map[next_tick]
                next_weight = weight_map[next_tick]
                
                # Merge: sum capacities, take worse (bigger) weight
                current_capacity += next_capacity
                current_weight = max(current_weight, next_weight)
                merged_with_ticks.append(next_tick)
                
                j += 1
            
            # Create the final merged tick object
            merged_tick = {
                'tick': current_tick,  # Keep the first tick's identifier
                'weight': current_weight,
                'capacity': current_capacity,
                'pool_type': pool_type
            }
            
            # Add merged_with information if any ticks were merged
            if merged_with_ticks:
                merged_tick['merged_with'] = merged_with_ticks
                merged_tick['original_capacity'] = capacity_map[current_tick]
                merged_tick['num_merged'] = len(merged_with_ticks)
            
            merged_ticks.append(merged_tick)
            
            # Move to the next unprocessed tick
            i = j
    
    return merged_ticks

def process_pool_data(weights_data: Dict, capacities_data: Dict, threshold: float) -> Dict:
    """
    Process all pools in the data by merging ticks based on threshold.
    
    Args:
        weights_data: Weights data dictionary
        capacities_data: Capacities data dictionary
        threshold: Capacity threshold for merging
    
    Returns:
        Dictionary with processed data
    """
    processed_data = {}
    
    # Iterate through all token pairs
    for token_pair in weights_data.keys():
        if token_pair not in capacities_data:
            print(f"Warning: Token pair {token_pair} not found in capacities data")
            continue
            
        processed_data[token_pair] = {}
        
        # Iterate through all pools for this token pair
        for pool_address in weights_data[token_pair].keys():
            if pool_address not in capacities_data[token_pair]:
                print(f"Warning: Pool {pool_address} not found in capacities data for {token_pair}")
                continue
            
            weights_ticks = weights_data[token_pair][pool_address]
            capacities_ticks = capacities_data[token_pair][pool_address]
            
            # Merge ticks for this pool
            merged_ticks = merge_ticks_by_threshold(weights_ticks, capacities_ticks, threshold)
            
            processed_data[token_pair][pool_address] = merged_ticks
    
    return processed_data

def save_processed_data(processed_data: Dict, weights_output_file: str, capacities_output_file: str):
    """
    Save the processed data to separate weights and capacities JSON files.
    
    Args:
        processed_data: Processed data dictionary
        weights_output_file: Output file path for weights
        capacities_output_file: Output file path for capacities
    """
    # Split the processed data into weights and capacities
    weights_data = {}
    capacities_data = {}
    
    for token_pair, pools in processed_data.items():
        weights_data[token_pair] = {}
        capacities_data[token_pair] = {}
        
        for pool_address, ticks in pools.items():
            weights_ticks = []
            capacities_ticks = []
            
            for tick in ticks:
                # Create weight tick object
                weight_tick = {
                    'tick': tick['tick'],
                    'weight': tick['weight'],
                    'pool_type': tick['pool_type']
                }
                weights_ticks.append(weight_tick)
                
                # Create capacity tick object
                capacity_tick = {
                    'tick': tick['tick'],
                    'capacity': tick['capacity'],
                    'pool_type': tick['pool_type']
                }
                capacities_ticks.append(capacity_tick)
            
            weights_data[token_pair][pool_address] = weights_ticks
            capacities_data[token_pair][pool_address] = capacities_ticks
    
    # Save both files
    with open(weights_output_file, 'w') as f:
        json.dump(weights_data, f, indent=2)
    
    with open(capacities_output_file, 'w') as f:
        json.dump(capacities_data, f, indent=2)

def main():
    """
    Main function to process the tick data.
    """
    block_number = 21974203
    # File paths
    weights_file = f"graphs/{block_number}_pool_weights.json"
    capacities_file = f"graphs/{block_number}_pool_capacities.json"
    processed_weights_file = f"graphs/{block_number}_processed_pool_weights.json"
    processed_capacities_file = f"graphs/{block_number}_processed_pool_capacities.json"
    
    # Set your threshold here
    threshold = 10  # Adjust this value as needed
    
    print("Loading JSON files...")
    weights_data, capacities_data = load_json_files(weights_file, capacities_file)
    
    print(f"Processing ticks with threshold: {threshold}")
    processed_data = process_pool_data(weights_data, capacities_data, threshold)
    
    print(f"Saving processed data to {processed_weights_file} and {processed_capacities_file}")
    save_processed_data(processed_data, processed_weights_file, processed_capacities_file)
    
    # Print some statistics
    total_pools = sum(len(pools) for pools in processed_data.values())
    total_ticks = sum(len(ticks) for pools in processed_data.values() for ticks in pools.values())
    merged_ticks = sum(1 for pools in processed_data.values() 
                      for ticks in pools.values() 
                      for tick in ticks if 'merged_with' in tick)
    
    print(f"\nProcessing complete!")
    print(f"Total token pairs: {len(processed_data)}")
    print(f"Total pools: {total_pools}")
    print(f"Total ticks after processing: {total_ticks}")
    print(f"Number of merged ticks: {merged_ticks}")
    print(f"Files created:")
    print(f"  - {processed_weights_file}")
    print(f"  - {processed_capacities_file}")

if __name__ == "__main__":
    main()