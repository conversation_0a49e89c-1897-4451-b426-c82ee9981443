import pandas as pd

# Read the node map file
names = ['21974203_pairs_capacity_usdt_ohm']
# name = '21974203_pairs_usdt_ohm'

for name in names:
    ####### prepare relationship pairs ######
    node_map_path = f'graphs/{name}_node_map.txt'

    # Dictionary to store node index to address mapping
    node_to_address = {}

    # Read the file
    with open(node_map_path, 'r') as f:
        for line in f:
            parts = line.strip().split()
            if len(parts) >= 2:
                node_idx = int(parts[0])
                address = parts[1]
                node_to_address[node_idx] = address

    # Find related nodes (same first 40 characters of address)
    related_nodes = {}

    # Group nodes by the first 40 characters of their addresses
    for node_idx, address in node_to_address.items():
        base_address = address[:40]  # First 40 characters
        if base_address not in related_nodes:
            related_nodes[base_address] = []
        related_nodes[base_address].append(node_idx)

    # Create pairs of related nodes
    relationship_pairs = []
    for base_address, nodes in related_nodes.items():
        if len(nodes) > 1:  # Only consider addresses with multiple nodes
            for i in range(len(nodes)):
                for j in range(i+1, len(nodes)):
                    relationship_pairs.append((nodes[i], nodes[j]))

    # Write the relationship pairs to a file
    output_path = f'graphs/relationship/{name}_relationships.txt'
    with open(output_path, 'w') as f:
        for node1, node2 in relationship_pairs:
            f.write(f"{node1} {node2}\n")

    print(f"Found {len(relationship_pairs)} relationship pairs")
    print(f"Written to {output_path}")