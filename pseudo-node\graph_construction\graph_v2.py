import json
import math
import networkx as nx
import pandas as pd
import os
import traceback

def load_data_from_json(file_path):
    """Load data from a JSON file"""
    with open(file_path, 'r') as file:
        return json.load(file)

def compute_exchange_rate(reserveA, reserveB, trade_amount):
    if trade_amount >= reserveA:
        return None # Invalid trade, prevents division by zero

    new_reserveA = reserveA + trade_amount  # Adding traded amount to reserveA
    new_reserveB = (reserveA * reserveB) / new_reserveA  # Solve for new reserveB using AMM formula
    new_rate = new_reserveB / new_reserveA  # Apply fee and compute rate
    return new_rate

def load_token_prices(price_file_path):
    """
    Load token prices from the quotes file
    Returns a dictionary mapping token address to its WETH price
    """
    try:
        with open(price_file_path, 'r') as file:
            price_data = json.load(file)
        
        # Create price mapping
        token_prices = {}
        for token in price_data:
            address = token['address'].lower()
            # Use avg_quote as the price, default to 0 if not available
            try:
                price = float(token['quotes']['avg_quote'])
            except (<PERSON><PERSON><PERSON>r, ValueError, TypeError):
                price = 0
            token_prices[address] = price
            
        return token_prices
    except Exception as e:
        print(f"Error loading token prices: {e}")
        return {}

def calculate_exchange_rates_v2(pools, eth_trade_amount, token_prices):
    """
    Compute the new exchange rate after a trade.
    eth_trade_amount: The trade amount in ETH (e.g., 0.05 ETH)
    token_prices: Dictionary mapping token addresses to their WETH prices
    """
    results = []
    skipped_pools = 0

    for pool in pools:
        try:
            pool_data = json.loads(pool) if isinstance(pool, str) else pool

            if pool_data['poolType'] == 'uniswap_v2_like_pool':
                pool_static_info = pool_data['poolState']['poolStaticInfo']
                if pool_static_info['dexExchange'] == 'uniswap':
                    pool_state = pool_data['poolState']['poolState']
                    
                    token0 = pool_static_info['token0'].lower()
                    token1 = pool_static_info['token1'].lower()
                    
                    # Get token prices in WETH
                    token0_price_in_eth = token_prices.get(token0, 0)
                    token1_price_in_eth = token_prices.get(token1, 0)

                    token_balance0 = float(pool_state['tokenBalance0'])/10**int(pool_static_info['token0Decimals'])
                    token_balance1 = float(pool_state['tokenBalance1'])/10**int(pool_static_info['token1Decimals'])

                    if token_balance0 > 0 and token_balance1 > 0:
                        # Calculate trade amounts based on token prices in ETH
                        if token0_price_in_eth > 0:
                            trade_amount0 = eth_trade_amount / token0_price_in_eth
                        else:
                            print(f"Token0 price is 0 for pool {pool_state['poolAddress']}")
                            trade_amount0 = 0
                            skipped_pools += 1
                            continue
                            
                        if token1_price_in_eth > 0:
                            trade_amount1 = eth_trade_amount / token1_price_in_eth
                        else:
                            print(f"Token1 price is 0 for pool {pool_state['poolAddress']}")
                            trade_amount1 = 0
                            skipped_pools += 1
                            continue

                        # Calculate rates using the ETH-equivalent amounts
                        rate_token0_to_token1 = compute_exchange_rate(token_balance0, token_balance1, trade_amount0) if trade_amount0 > 0 else None
                        rate_token1_to_token0 = compute_exchange_rate(token_balance1, token_balance0, trade_amount1) if trade_amount1 > 0 else None
                    else:
                        rate_token0_to_token1 = rate_token1_to_token0 = None
                    
                    result = {
                        'pool_id': pool_state['poolAddress'],
                        'token0_symbol': token0,
                        'token1_symbol': token1, 
                        'token0_balance': token_balance0,
                        'token1_balance': token_balance1,
                        'rate_token0_to_token1': rate_token0_to_token1,
                        'rate_token1_to_token0': rate_token1_to_token0,
                        'swap_fee': pool_static_info['swapFee']
                    }
                    results.append(result)
        except Exception as e:
            skipped_pools += 1
            continue
            
    return results, skipped_pools

def create_token_graph(exchange_rates_v2, tax=1):
    """Create a token exchange graph with weighted edges"""
    G = nx.DiGraph()
    log_base = math.e
    
    # Process v2 pools
    for rate in exchange_rates_v2:
        token0 = rate['token0_symbol'].lower()
        token1 = rate['token1_symbol'].lower()
        
        # Apply swap fee
        lambda_tax = float(rate['swap_fee'])/(10**6) * tax
        
        if rate['rate_token0_to_token1'] is None or rate['rate_token1_to_token0'] is None:
            continue
            
        try:
            # Apply swap fee here
            rate0to1 = (1 - lambda_tax) * rate['rate_token0_to_token1']
            rate1to0 = (1 - lambda_tax) * rate['rate_token1_to_token0']
            
            if rate0to1 <= 0 or rate1to0 <= 0:
                continue
                
            pij = -math.log(rate0to1, log_base)
            pji = -math.log(rate1to0, log_base)
            
            G.add_node(token0)
            G.add_node(token1)
            G.add_edge(token0, token1, weight=pij)
            G.add_edge(token1, token0, weight=pji)
        except (ValueError, TypeError, ZeroDivisionError):
            continue
    
    return G

def print_graph(token_graph, path):
    """Save the graph to files with node mapping"""
    # Ensure output directory exists
    os.makedirs(os.path.dirname(path) if os.path.dirname(path) else '.', exist_ok=True)
    
    # Sort nodes to ensure consistent node IDs
    sorted_nodes = sorted(token_graph.nodes())
    node_map = {node: idx for idx, node in enumerate(sorted_nodes)}
    indexed_graph = nx.DiGraph()

    for edge in token_graph.edges(data=True):
        source, target, data = edge
        indexed_graph.add_edge(node_map[source], node_map[target], weight=data['weight'])

    graph_file = '{}_token_graph.txt'.format(path)
    map_file = '{}_node_map.txt'.format(path)
    
    with open(graph_file, 'w') as f:
        for edge in indexed_graph.edges(data=True):
            f.write("{} {} {}\n".format(edge[0], edge[1], edge[2]['weight']))

    with open(map_file, 'w') as f:
        for node, idx in node_map.items():
            f.write("{} {}\n".format(idx, node))

    return graph_file, map_file
    
def main():
    """Main function to process pool data and create token graph"""
    try:
        block_number = 21974203
        # File paths
        v2_file_path = f'prices/block-{block_number}/filtered-v2pools.json'
        price_file_path = f'prices/block-{block_number}/tokens-quotes.json'

        # Load token prices
        token_prices = load_token_prices(price_file_path)
        
        eth_trade_amount = 0.1  # Trade amount in ETH
        
        # Load and process v2 pools with token prices
        v2_data = load_data_from_json(v2_file_path)
        v2_pools, v2_skipped = calculate_exchange_rates_v2(v2_data, eth_trade_amount, token_prices)
        
        # Create graph
        graph = create_token_graph(v2_pools, tax=1)
        nodes = len(graph.nodes())
        edges = len(graph.edges())
        
        # Create output directory
        os.makedirs('graphs', exist_ok=True)
        
        graph_file, map_file = print_graph(graph, f'graphs/{block_number}_{eth_trade_amount}_v2only')
        
        # Final summary output
        print(f"Processed {len(v2_pools)} v2 pools, skipped {v2_skipped}")
        print(f"Created graph with {nodes} nodes and {edges} edges")
        print(f"Saved output to {graph_file} and {map_file}")
        
    except Exception as e:
        print(f"Error: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()