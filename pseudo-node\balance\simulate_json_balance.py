import pandas as pd
import json
import os
from web3 import Web3
from typing import List, Dict, Tuple
import numpy as np
import math
import copy
from decimal import Decimal, getcontext, ROUND_DOWN, ROUND_HALF_UP

# Set ultra-high precision for financial calculations
getcontext().prec = 256  # Increase the precision even further

EPSILON = Decimal('1e-18')  # A small threshold for floating-point precision


def price_from_tick(tick: int) -> Decimal:
    """
    Calculate price as 1.0001 raised to the power of the tick.
    This is the Uniswap V3 price formula.
    """
    return Decimal('1.0001') ** Decimal(tick)


def tick_from_price(price: Decimal) -> int:
    """
    Calculate the tick value corresponding to a given price using the formula:
    tick = log(price) / log(1.0001).
    Ensures proper flooring and handles small values by using epsilon.
    """
    epsilon = Decimal('1e-128')  # Threshold for very small prices
    correction_value = Decimal('1e-18')  # Prevent division by zero for very small prices

    if price <= epsilon:
        price = correction_value  # Correct the value to prevent mathematical errors

    # Return the floor of the logarithm of price divided by the logarithm of 1.0001
    return int(math.floor(math.log(float(price)) / math.log(1.0001)))


def sqrt_price_q96_to_decimal(sqrt_price_x96: int) -> Decimal:
    """
    Convert Q64.96 sqrt price (fixed-point 96-bit integer) to a Decimal with high precision.
    This conversion is necessary because most of the calculations in Uniswap are done using
    a 96-bit fixed-point representation.
    """
    return Decimal(sqrt_price_x96) / (Decimal(2) ** 96)


def decimal_to_sqrt_price_q96(sqrt_price: Decimal) -> int:
    """
    Convert a Decimal representing the square root price back to a Q64.96 fixed-point integer
    by multiplying with 2^96 and truncating the result.
    """
    return int((sqrt_price * (Decimal(2) ** 96)).to_integral(rounding=ROUND_DOWN))


# ----- Functions for token0 -> token1 (zeroForOne swap) -----
def calculate_dx(L: Decimal, sp_current: Decimal, sp_target: Decimal) -> Decimal:
    """
    Calculate the amount of token0 (dx) required to move the price from sp_current to sp_target.
    This function is used for token0 -> token1 (zeroForOne) swaps.
    """
    if sp_current < EPSILON or sp_target < EPSILON:
        print(f"sp_current or sp_target is zero {sp_target} & {sp_current}")

        sp_current = EPSILON
        sp_target = EPSILON

    return L * (sp_current - sp_target) / (sp_current * sp_target)


def calculate_dy(L: Decimal, sp_current: Decimal, sp_target: Decimal) -> Decimal:
    """
    Calculate the amount of token1 (dy) received when moving the price from sp_current to sp_target.
    This is used for token0 -> token1 (zeroForOne) swaps.
    """
    if sp_current < EPSILON or sp_target < EPSILON:
        print(f"sp_current or sp_target is zero {sp_target} & {sp_current}")
        sp_current = EPSILON
        sp_target = EPSILON

    return L * (sp_current - sp_target)


def get_next_sqrt_price(L: Decimal, sp: Decimal, dx_net: Decimal) -> Decimal:
    """
    Calculate the new square root price after consuming a certain amount of token0 (dx_net).
    Ensures the price is updated accurately while avoiding division by zero.
    """
    if L < EPSILON:
        print("L is zero")
        L = EPSILON

    numerator = L * sp
    denominator = L + dx_net * sp
    return (numerator / denominator).quantize(Decimal('1e-18'), rounding=ROUND_DOWN)


# ----- Functions for token1 -> token0 (oneForZero swap) -----
def calculate_dy_one_for_zero(L: Decimal, sp_current: Decimal, sp_target: Decimal) -> Decimal:
    """
    Calculate the amount of token1 (dy) required to move the price from sp_current to sp_target
    when performing a token1 -> token0 (oneForZero) swap.
    """
    return L * (sp_target - sp_current)


def calculate_dx_one_for_zero(L: Decimal, sp_current: Decimal, sp_target: Decimal) -> Decimal:
    """
    Calculate the amount of token0 (dx) output when moving the price from sp_current to sp_target
    during a token1 -> token0 (oneForZero) swap.
    """
    return L * ((Decimal(1) / sp_current) - (Decimal(1) / sp_target))


def get_next_sqrt_price_one_for_zero(L: Decimal, sp: Decimal, dy_net: Decimal) -> Decimal:
    """
    Calculate the new square root price after consuming a certain amount of token1 (dy_net) in a
    token1 -> token0 (oneForZero) swap.
    """
    return (sp + (dy_net / L)).quantize(Decimal('1e-18'), rounding=ROUND_DOWN)


# ----- Simulation of token0 -> token1 swap (zeroForOne) -----
def simulate_swap_zero_for_one(pool_data: dict, amount_in: int, fee: Decimal):
    """
    Simulate a Uniswap V3 exactInput swap (token0 -> token1) using the given pool data,
    amount of token0 to swap, and fee rate. The function calculates the amount of token1
    output, the amount of token0 consumed, and any fees collected during the swap.
    """
    pool_state = pool_data["poolState"]["poolState"]
    fee_rate = fee
    sp_current = sqrt_price_q96_to_decimal(int(pool_state["sqrtPriceX96"]))

    if sp_current <= Decimal('1e-18'):
        sp_current = Decimal('1e-18')

    L = Decimal(pool_state["liquidity"])  # Liquidity in the pool
    amount_remaining = Decimal(amount_in)  # Amount of token0 remaining for the swap
    token1_out = Decimal(0)  # Token1 output after the swap
    total_fee = Decimal(0)  # Total fee collected during the swap

    # Process ticks below current tick (descending order)
    ticks = sorted(
        [t for t in pool_state["tickBitMap"] if int(t[0]) < int(pool_state["currentTick"])],
        key=lambda x: -int(x[0])  # Sort ticks in descending order
    )

    for tick_info in ticks:
        tick_idx = int(tick_info[0])
        tick_data = tick_info[1]
        liquidity_net = Decimal(tick_data["liquidityNet"])
        initialized = tick_data["initialized"]

        if amount_remaining <= 0:
            break

        # Calculate the price corresponding to this tick
        sp_target = (Decimal('1.0001') ** (Decimal(tick_idx) / 2)).quantize(Decimal('1e-18'), rounding=ROUND_DOWN)
        if sp_target >= sp_current:
            continue

        dx_net_needed = calculate_dx(L, sp_current, sp_target)  # Calculate token0 required
        dx_gross_needed = dx_net_needed / (Decimal(1) - fee_rate)  # Gross token0 needed with fees

        if amount_remaining >= dx_gross_needed:
            token1_out += calculate_dy(L, sp_current, sp_target)  # Calculate token1 output
            total_fee += dx_gross_needed - dx_net_needed  # Calculate fees
            amount_remaining -= dx_gross_needed
            sp_current = sp_target

            if initialized:
                L -= liquidity_net  # Adjust liquidity when crossing a tick
        else:
            dx_net_available = amount_remaining * (Decimal(1) - fee_rate)
            sp_new = get_next_sqrt_price(L, sp_current, dx_net_available)
            token1_out += calculate_dy(L, sp_current, sp_new)
            total_fee += amount_remaining - dx_net_available
            amount_remaining = Decimal(0)
            sp_current = sp_new

    # Handle the final partial swap if input still remains
    if amount_remaining > 0:
        dx_net_available = amount_remaining * (Decimal(1) - fee_rate)
        sp_new = get_next_sqrt_price(L, sp_current, dx_net_available)
        token1_out += calculate_dy(L, sp_current, sp_new)
        total_fee += amount_remaining - dx_net_available
        sp_current = sp_new

    if sp_current == 0:
        sp_current = Decimal('1e-18')

    # Correct balance calculations (balance is based on liquidity and price)
    balance0 = Decimal(L / sp_current).to_integral(rounding=ROUND_DOWN)
    balance1 = Decimal(L * sp_current).to_integral(rounding=ROUND_DOWN)

    # Update the pool state after the swap
    updated_state = copy.deepcopy(pool_state)
    updated_state["sqrtPriceX96"] = str(decimal_to_sqrt_price_q96(sp_current))
    updated_state["currentTick"] = str(tick_from_price(sp_current ** 2))
    updated_state["liquidity"] = str(int(L))
    updated_state["balance0"] = str(balance0)
    updated_state["balance1"] = str(balance1)

    return {
        "token1_output": token1_out,
        "token0_consumed": Decimal(amount_in) - amount_remaining,
        "fees_collected": total_fee,
        "new_pool_state": updated_state,
        "final_price": sp_current ** 2
    }

# ----- Simulation of token1 -> token0 swap (oneForZero) -----
def simulate_swap_one_for_zero(pool_data: dict, amount_in: int, fee: Decimal):
    """
    Simulate a Uniswap V3 exactInput swap (token1 -> token0) using the given pool data,
    amount of token1 to swap, and fee rate. The function calculates the amount of token0
    output, the amount of token1 consumed, and any fees collected during the swap.
    """
    pool_state = pool_data["poolState"]["poolState"]
    fee_rate = fee
    sp_current = sqrt_price_q96_to_decimal(int(pool_state["sqrtPriceX96"]))

    if sp_current <= Decimal('1e-18'):
        sp_current = Decimal('1e-18')

    L = Decimal(pool_state["liquidity"])  # Liquidity in the pool
    amount_remaining = Decimal(amount_in)  # Amount of token1 remaining for the swap
    token0_out = Decimal(0)  # Token0 output after the swap
    total_fee = Decimal(0)  # Total fee collected during the swap

    # Process ticks above current tick (ascending order)
    ticks = sorted(
        [t for t in pool_state["tickBitMap"] if int(t[0]) > int(pool_state["currentTick"])],
        key=lambda x: int(x[0])  # Sort ticks in ascending order
    )

    for tick_info in ticks:
        tick_idx = int(tick_info[0])
        tick_data = tick_info[1]
        liquidity_net = Decimal(tick_data["liquidityNet"])
        initialized = tick_data["initialized"]

        if amount_remaining <= 0:
            break

        # Calculate the price corresponding to this tick
        sp_target = (Decimal('1.0001') ** (Decimal(tick_idx) / 2)).quantize(Decimal('1e-18'), rounding=ROUND_DOWN)
        if sp_target <= sp_current:
            continue

        dy_net_needed = calculate_dy_one_for_zero(L, sp_current, sp_target)  # Calculate token1 required
        dy_gross_needed = dy_net_needed / (Decimal(1) - fee_rate)  # Gross token1 needed with fees

        if amount_remaining >= dy_gross_needed:
            token0_out += calculate_dx_one_for_zero(L, sp_current, sp_target)  # Calculate token0 output
            total_fee += dy_gross_needed - dy_net_needed  # Calculate fees
            amount_remaining -= dy_gross_needed
            sp_current = sp_target

            if initialized:
                L += liquidity_net  # Adjust liquidity when crossing a tick
        else:
            dy_net_available = amount_remaining * (Decimal(1) - fee_rate)
            sp_new = get_next_sqrt_price_one_for_zero(L, sp_current, dy_net_available)
            token0_out += calculate_dx_one_for_zero(L, sp_current, sp_new)
            total_fee += amount_remaining - dy_net_available
            amount_remaining = Decimal(0)
            sp_current = sp_new

    # Handle the final partial swap if input still remains
    if amount_remaining > 0:
        dy_net_available = amount_remaining * (Decimal(1) - fee_rate)
        sp_new = get_next_sqrt_price_one_for_zero(L, sp_current, dy_net_available)
        token0_out += calculate_dx_one_for_zero(L, sp_current, sp_new)
        total_fee += amount_remaining - dy_net_available
        sp_current = sp_new

    # Correct balance calculations (balance is based on liquidity and price)
    balance0 = Decimal(L / sp_current).to_integral(rounding=ROUND_DOWN)
    balance1 = Decimal(L * sp_current).to_integral(rounding=ROUND_DOWN)

    # Update the pool state after the swap
    updated_state = copy.deepcopy(pool_state)
    updated_state["sqrtPriceX96"] = str(decimal_to_sqrt_price_q96(sp_current))
    updated_state["currentTick"] = str(tick_from_price(sp_current ** 2))
    updated_state["liquidity"] = str(int(L))
    updated_state["balance0"] = str(balance0)
    updated_state["balance1"] = str(balance1)

    return {
        "token0_output": token0_out,
        "token1_consumed": Decimal(amount_in) - amount_remaining,
        "fees_collected": total_fee,
        "new_pool_state": updated_state,
        "final_price": sp_current ** 2
    }


class UniswapV3Mathematical:
    """Mathematical implementation for Uniswap V3 pools."""
    
    def __init__(self) -> None:
        # The node_rpc_url is maintained for compatibility but not used
        self.v3_pools_data = []  # This will be filled when initialize_pools is called
    
    def initialize_pools(self, v3_pools_data):
        """Initialize with pool data from the loaded JSON"""
        self.v3_pools_data = v3_pools_data
    
    def exact_input(
        self,
        token_in: str,
        token_out: str,
        fee: int,
        amount_in: int,
        block_no: int,
        sqrt_price_limit_x96: int = 0,
    ) -> int:
        """Simulate exact input swap for V3 pools by mathematical calculation."""
        # Get the pool data for this token pair and fee
        pool_data = self._get_pool_data(token_in, token_out, fee, block_no)
        
        if not pool_data:
            raise ValueError(f"Pool not found for {token_in} -> {token_out} with fee {fee}")
        
        # Convert fee to decimal (e.g. 3000 -> 0.003)
        fee_decimal = Decimal(fee) / Decimal(1000000)
        
        # Determine if this is a token0 to token1 swap or vice versa
        pool_token0 = pool_data["poolState"]["poolStaticInfo"]["token0"].lower()
        
        if token_in.lower() == pool_token0:
            # token0 to token1 swap
            result = simulate_swap_zero_for_one(pool_data, amount_in, fee_decimal)
            return int(result["token1_output"])
        else:
            # token1 to token0 swap
            result = simulate_swap_one_for_zero(pool_data, amount_in, fee_decimal)
            return int(result["token0_output"])
    
    def _get_pool_data(self, token_in: str, token_out: str, fee: int, block_no: int) -> dict:
        """
        Get pool data for a given token pair and fee.
        Uses the pool data loaded from JSON files.
        """
        token_in = token_in.lower()
        token_out = token_out.lower()
        
        # 找到匹配代币对和费率的池子
        for pool in self.v3_pools_data:
            pool_info = pool["poolState"]["poolStaticInfo"]
            pool_token0 = pool_info["token0"].lower()
            pool_token1 = pool_info["token1"].lower()
            pool_fee = int(pool_info["swapFee"])
            
            # 检查池子是否匹配代币和费率
            tokens_match = (
                (token_in == pool_token0 and token_out == pool_token1) or
                (token_in == pool_token1 and token_out == pool_token0)
            )
            
            if tokens_match and pool_fee == fee:
                return pool
        
        return None


# Replace V3Simulator with UniswapV3Mathematical
V3Simulator = UniswapV3Mathematical

class V2Simulator:
    """Simulator for Uniswap V2 pools."""
    
    def __init__(self, node_rpc_url: str) -> None:
        self.web3 = Web3(Web3.HTTPProvider(node_rpc_url))
        
        if not self.web3.is_connected():
            raise Exception("Failed to connect to the network")
        
        # Router contract for V2 pools
        V2_ROUTER2_ADDRESS = "0x7a250d5630B4cF539739dF2C5dAcb4c659F2488D"
        V2_ROUTER2_ABI = [
            {
                "inputs": [
                    {"internalType": "uint256", "name": "amountIn", "type": "uint256"},
                    {"internalType": "uint256", "name": "reserveIn", "type": "uint256"},
                    {"internalType": "uint256", "name": "reserveOut", "type": "uint256"},
                ],
                "name": "getAmountOut",
                "outputs": [
                    {"internalType": "uint256", "name": "amountOut", "type": "uint256"}
                ],
                "stateMutability": "pure",
                "type": "function",
            }
        ]
        
        self.router_contract = self.web3.eth.contract(
            address=V2_ROUTER2_ADDRESS, abi=V2_ROUTER2_ABI
        )
    
    def get_amount_out(self, in_amount: int, in_reserve: int, out_reserve: int) -> int:
        """Calculate output amount for V2 pools."""
        out_amount = self.router_contract.functions.getAmountOut(
            in_amount, in_reserve, out_reserve
        ).call()
        
        return out_amount


def node_name_mapping(node_name_file):
    node_name_mapping = {}
    with open(node_name_file, 'r') as file:
        for line in file:
            line = line.strip()
            if line:
                parts = line.split()
                symbol = parts[0]
                address = parts[1]
                node_name_mapping[symbol] = address
    return node_name_mapping

def read_in_node_mapping(node_mapping_file):
    node_mapping = {}
    with open(node_mapping_file, 'r') as file:
        for line in file:
            line = line.strip()
            if line:
                parts = line.split()
                node_id = parts[0]
                address = parts[1]
                node_mapping[address] = node_id
    return node_mapping

def get_min_balance(pool_addresses, token_paths, balance_dict, pool_types=None):
    """
    Get the minimum balance for a list of pool addresses, considering the token swap direction.
    
    Args:
        pool_addresses: List of pool addresses in the path
        token_paths: List of token pairs in the path (e.g. ['tokenA', 'tokenB', 'tokenC'])
        balance_dict: Dictionary mapping pool addresses to their balances
        pool_types: List of pool types ('v2' or 'v3') corresponding to each pool in the path
        
    Returns:
        Minimum balance across all pools in the path, considering token direction
    """
    min_balance = float('inf')
    
    for i, pool_address in enumerate(pool_addresses):
        if i + 1 >= len(token_paths):
            # This shouldn't happen if path data is valid
            return 0
        
        # Check if this is a V2 pool
        if pool_types and i < len(pool_types) and pool_types[i].lower() == 'v2':
            # For V2 pools, use a fixed capacity of 100
            balance = 100
            min_balance = min(min_balance, balance)
            continue
            
        token_in = token_paths[i].lower()
        token_out = token_paths[i+1].lower()
        
        # Check if pool exists in balance dict
        if pool_address not in balance_dict:
            return 0
            
        # Check if balance is a list (direction-specific capacities)
        if isinstance(balance_dict[pool_address], list) and len(balance_dict[pool_address]) == 2:
            # For now, we'll assume balance_dict[pool][0] is for token0->token1 and balance_dict[pool][1] is for token1->token0
            # You would need to check this against actual pool token order data
            
            # In a real implementation, you would look this up from pool data
            # For now, we'll assume token0 comes first alphabetically
            if token_in < token_out:
                # token_in is token0, so use balance_dict[pool][0]
                balance = balance_dict[pool_address][0]
            else:
                # token_in is token1, so use balance_dict[pool][1]
                balance = balance_dict[pool_address][1]
        else:
            # Single balance value (same for both directions)
            balance = balance_dict[pool_address]
        
        min_balance = min(min_balance, balance)
    
    # If no pools or min_balance is still infinity, return 0
    if min_balance == float('inf'):
        return 0
    
    return min_balance


def calculate_proportions_bps(input_token: str, paths_pool_addresses: List[List[str]], 
                              paths_tokens: List[List[str]],
                              price_dict: dict,
                              balance_dict: dict, 
                              token_decimals_dict: dict,
                              original_amount: float,
                              paths_pool_types: List[List[str]] = None) -> Tuple[List[int], float]:
    """
    Calculate the proportion of input to allocate to each path in basis points (1/100 of 1%).
    Consider token direction when checking pool balances.
    
    Args:
        input_token: Address of the input token
        paths_pool_addresses: List of lists of pool addresses for each path
        paths_tokens: List of lists of tokens in each path
        price_dict: Dictionary mapping token address to its price
        balance_dict: Dictionary mapping pool address to its balance (or direction-specific balances)
        token_decimals_dict: Dictionary mapping token address to its decimals
        original_amount: Original input amount
        paths_pool_types: List of lists of pool types ('v2' or 'v3') for each path
        
    Returns:
        Tuple of (proportions in basis points, remaining amount)
    """
    if input_token not in price_dict:
        print(f"Warning: Could not find price for input token or no valid paths")
        return [], original_amount

    decimals = token_decimals_dict.get(input_token, 6)
    amount = original_amount / (10 ** decimals) * price_dict[input_token]
    print(f"Converting {original_amount} tokens to {amount} ETH equivalent")

    n_paths = len(paths_pool_addresses)
    if n_paths == 0:
        return [], amount

    working_balances = copy.deepcopy(balance_dict)
    proportions = [0] * n_paths
    remaining_amount = amount
    total_allocated_bps = 0

    for path_idx, path in enumerate(paths_pool_addresses):
        if remaining_amount <= 0 or total_allocated_bps >= 10000:
            break

        # Get the tokens for this path
        path_tokens = paths_tokens[path_idx]
        
        # Get pool types for this path if available
        path_pool_types = None
        if paths_pool_types and path_idx < len(paths_pool_types):
            path_pool_types = paths_pool_types[path_idx]
        
        min_balance = get_min_balance(path, path_tokens, working_balances, path_pool_types)
        if min_balance <= 0:
            continue

        allocation_amount = min(min_balance, remaining_amount)
        path_proportion = int((allocation_amount / amount) * 10000)

        # Clip to not exceed total 10000 bps
        if total_allocated_bps + path_proportion > 10000:
            path_proportion = 10000 - total_allocated_bps
            allocation_amount = (path_proportion / 10000) * amount

        if path_proportion <= 0:
            continue  # skip negligible allocation

        proportions[path_idx] = path_proportion
        total_allocated_bps += path_proportion
        remaining_amount -= allocation_amount

        # Update working balances for each pool in the path, considering token direction
        for i, pool_address in enumerate(path):
            # Skip updating balances for v2 pools
            if path_pool_types and i < len(path_pool_types) and path_pool_types[i].lower() == 'v2':
                continue
                
            if i + 1 >= len(path_tokens):
                continue
                
            token_in = path_tokens[i].lower()
            token_out = path_tokens[i+1].lower()
            
            # Check if pool exists in working_balances
            if pool_address not in working_balances:
                continue
                
            # Check if balance is a list (direction-specific capacities)
            if isinstance(working_balances[pool_address], list) and len(working_balances[pool_address]) == 2:
                # Determine which direction is being used (as in get_min_balance)
                if token_in < token_out:
                    # token_in is token0, so use balance_dict[pool][0]
                    working_balances[pool_address][0] -= allocation_amount
                else:
                    # token_in is token1, so use balance_dict[pool][1]
                    working_balances[pool_address][1] -= allocation_amount
            else:
                # Single balance value (same for both directions)
                working_balances[pool_address] -= allocation_amount
    
    # Convert remaining_amount back to input_token units
    remaining_amount_in_token = remaining_amount / price_dict[input_token] * (10 ** decimals)

    return proportions, remaining_amount_in_token


def simulate_paths_from_analysis(
    path_analysis_file: str,
    v3_pools_file: str,
    v2_pools_file: str,
    input_amount: int,
    node_rpc_url: str,
    block_number: int,
    name: str,
    balance_dict: dict,
    price_dict: dict,
    token_decimals_dict: dict
):
    """
    Simulate paths from a path analysis file and generate results using a layer-by-layer approach.
    
    Args:
        path_analysis_file: Path to the CSV file with path analysis
        v3_pools_file: Path to the V3 pools data file
        v2_pools_file: Path to the V2 pools data file
        input_amount: Input amount for simulation
        node_rpc_url: URL for the Ethereum node
        block_number: Block number for simulation
        name: Name for output files
    
    Returns:
        Tuple of (DataFrame with results, result dictionary)
    """
    # Create output directories
    os.makedirs('results', exist_ok=True)
    os.makedirs('../results/path_analysis_json', exist_ok=True)
    
    # Create log file
    log_path = f"../results/path_analysis_json/path_simulation_{name}_log_{input_amount}.txt"
    log_file = open(log_path, "w", encoding="utf-8")
    
    # Define a function to print to both console and log file
    def log_collect(message):
        print(message)
        log_file.write(message + "\n")
    
    log_collect(f"Starting path simulation for {name}")
    log_collect(f"Input amount: {input_amount}")
    log_collect(f"Block number: {block_number}")
    
    # Load path analysis data
    log_collect(f"Loading path analysis from {path_analysis_file}...")
    df = pd.read_csv(path_analysis_file)
    input_token = df['path'].values[0].split(' -> ')[0]
    
    # Convert string representations of lists to actual lists
    df['involved_pool_addresses'] = df['involved_pool_addresses'].apply(
        lambda x: eval(x) if isinstance(x, str) else x
    )
    df['pools_type_list'] = df['pools_type_list'].apply(
        lambda x: eval(x) if isinstance(x, str) else x
    )
    
    df['used_tick_ids'] = df['used_tick_ids'].apply(
        lambda x: eval(x) if isinstance(x, str) else x
    )
    
    # Validate path data consistency
    log_collect("Validating path data consistency...")
    valid_paths = []
    for i, row in df.iterrows():
        path_tokens = row['path'].split(' -> ')
        pool_addresses = row['involved_pool_addresses']
        pool_types = row['pools_type_list']
        
        valid_paths.append(i)
    
    # Keep only valid paths
    if len(valid_paths) < len(df):
        log_collect(f"Keeping {len(valid_paths)} valid paths out of {len(df)} total paths")
        df = df.iloc[valid_paths].reset_index(drop=True)
    
    # Load pools data
    log_collect("Loading pools data...")
    with open(v3_pools_file, "r") as file:
        v3_pools_data = json.load(file)
    
    with open(v2_pools_file, "r") as file:
        v2_pools_data = json.load(file)
    
    # Initialize simulators
    log_collect("Initializing simulators...")
    v3_simulator = V3Simulator()
    v3_simulator.initialize_pools(v3_pools_data)
    v2_simulator = V2Simulator(node_rpc_url)
    
    # Create lists of token paths and pool types for each path
    paths_tokens = []
    paths_pool_types = []
    for i, row in df.iterrows():
        path_tokens = row['path'].split(' -> ')
        paths_tokens.append(path_tokens)
        
        pool_types = row['pools_type_list']
        paths_pool_types.append(pool_types)
    
    # Calculate proportions based on min_balance, considering token direction and pool types
    proportions_bps, remaining_amount = calculate_proportions_bps(
        input_token, 
        df['used_tick_ids'].tolist(),
        paths_tokens,
        price_dict, 
        balance_dict, 
        token_decimals_dict,
        input_amount,
        paths_pool_types
    )
    
    # Add proportions to DataFrame
    df['proportion_bps'] = proportions_bps
    
    # Calculate input amount for each path
    df['path_input_amount'] = df['proportion_bps'].apply(
        lambda x: (input_amount * x) // 10000
    )
    
    # Write detailed allocation information to log file
    log_collect("\n" + "="*80)
    log_collect("DETAILED PATH ALLOCATION")
    log_collect("="*80)
    
    # Sort paths by proportion (highest first) for the allocation log
    allocation_df = df.sort_values(by='proportion_bps', ascending=False).reset_index(drop=True)
    
    total_allocated = 0
    for i, row in allocation_df.iterrows():
        path_id = row['path_id'] if 'path_id' in row else i + 1
        path_input = row['path_input_amount']
        proportion = row['proportion_bps']
        
        # Skip paths with zero input or zero proportion
        if path_input == 0 or proportion == 0:
            continue
            
        path_tokens = row['path']
        pool_addresses = row['involved_pool_addresses']
        pool_types = row['pools_type_list']
            
        total_allocated += path_input
        
        log_collect(f"\nPath {path_id}:")
        log_collect(f"  Tokens: {path_tokens}")
        log_collect(f"  Proportion: {proportion/100:.2f}% ({proportion} bps)")
        log_collect(f"  Input amount: {path_input} ({path_input/input_amount*100:.4f}% of total)")
        
        # Log pool details for this path
        log_collect("  Pools:")
        for j, (pool, pool_type) in enumerate(zip(pool_addresses, pool_types)):
            tokens = path_tokens.split(' -> ')
            token_in = tokens[j].strip()
            token_out = tokens[j+1].strip()
            log_collect(f"    {j+1}. {pool} ({pool_type.upper()}) - {token_in} → {token_out}")
    
    log_collect("\nAllocation Summary:")
    log_collect(f"  Total input amount: {input_amount}")
    log_collect(f"  Total allocated: {total_allocated} ({total_allocated/input_amount*100:.4f}% of input)")
    log_collect(f"  Unallocated: {input_amount - total_allocated} ({(input_amount - total_allocated)/input_amount*100:.4f}% of input)")
    log_collect("="*80 + "\n")
    
    # Prepare result structure
    result = {
        "blockNumber": block_number,
        "fromAmount": str(input_amount),
        "from": "",  # Will be set from first path
        "toAmount": "0",  # Will be updated after simulation
        "to": "",  # Will be set from first path
        "route": {
            "fills": [],
            # "tokens": []
        },
        'remaining_amount': remaining_amount
    }
    
    # Set source and destination tokens if available
    if not df.empty and 'path' in df.columns:
        first_path = df.iloc[0]['path']
        if ' -> ' in first_path:
            tokens = first_path.split(' -> ')
            result["from"] = tokens[0].lower()
            result["to"] = tokens[-1].lower()
    
    # Collect all tokens used in paths
    all_tokens = set()
    for _, row in df.iterrows():
        path_tokens = row['path']
        tokens = path_tokens.split(' -> ')
        for token in tokens:
            all_tokens.add(token.strip().lower())
    
    
    # Identify common pools across paths and calculate their total proportions
    log_collect("\nAnalyzing pool usage across paths...")
    pool_proportions = {}
    pool_info = {}  # Store additional info about each pool
    
    for i, row in df.iterrows():
        path_id = row['path_id'] if 'path_id' in row else i + 1
        pool_addresses = row['involved_pool_addresses']
        pool_types = row['pools_type_list']
        proportion_bps = row['proportion_bps']
        path_tokens = row['path']
        tokens = path_tokens.split(' -> ')
        
        
        for j, pool_address in enumerate(pool_addresses):
            if j < len(pool_types):
                pool_address = pool_address.lower()
                pool_type = pool_types[j]
                
                # Ensure tokens array has enough elements
                if j >= len(tokens) - 1:
                    log_collect(f"Warning: Path {path_id} has token count mismatch. Tokens: {len(tokens)}, Pools: {len(pool_addresses)}")
                    continue  # Skip this pool
                
                token_in = tokens[j].strip().lower()
                token_out = tokens[j+1].strip().lower()
                
                if pool_address not in pool_proportions:
                    pool_proportions[pool_address] = 0
                    pool_info[pool_address] = {
                        "type": pool_type,
                        "from": token_in,
                        "to": token_out
                    }
                
                pool_proportions[pool_address] += proportion_bps
    
    # Log pool usage information
    log_collect("Pool usage across paths:")
    for pool, total_proportion in pool_proportions.items():
        # Only log pools with non-zero proportion
        if total_proportion > 0:
            log_collect(f"Pool {pool}: {total_proportion/100}% of total volume")
    
    # Create fills with each pool appearing only once
    for pool_address, proportion in pool_proportions.items():
        # Skip pools with zero proportion
        if proportion <= 0:
            continue
            
        info = pool_info[pool_address]
        fill = {
            "from": info["from"],
            "to": info["to"],
            "pool": pool_address,
            "source": f"Uniswap_{info['type'].upper()}",
            "poolTotalProportionBps": str(proportion)
        }
        result["route"]["fills"].append(fill)
    
    # Layer-by-layer simulation
    log_collect("\nStarting layer-by-layer simulation...")
    
    # Determine the maximum number of hops across all paths
    max_hops = max(len(row['involved_pool_addresses']) for _, row in df.iterrows())
    log_collect(f"Maximum number of hops: {max_hops}")
    
    # Initialize path state tracking
    path_states = {}
    for i, row in df.iterrows():
        path_id = row['path_id'] if 'path_id' in row else i + 1
        path_input = row['path_input_amount']
        path_tokens = row['path'].split(' -> ')
        
        # Skip paths with zero input
        if path_input == 0:
            continue
        
        path_states[i] = {
            'current_amount': path_input,
            'current_token': path_tokens[0].strip().lower(),
            'tokens': path_tokens,
            'completed': False
        }
    
    # Simulate layer by layer
    for layer in range(max_hops):
        log_collect(f"\n{'='*50}")
        log_collect(f"Simulating layer {layer+1} of {max_hops}")
        
        # 按池子地址分组该层的交换
        layer_swaps = {}  # pool_address -> [(path_idx, token_in, token_out, amount, pool_type)]
        
        for path_idx, state in list(path_states.items()):
            if state['completed']:
                continue
                
            row = df.iloc[path_idx]
            pool_addresses = row['involved_pool_addresses']
            pool_types = row['pools_type_list']
            
            # 检查该路径是否有此层
            if layer < len(pool_addresses):
                # 使用确切的池子地址
                pool_address = pool_addresses[layer].lower() 
                pool_type = pool_types[layer]
                token_in = state['current_token']
                
                # Check if there are enough tokens in the path for this layer
                if layer + 1 >= len(state['tokens']):
                    log_collect(f"Warning: Path {path_idx+1} has insufficient tokens for layer {layer+1}")
                    state['completed'] = True
                    continue
                
                token_out = state['tokens'][layer+1].strip().lower()
                amount = state['current_amount']
                
                if pool_address not in layer_swaps:
                    layer_swaps[pool_address] = []
                
                layer_swaps[pool_address].append((path_idx, token_in, token_out, amount, pool_type))
            else:
                # This path has fewer hops, mark as completed
                state['completed'] = True
        
        # Simulate each pool's swaps for this layer
        for pool_address, swaps in layer_swaps.items():
            log_collect(f"\nSimulating pool {pool_address} with {len(swaps)} swaps")
            
            # Aggregate amounts for the same token pair
            aggregated_swaps = {}  # (token_in, token_out, pool_type) -> [(path_idx, amount)]
            
            for path_idx, token_in, token_out, amount, pool_type in swaps:
                key = (token_in, token_out, pool_type)
                if key not in aggregated_swaps:
                    aggregated_swaps[key] = []
                aggregated_swaps[key].append((path_idx, amount))
            
            # Simulate each unique token pair swap
            for (token_in, token_out, pool_type), path_amounts in aggregated_swaps.items():
                total_in = sum(amount for _, amount in path_amounts)
                log_collect(f"Aggregated swap: {token_in} -> {token_out}, Total amount: {total_in}")
                
                try:
                    if pool_type == 'v2':
                        # Find the pool in v2_pools_data
                        pool = next(
                            (p for p in v2_pools_data if p["poolAddress"].lower() == pool_address.lower()),
                            None
                        )
                        
                        if not pool:
                            log_collect(f"Error: V2 pool {pool_address} not found")
                            continue
                        
                        token0 = pool["poolState"]["poolStaticInfo"]["token0"].lower()
                        token1 = pool["poolState"]["poolStaticInfo"]["token1"].lower()
                        reserve0 = int(pool["poolState"]["poolState"]["tokenBalance0"])
                        reserve1 = int(pool["poolState"]["poolState"]["tokenBalance1"])
                        
                        log_collect(f"V2 Pool reserves: token0={reserve0}, token1={reserve1}")
                        
                        if token_in.lower() == token0 and token_out.lower() == token1:
                            reserve_in, reserve_out = reserve0, reserve1
                        elif token_in.lower() == token1 and token_out.lower() == token0:
                            reserve_in, reserve_out = reserve1, reserve0
                        else:
                            log_collect(f"Error: Token pair {token_in} -> {token_out} is invalid for pool {pool_address}")
                            continue
                        
                        total_out = v2_simulator.get_amount_out(total_in, reserve_in, reserve_out)
                    else:  # v3
                        # Find the pool in v3_pools_data
                        pool = next(
                            (p for p in v3_pools_data if p["poolState"]["poolStaticInfo"]["poolAddress"].lower() == pool_address.lower()),
                            None
                        )
                        
                        if not pool:
                            log_collect(f"Error: V3 pool {pool_address} not found")
                            continue
                        
                        fee = int(pool["poolState"]["poolStaticInfo"]["swapFee"])
                        log_collect(f"V3 Pool fee: {fee}")
                        
                        total_out = v3_simulator.exact_input(
                            Web3.to_checksum_address(token_in),
                            Web3.to_checksum_address(token_out),
                            fee,
                            total_in,
                            block_number,
                            0
                        )
                    
                    log_collect(f"Swap successful! Total output: {total_out}")
                    
                    # Distribute output proportionally to input amounts
                    for path_idx, amount_in in path_amounts:
                        proportion = amount_in / total_in if total_in > 0 else 0
                        amount_out = int(total_out * proportion)
                        
                        # Update path state
                        path_states[path_idx]['current_amount'] = amount_out
                        path_states[path_idx]['current_token'] = token_out
                        
                        log_collect(f"Path {path_idx+1}: Input={amount_in}, Output={amount_out}")
                    
                except Exception as e:
                    log_collect(f"Error simulating swap: {str(e)}")
                    # Mark affected paths as completed (failed)
                    for path_idx, _ in path_amounts:
                        path_states[path_idx]['completed'] = True
    
    # Calculate total output
    total_output_amount = 0
    log_collect("\n" + "="*80)
    log_collect("FINAL SIMULATION RESULTS")
    log_collect("="*80)
    
    for path_idx, state in path_states.items():
        path_id = df.iloc[path_idx]['path_id'] if 'path_id' in df else path_idx + 1
        initial_input = df.iloc[path_idx]['path_input_amount']
        
        if state['current_token'] == result["to"]:
            output = state['current_amount']
            df.at[path_idx, 'output_amount'] = output
            total_output_amount += output
            
            # Calculate path-specific exchange rate
            path_rate = output / initial_input if initial_input > 0 else 0
            
            log_collect(f"Path {path_id}:")
            log_collect(f"  Initial input: {initial_input}")
            log_collect(f"  Final output: {output}")
            log_collect(f"  Exchange rate: {path_rate}")
        else:
            log_collect(f"Path {path_id}:")
            log_collect(f"  Initial input: {initial_input}")
            log_collect(f"  Final output: 0 (did not reach destination token)")
            df.at[path_idx, 'output_amount'] = 0
    
    # Update total output amount
    result["toAmount"] = str(int(total_output_amount))
    
    # Calculate effective exchange rate
    effective_rate = total_output_amount / input_amount if input_amount > 0 else 0
    
    log_collect("\nOverall Results:")
    log_collect(f"  Total input amount: {input_amount}")
    log_collect(f"  Total output amount: {total_output_amount}")
    log_collect(f"  Effective exchange rate: {effective_rate}")
    log_collect("="*80)
    
    # Save result to JSON file
    output_file = f"../results/path_analysis_json/path_simulation_{name}_input_{input_amount}.json"
    with open(output_file, "w") as f:
        json.dump(result, f, indent=2)
    
    log_collect(f"\nSimulation results saved to: {output_file}")
    log_collect(f"Detailed logs saved to: {log_path}")
    
    # Close log file
    log_file.close()
    
    return df, result



def load_token_prices(price_file_path):
    """
    Load token prices from the quotes file
    Returns a dictionary mapping token address to its WETH price
    """
    try:
        with open(price_file_path, 'r') as file:
            price_data = json.load(file)
        
        # Create price mapping
        token_prices = {}
        for token in price_data:
            address = token['address'].lower()
            # Use avg_quote as the price, default to 0 if not available
            try:
                price = float(token['quotes']['avg_quote'])
            except (KeyError, ValueError, TypeError):
                price = 0
            token_prices[address] = price
            
        return token_prices
    except Exception as e:
        print(f"Error loading token prices: {e}")
        return {}


def main():
    # Configuration
    block_number = 21974203
    graph_name = 'usdt_ohm'
    names = ['top_8846_8454_3_10000']
    k = 10000

    node_rpc_url = ETH_NODE_URL

    input_amounts = [40000000000000, 200000000000000, 1000000000000000] 

    price_dict = load_token_prices(f'../../data/prices/block-{block_number}/tokens-quotes.json')
    token_decimals_df = pd.read_csv(f'../../data/token-decimals.csv')
    token_decimals_dict = {}
    for index, row in token_decimals_df.iterrows():
        token_decimals_dict[row['token_address']] = row['decimals']

    node_index = read_in_node_mapping(f'../../graph_construction/graphs/{block_number}_pairs_capacity_{graph_name}_node_map.txt')
    node_name = node_name_mapping(f"../../data/node_name.txt")

    index_name = {}
    for name, addr in node_name.items():
        try:
            index_name[int(node_index[addr])] = name
        except:
            # print('no index:', addr)
            pass


    # File paths
    for name in names:   
        # read in balance file
        balance_file = f'../../graph_construction/graphs/{block_number}_pairs_capacity_{graph_name}_pool_balance.txt'

        with open(balance_file, 'r') as f:
            balance = f.read().splitlines()

        balance_dict = {}
        for line in balance:
            parts = line.split(None, 1)  # Split on first whitespace only
            if len(parts) == 2:
                pool = parts[0]
                balance_str = parts[1].strip()
                
                # Check if the balance is already in list format
                if balance_str.startswith('[') and balance_str.endswith(']'):
                    try:
                        # Parse the string representation of a list
                        balances = eval(balance_str)
                        if isinstance(balances, list):
                            # If it's a valid list, add to balance_dict
                            balance_dict[pool.lower()] = balances
                    except:
                        # If eval fails, try as a simple float
                        try:
                            balance_dict[pool.lower()] = float(balance_str)
                        except:
                            print(f"Warning: Could not parse balance for pool {pool}: {balance_str}")
                else:
                    # Try to parse as a simple float
                    try:
                        balance_dict[pool.lower()] = float(balance_str)
                    except:
                        print(f"Warning: Could not parse balance for pool {pool}: {balance_str}")


        path_analysis_file = f'../results/path_analysis_df/path_analysis_{name}.csv'
        v3_pools_file = f'../../data/prices/block-{block_number}/filtered-v3pools.json'
        v2_pools_file = f'../../data/prices/block-{block_number}/filtered-v2pools.json'
        
        for input_amount in input_amounts:
            df, result = simulate_paths_from_analysis(
                path_analysis_file=path_analysis_file,
                v3_pools_file=v3_pools_file,
                v2_pools_file=v2_pools_file,
                input_amount=input_amount,
                node_rpc_url=node_rpc_url,
                block_number=block_number,
                name=name,
                balance_dict=balance_dict,
                price_dict=price_dict,
                token_decimals_dict = token_decimals_dict
            )


if __name__ == "__main__":
    main()