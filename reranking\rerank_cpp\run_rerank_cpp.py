import os
import pandas as pd

input_graph_dirs = [
    # usdt-ohm
    "1247_548_3",
    # usdt-frax
    "1247_781_3",
    # usdt-cbbtc
    "1247_1162_3",
    # wbtc-spx
    "187_1280_3",
    # wbtc-usdt
    "187_1247_3",
    # spx-peas
    "1280_16_3",
    # reth-wsteth
    "992_742_3",
]

input_amounts = [
    #usdt-ohm
    [94.3810545393768, 94.3810545393768 * 5, 94.3810545393768 * 25],
    # usdt-frax 2e11
    [94.3810545393768, 94.3810545393768 * 5, 94.3810545393768 * 25],
    # usdt-cbbtc
    [94.3810545393768, 94.3810545393768 * 5, 94.3810545393768 * 25],
    # wbtc-spx
    [789.4444139709391, 789.4444139709391 * 5, 789.4444139709391 * 25],
    # wbtc-usdt
    [789.4444139709391, 789.4444139709391 * 5, 789.4444139709391 * 25],
    # spx-peas
    [84.2830041719308, 84.2830041719308 * 5, 84.2830041719308 * 25],
    # reth-wsteth
    [22.46013091186923, 22.46013091186923 * 5, 22.46013091186923 * 25],
]

for i, input_graph_dir in enumerate(input_graph_dirs):
    input_amount_list = input_amounts[i]
    for input_amount in input_amount_list:
        # print(f"Processing {input_graph_dir} with input amount {input_amount}")
        # run rerank_cpp
        # output_file = f"results/{input_graph_dir}_{input_amount}.txt"
        # os.system(f"./rerank.out cpp_graph/{input_graph_dir} {input_amount} > {output_file}")
        output_file = f"results_without_lazy_computation/{input_graph_dir}_{input_amount}.txt"
        # os.system(f"./without_lazy_computation.out cpp_graph/{input_graph_dir} {input_amount} > {output_file}")
    
        with open(output_file, 'r') as file:
           for line in file:
               if 'Time taken:' in line:
                   time_taken = line.split('Time taken: ')[1].strip()
                   time_taken_number = ''.join(filter(str.isdigit, time_taken))
                   print(int(time_taken_number))
        #        
        #    original_scan = 0
        #    no_lazy_compute_scan = 0
        #    actual_scan = 0

        #    for line in file:
        #        if 'original scan:' in line:
        #            original_scan = int(line.split('original scan: ')[1].strip())
        #        elif 'no lazy compute scan:' in line:
        #            no_lazy_compute_scan = int(line.split('no lazy compute scan: ')[1].strip())
        #        elif 'actual scan:' in line:
        #            actual_scan = int(line.split('actual scan: ')[1].strip())

        #    print(original_scan, no_lazy_compute_scan, actual_scan, sep=" ")
