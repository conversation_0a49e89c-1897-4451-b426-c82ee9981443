import os
import json
import pandas as pd
import re
from collections import defaultdict

# =====================
# User-configurable variables
# =====================
BLOCK_NUMBER = 21974203  # Change this to your desired block number
output_dirs = [
    "187_1247_3",
    "187_1280_3",
    "992_742_3",
    "1247_548_3",
    "1247_781_3",
    "1247_1162_3",
    "1280_16_3"
]

for output_dir in output_dirs:
    OUTPUT_DIR = output_dir  # Output directory for generated files
    PATH_NAME = f"all_paths_{OUTPUT_DIR}"  # Change this to your desired path file name (without extension)

    # If you want to use a raw path enumeration file (with node indices):
    PATH_ENUM_FILE = f"results/{PATH_NAME}.txt"  # Set to None to use CSV mode
    NODE_MAP_FILE = f"graphs/{BLOCK_NUMBER}_combined_node_map.txt"  # Node index to token address

    # If you want to use the CSV (token address) mode, set PATH_ENUM_FILE = None
    PATH_CSV = f"results/{PATH_NAME}.txt"
    POOL_WEIGHTS_FILE = f"graphs/{BLOCK_NUMBER}_pool_weights.json"
    POOL_CAPACITIES_FILE = f"graphs/{BLOCK_NUMBER}_pool_capacities.json"

    EDGES_OUT = os.path.join(OUTPUT_DIR, "edges.txt")
    PATHS_OUT = os.path.join(OUTPUT_DIR, "paths.txt")

    # =====================
    # Load data
    # =====================
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)

    with open(POOL_WEIGHTS_FILE, "r") as f:
        pool_weights = json.load(f)
    with open(POOL_CAPACITIES_FILE, "r") as f:
        pool_capacities = json.load(f)

    # =====================
    # Helper: Load node map (index -> token address)
    # =====================
    def load_node_map(node_map_file):
        node_map = {}
        with open(node_map_file, "r") as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) >= 2:
                    node_map[int(parts[0])] = parts[1].lower()
        return node_map

    # =====================
    # Parse paths
    # =====================
    paths_token_sequences = []
    if PATH_ENUM_FILE and os.path.exists(PATH_ENUM_FILE):
        # --- RAW PATH ENUMERATION MODE ---
        print(f"Parsing raw path file: {PATH_ENUM_FILE}")
        node_map = load_node_map(NODE_MAP_FILE)
        with open(PATH_ENUM_FILE, "r") as f:
            content = f.read()
        # Extract all lines that look like a path (sequence of numbers)
        path_blocks = re.findall(r"Path \d+:\s*([\d\s]+)", content)
        for path_str in path_blocks:
            node_indices = [int(x) for x in path_str.strip().split()]
            token_seq = [node_map[idx] for idx in node_indices]
            paths_token_sequences.append(token_seq)
    else:
        # --- CSV MODE (token addresses) ---
        print(f"Parsing CSV path file: {PATH_CSV}")
        df = pd.read_csv(PATH_CSV)
        for _, row in df.iterrows():
            tokens = eval(row['token_sequence'])
            paths_token_sequences.append([t.lower() for t in tokens])

    # =====================
    # Build EdgeGroups
    # =====================
    group_map = {}  # (token_in, token_out) -> group_idx
    edge_map = {}   # (token_in, token_out, pool_addr, tick) -> edge_id
    edge_groups = []  # List of lists of (weight, capacity, edge_id)
    edge_id_counter = 0

    # First, collect all unique hops and all pool+tick options for each
    hop_to_edges = defaultdict(list)
    for tokens in paths_token_sequences:
        for i in range(len(tokens) - 1):
            token_in = tokens[i]
            token_out = tokens[i+1]
            pair_key = f"{token_in}_{token_out}"
            if pair_key in pool_weights:
                for pool_addr, tick_list in pool_weights[pair_key].items():
                    for tick_data in tick_list:
                        tick = tick_data.get('tick')
                        weight = tick_data.get('weight', float('inf'))
                        pool_type = tick_data.get('pool_type', 'v3')
                        # Find capacity for this tick
                        capacity = 0
                        if pair_key in pool_capacities and pool_addr in pool_capacities[pair_key]:
                            for cap_data in pool_capacities[pair_key][pool_addr]:
                                if cap_data.get('tick') == tick:
                                    capacity = cap_data.get('capacity', 0)
                                    break
                        hop_to_edges[(token_in, token_out)].append((weight, capacity, pool_addr, tick, pool_type))

    # Assign group indices
    for idx, hop in enumerate(hop_to_edges.keys()):
        group_map[hop] = idx
        edge_groups.append([])

    # Assign edge IDs and fill edge_groups
    for hop, edges in hop_to_edges.items():
        group_idx = group_map[hop]
        for (weight, capacity, pool_addr, tick, pool_type) in edges:
            edge_id = edge_id_counter
            edge_map[(hop[0], hop[1], pool_addr, tick)] = edge_id
            edge_groups[group_idx].append((weight, capacity, edge_id))
            edge_id_counter += 1

    # =====================
    # Build paths (as group indices)
    # =====================
    paths = []
    missing_hops = set()
    skipped_paths = 0
    for tokens in paths_token_sequences:
        path_group_indices = []
        valid = True
        for i in range(len(tokens) - 1):
            token_in = tokens[i]
            token_out = tokens[i+1]
            group_idx = group_map.get((token_in, token_out))
            if group_idx is None:
                missing_hops.add((token_in, token_out))
                valid = False
                break
            path_group_indices.append(group_idx)
        if valid:
            paths.append(path_group_indices)
        else:
            skipped_paths += 1

    # =====================
    # Write each edge group to a separate file
    # =====================
    group_file_paths = []
    for idx, group in enumerate(edge_groups):
        group_file = os.path.join(OUTPUT_DIR, f"edges_group_{idx}.txt")
        group_file_paths.append(group_file)
        with open(group_file, "w") as f:
            for weight, capacity, edge_id in group:
                f.write(f"{weight} {capacity} {edge_id}\n")

    # =====================
    # Write paths.txt
    # =====================
    with open(PATHS_OUT, "w") as f:
        for path in paths:
            f.write(" ".join(str(idx) for idx in path) + "\n")

    print(f"Wrote {len(group_file_paths)} edge group files to {OUTPUT_DIR} (edges_group_*.txt)")
    print(f"Wrote paths file to {PATHS_OUT}")
    if missing_hops:
        print(f"WARNING: {len(missing_hops)} missing hops encountered, {skipped_paths} paths skipped.")
        for hop in missing_hops:
            print(f"  Missing hop: {hop[0]} -> {hop[1]}") 