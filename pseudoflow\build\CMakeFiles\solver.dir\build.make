# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/crypto-arbitrage-max-flow/networkx

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/crypto-arbitrage-max-flow/networkx/build

# Include any dependencies generated for this target.
include CMakeFiles/solver.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/solver.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/solver.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/solver.dir/flags.make

CMakeFiles/solver.dir/main.cpp.o: CMakeFiles/solver.dir/flags.make
CMakeFiles/solver.dir/main.cpp.o: ../main.cpp
CMakeFiles/solver.dir/main.cpp.o: CMakeFiles/solver.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/crypto-arbitrage-max-flow/networkx/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/solver.dir/main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/solver.dir/main.cpp.o -MF CMakeFiles/solver.dir/main.cpp.o.d -o CMakeFiles/solver.dir/main.cpp.o -c /home/<USER>/crypto-arbitrage-max-flow/networkx/main.cpp

CMakeFiles/solver.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/solver.dir/main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/crypto-arbitrage-max-flow/networkx/main.cpp > CMakeFiles/solver.dir/main.cpp.i

CMakeFiles/solver.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/solver.dir/main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/crypto-arbitrage-max-flow/networkx/main.cpp -o CMakeFiles/solver.dir/main.cpp.s

CMakeFiles/solver.dir/network_simplex.cpp.o: CMakeFiles/solver.dir/flags.make
CMakeFiles/solver.dir/network_simplex.cpp.o: ../network_simplex.cpp
CMakeFiles/solver.dir/network_simplex.cpp.o: CMakeFiles/solver.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/crypto-arbitrage-max-flow/networkx/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/solver.dir/network_simplex.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/solver.dir/network_simplex.cpp.o -MF CMakeFiles/solver.dir/network_simplex.cpp.o.d -o CMakeFiles/solver.dir/network_simplex.cpp.o -c /home/<USER>/crypto-arbitrage-max-flow/networkx/network_simplex.cpp

CMakeFiles/solver.dir/network_simplex.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/solver.dir/network_simplex.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/crypto-arbitrage-max-flow/networkx/network_simplex.cpp > CMakeFiles/solver.dir/network_simplex.cpp.i

CMakeFiles/solver.dir/network_simplex.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/solver.dir/network_simplex.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/crypto-arbitrage-max-flow/networkx/network_simplex.cpp -o CMakeFiles/solver.dir/network_simplex.cpp.s

CMakeFiles/solver.dir/pseudoflow.cpp.o: CMakeFiles/solver.dir/flags.make
CMakeFiles/solver.dir/pseudoflow.cpp.o: ../pseudoflow.cpp
CMakeFiles/solver.dir/pseudoflow.cpp.o: CMakeFiles/solver.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/crypto-arbitrage-max-flow/networkx/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/solver.dir/pseudoflow.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/solver.dir/pseudoflow.cpp.o -MF CMakeFiles/solver.dir/pseudoflow.cpp.o.d -o CMakeFiles/solver.dir/pseudoflow.cpp.o -c /home/<USER>/crypto-arbitrage-max-flow/networkx/pseudoflow.cpp

CMakeFiles/solver.dir/pseudoflow.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/solver.dir/pseudoflow.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/crypto-arbitrage-max-flow/networkx/pseudoflow.cpp > CMakeFiles/solver.dir/pseudoflow.cpp.i

CMakeFiles/solver.dir/pseudoflow.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/solver.dir/pseudoflow.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/crypto-arbitrage-max-flow/networkx/pseudoflow.cpp -o CMakeFiles/solver.dir/pseudoflow.cpp.s

# Object files for target solver
solver_OBJECTS = \
"CMakeFiles/solver.dir/main.cpp.o" \
"CMakeFiles/solver.dir/network_simplex.cpp.o" \
"CMakeFiles/solver.dir/pseudoflow.cpp.o"

# External object files for target solver
solver_EXTERNAL_OBJECTS =

solver: CMakeFiles/solver.dir/main.cpp.o
solver: CMakeFiles/solver.dir/network_simplex.cpp.o
solver: CMakeFiles/solver.dir/pseudoflow.cpp.o
solver: CMakeFiles/solver.dir/build.make
solver: /usr/lib/x86_64-linux-gnu/liblemon.so
solver: CMakeFiles/solver.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/crypto-arbitrage-max-flow/networkx/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking CXX executable solver"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/solver.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/solver.dir/build: solver
.PHONY : CMakeFiles/solver.dir/build

CMakeFiles/solver.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/solver.dir/cmake_clean.cmake
.PHONY : CMakeFiles/solver.dir/clean

CMakeFiles/solver.dir/depend:
	cd /home/<USER>/crypto-arbitrage-max-flow/networkx/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/crypto-arbitrage-max-flow/networkx /home/<USER>/crypto-arbitrage-max-flow/networkx /home/<USER>/crypto-arbitrage-max-flow/networkx/build /home/<USER>/crypto-arbitrage-max-flow/networkx/build /home/<USER>/crypto-arbitrage-max-flow/networkx/build/CMakeFiles/solver.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/solver.dir/depend

