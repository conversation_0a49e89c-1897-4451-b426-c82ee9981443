#ifndef PSEUDOFLOW_H
#define PSEUDOFLOW_H

#include <iostream>
#include <fstream>
#include <sstream>
#include <vector>
#include <string>
#include <chrono>
#include <algorithm>
#include <cstdlib>

// Type aliases for convenience.
using uint = unsigned int;
using llint = long long int;
using ullint = unsigned long long int;

// Forward declarations.
class Node;
class Arc;

//---------------------------------------------------------------------
// Class representing an arc in the network.
struct Arc {
    Node* from = nullptr;
    Node* to = nullptr;
    uint flow = 0;
    uint capacity = 0;
    uint direction = 1; // 1 or 0 to indicate “upward”/“downward”
};

//---------------------------------------------------------------------
// Class representing a node in the network.
struct Node {
    uint visited = 0;
    uint numAdjacent = 0;
    uint number = 0;    // 1-based node number
    uint label = 0;
    int excess = 0;

    Node* parent = nullptr;
    std::vector<Node*> children;
    size_t nextScanIndex = 0;

    std::vector<Arc*> outOfTree;
    uint nextArc = 0;
    Arc* arcToParent = nullptr;

    Node* next = nullptr;
};

//---------------------------------------------------------------------
// Class representing a “bucket” of strong roots.
struct Root {
    std::vector<Node*> nodes;
};

//---------------------------------------------------------------------
// Main class that encapsulates the pseudoflow (min cut/max flow) solver.
class Pseudoflow {
public:
    // Variables
    uint numNodes = 0;
    uint numArcs = 0;
    uint source = 0;
    uint sink = 0;
    uint highestStrongLabel = 1;

    // Constructor
    Pseudoflow();

    // Reads the input file, builds the graph, and stores the source and sink.
    void readGraphFromFile(const std::string &filename, uint s, uint t);

    // Performs the initial flow push from the source and sink.
    void simpleInitialization();

    // Runs the pseudoflow Phase 1.
    void pseudoflowPhase1();

    // Computes and returns the min cut value.
    llint mincut();

private:
    // Helper functions
    void addRelationship(Node* newParent, Node* child);
    void breakRelationship(Node* oldParent, Node* child);
    void merge(Node* parent, Node* child, Arc* newArc);
    void pushUpward(Arc* currentArc, Node* child, Node* parent, uint resCap);
    void pushDownward(Arc* currentArc, Node* child, Node* parent, uint flow);
    void pushExcess(Node* strongRoot);
    Arc* findWeakNode(Node* strongNode, Node*& weakNode);
    void checkChildren(Node* curNode);
    void processRoot(Node* strongRoot);
    Node* getHighestStrongRoot();
    void liftAll(Node* rootNode);

    std::vector<Node> nodes;
    std::vector<Arc> arcs;
    std::vector<Root> strongRoots;
    std::vector<uint> labelCount;
};

#endif // PSEUDOFLOW_H