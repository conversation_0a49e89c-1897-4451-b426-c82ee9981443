#include "network_simplex.h"

using namespace lemon;
using uint = unsigned int;

    // Constructor: initialize maps with graph 'g'
Mincost::Mincost() : capMap(g), costMap(g), supplyMap(g), flowMap(g) { }

// Reads the input file, builds the graph, and stores the source and sink.
// The file should contain lines of the format:
//   from_node to_node capacity cost
// with nodes assumed to be 0-based.
void Mincost::readGraphFromFile(const std::string &filename, uint s, uint t) {
    sId_ = s;
    tId_ = t;
    
    std::ifstream infile(filename);
    if (!infile) {
        std::cerr << "Error: cannot open file " << filename << "\n";
        exit(1);
    }
    
    int from, to;
    int capacity, cost;
    int maxNodeId = -1;
    std::string line;
    
    // First pass: determine the maximum node ID
    while (std::getline(infile, line)) {
        std::istringstream iss(line);
        if (iss >> from >> to >> capacity >> cost) {
            maxNodeId = std::max({maxNodeId, from, to});
        }
    }
    if (maxNodeId < 0) {
        std::cerr << "Error: input file appears empty or invalid.\n";
        exit(1);
    }
    int n = maxNodeId + 1;
    
    // Create nodes in the graph and store them in nodeVector.
    nodeVector.resize(n);
    for (int i = 0; i < n; ++i) {
        nodeVector[i] = g.addNode();
    }
    
    // Rewind the file for the second pass.
    infile.clear();
    infile.seekg(0);
    
    // Second pass: create arcs and assign capacity and cost values.
    while (std::getline(infile, line)) {
        std::istringstream iss(line);
        if (iss >> from >> to >> capacity >> cost) {
            ListDigraph::Arc a = g.addArc(nodeVector[from], nodeVector[to]);
            capMap[a] = capacity;
            costMap[a] = cost;
        }
    }
    infile.close();
}

// Runs the network simplex with the given supply value (i.e. flow to send)
// and returns the minimum total cost.
long long Mincost::mincost(long long supplyValue) {
    // Initialize supplies for all nodes to 0.
    for (size_t i = 0; i < nodeVector.size(); ++i) {
        supplyMap[nodeVector[i]] = 0;
    }
    // Set the supply at the source and sink.
    supplyMap[nodeVector[sId_]] = supplyValue;
    supplyMap[nodeVector[tId_]] = -supplyValue;
    
    // Set up and run the network simplex.
    // (For LEMON 1.3.1 use "upperMap()" for the capacity map.)
    NetworkSimplex<ListDigraph> ns(g);
    ns.upperMap(capMap)
        .costMap(costMap)
        .supplyMap(supplyMap);
        
    // The run() method returns a ProblemType.
    NetworkSimplex<ListDigraph>::ProblemType status = ns.run();
    
    if (status == NetworkSimplex<ListDigraph>::OPTIMAL) {
        std::cout << "Optimal solution found.\n";
    } else if (status == NetworkSimplex<ListDigraph>::INFEASIBLE) {
        std::cout << "Problem is infeasible!\n";
        return 0;
    } else if (status == NetworkSimplex<ListDigraph>::UNBOUNDED) {
        std::cout << "Problem is unbounded!\n";
        return 0;
    } else {
        std::cout << "Unknown error!\n";
        return 0;
    }
    
    long long totalCost = ns.totalCost();
    std::cout << "Minimum total cost = " << totalCost << "\n";
    
    // Retrieve the flow values for each arc.
    ns.flowMap(flowMap);
    
    return totalCost;
}

// Prints the flow on each arc (only arcs with positive flow).
void Mincost::printFlow() {
    for (ListDigraph::ArcIt a(g); a != INVALID; ++a) {
        int f = flowMap[a];
        if (f > 0) {
            // Determine the indices of the source and target nodes.
            int fromId = std::find(nodeVector.begin(), nodeVector.end(), g.source(a)) - nodeVector.begin();
            int toId   = std::find(nodeVector.begin(), nodeVector.end(), g.target(a)) - nodeVector.begin();
            std::cout << "Flow on arc (" << fromId << "->" << toId 
                        << "): " << f 
                        << " (cap " << capMap[a]
                        << ", cost " << costMap[a] << ")\n";
        }
    }
}