{"cells": [{"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import math\n", "from collections import defaultdict, deque\n", "import heapq\n", "import glob\n", "import os\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import networkx as nx\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def load_data_from_json(file_path):\n", "    \"\"\"Load data from a JSON file\"\"\"\n", "    with open(file_path, 'r') as file:\n", "        return json.load(file)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["21974203 698 1766\n", "21974830 621 1765\n", "21976094 807 1705\n"]}], "source": ["block_nums = [21974203, 21974830, 21976094]\n", "for block_num in block_nums:\n", "    v2 = load_data_from_json(f'../data/prices/block-{block_num}/filtered-v2pools.json')\n", "    v3 = load_data_from_json(f'../data/prices/block-{block_num}/filtered-v3pools.json')\n", "    \n", "    print(block_num, len(v2), len(v3))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def load_token_prices(price_file_path):\n", "    \"\"\"\n", "    Load token prices from the quotes file\n", "    Returns a dictionary mapping token address to its WETH price\n", "    \"\"\"\n", "    try:\n", "        with open(price_file_path, 'r') as file:\n", "            price_data = json.load(file)\n", "        \n", "        # Create price mapping\n", "        token_prices = {}\n", "        for token in price_data:\n", "            address = token['address'].lower()\n", "            # Use avg_quote as the price, default to 0 if not available\n", "            try:\n", "                price = float(token['quotes']['avg_quote'])\n", "            except (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>E<PERSON>r):\n", "                price = 0\n", "            token_prices[address] = price\n", "            \n", "        return token_prices\n", "    except Exception as e:\n", "        print(f\"Error loading token prices: {e}\")\n", "        return {}"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>block_num</th>\n", "      <th>total_unique_tokens</th>\n", "      <th>tokens_without_price</th>\n", "      <th>tokens_without_price_pct</th>\n", "      <th>v2_tokens_without_price</th>\n", "      <th>v2_tokens_without_price_pct</th>\n", "      <th>v3_tokens_without_price</th>\n", "      <th>v3_tokens_without_price_pct</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21974203</td>\n", "      <td>1823</td>\n", "      <td>77</td>\n", "      <td>4.22</td>\n", "      <td>22</td>\n", "      <td>3.14</td>\n", "      <td>58</td>\n", "      <td>4.52</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>21974830</td>\n", "      <td>1756</td>\n", "      <td>79</td>\n", "      <td>4.50</td>\n", "      <td>24</td>\n", "      <td>3.80</td>\n", "      <td>57</td>\n", "      <td>4.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>21976094</td>\n", "      <td>1895</td>\n", "      <td>73</td>\n", "      <td>3.85</td>\n", "      <td>22</td>\n", "      <td>2.74</td>\n", "      <td>54</td>\n", "      <td>4.30</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   block_num  total_unique_tokens  tokens_without_price  \\\n", "0   21974203                 1823                    77   \n", "1   21974830                 1756                    79   \n", "2   21976094                 1895                    73   \n", "\n", "   tokens_without_price_pct  v2_tokens_without_price  \\\n", "0                      4.22                       22   \n", "1                      4.50                       24   \n", "2                      3.85                       22   \n", "\n", "   v2_tokens_without_price_pct  v3_tokens_without_price  \\\n", "0                         3.14                       58   \n", "1                         3.80                       57   \n", "2                         2.74                       54   \n", "\n", "   v3_tokens_without_price_pct  \n", "0                         4.52  \n", "1                         4.45  \n", "2                         4.30  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["block_nums = [21974203, 21974830, 21976094]\n", "v2_pools = {}\n", "v3_pools = {}\n", "price_results = []\n", "\n", "for block_num in block_nums:\n", "    v2 = load_data_from_json(f'../data/prices/block-{block_num}/filtered-v2pools.json')\n", "    v3 = load_data_from_json(f'../data/prices/block-{block_num}/filtered-v3pools.json')\n", "    prices = load_token_prices(f'../data/prices/block-{block_num}/tokens-quotes.json')\n", "    v2_pools[block_num] = v2\n", "    v3_pools[block_num] = v3\n", "    # Extract unique tokens from v2 pools\n", "    v2_tokens = set()\n", "    for pool in v2:\n", "        static_info = pool['poolState']['poolStaticInfo']\n", "        v2_tokens.add(static_info['token0'].lower())\n", "        v2_tokens.add(static_info['token1'].lower())\n", "    \n", "    # Extract unique tokens from v3 pools\n", "    v3_tokens = set()\n", "    for pool in v3:\n", "        static_info = pool['poolState']['poolStaticInfo']\n", "        v3_tokens.add(static_info['token0'].lower())\n", "        v3_tokens.add(static_info['token1'].lower())\n", "    \n", "    # Combine all unique tokens\n", "    all_tokens = v2_tokens.union(v3_tokens)\n", "    \n", "    # Count tokens without price\n", "    tokens_without_price = 0\n", "    v2_tokens_without_price = 0\n", "    v3_tokens_without_price = 0\n", "    \n", "    for token in all_tokens:\n", "        if token not in prices or prices[token] == 0:\n", "            tokens_without_price += 1\n", "            \n", "    for token in v2_tokens:\n", "        if token not in prices or prices[token] == 0:\n", "            v2_tokens_without_price += 1\n", "            \n", "    for token in v3_tokens:\n", "        if token not in prices or prices[token] == 0:\n", "            v3_tokens_without_price += 1\n", "    \n", "    price_results.append({\n", "        'block_num': block_num,\n", "        'total_unique_tokens': len(all_tokens),\n", "        'tokens_without_price': tokens_without_price,\n", "        'tokens_without_price_pct': round(tokens_without_price / len(all_tokens) * 100, 2),\n", "        'v2_tokens_without_price': v2_tokens_without_price,\n", "        'v2_tokens_without_price_pct': round(v2_tokens_without_price / len(v2_tokens) * 100, 2),\n", "        'v3_tokens_without_price': v3_tokens_without_price,\n", "        'v3_tokens_without_price_pct': round(v3_tokens_without_price / len(v3_tokens) * 100, 2)\n", "    })\n", "\n", "# Create a DataFrame for better visualization\n", "price_results_df = pd.DataFrame(price_results)\n", "price_results_df"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 63 benchmark files\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>filename</th>\n", "      <th>block_number</th>\n", "      <th>from_token</th>\n", "      <th>to_token</th>\n", "      <th>from_token_name</th>\n", "      <th>to_token_name</th>\n", "      <th>pool_addresses</th>\n", "      <th>exchanges</th>\n", "      <th>from_amount</th>\n", "      <th>to_amount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>21974203_usdt-frax_200000000000.json</td>\n", "      <td>21974203</td>\n", "      <td>******************************************</td>\n", "      <td>******************************************</td>\n", "      <td>USDT</td>\n", "      <td>FRAX</td>\n", "      <td>[******************************************, 0...</td>\n", "      <td>[Uniswap_V3, Uniswap_V3, Uniswap_V3, Uniswap_V3]</td>\n", "      <td>200000000000</td>\n", "      <td>200583152656090259415807</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>21974203_wbtc-spx_60000000000.json</td>\n", "      <td>21974203</td>\n", "      <td>******************************************</td>\n", "      <td>******************************************</td>\n", "      <td>WBTC</td>\n", "      <td>SPX</td>\n", "      <td>[******************************************, 0...</td>\n", "      <td>[Uniswap_V3, Uniswap_V3, Uniswap_V3, Uniswap_V...</td>\n", "      <td>60000000000</td>\n", "      <td>1080276420915361</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>21974203_spx-peas_200000000000000.json</td>\n", "      <td>21974203</td>\n", "      <td>******************************************</td>\n", "      <td>******************************************</td>\n", "      <td>SPX</td>\n", "      <td>PEAS</td>\n", "      <td>[******************************************, 0...</td>\n", "      <td>[Uniswap_V3, Uniswap_V2, Uniswap_V3, Uniswap_V...</td>\n", "      <td>200000000000000</td>\n", "      <td>166564453758174295778158</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>21974203_reth-wsteth_100000000000000000000.json</td>\n", "      <td>21974203</td>\n", "      <td>******************************************</td>\n", "      <td>******************************************</td>\n", "      <td>rETH</td>\n", "      <td>wstETH</td>\n", "      <td>[******************************************, 0...</td>\n", "      <td>[Uniswap_V3, Uniswap_V3, Uniswap_V3]</td>\n", "      <td>100000000000000000000</td>\n", "      <td>93938198144672799714</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>21974203_wbtc-spx_10000000000.json</td>\n", "      <td>21974203</td>\n", "      <td>******************************************</td>\n", "      <td>******************************************</td>\n", "      <td>WBTC</td>\n", "      <td>SPX</td>\n", "      <td>[******************************************, 0...</td>\n", "      <td>[Uniswap_V3, Uniswap_V3, Uniswap_V3, Uniswap_V...</td>\n", "      <td>10000000000</td>\n", "      <td>770767285841052</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                           filename  block_number  \\\n", "3              21974203_usdt-frax_200000000000.json      21974203   \n", "2                21974203_wbtc-spx_60000000000.json      21974203   \n", "5            21974203_spx-peas_200000000000000.json      21974203   \n", "8   21974203_reth-wsteth_100000000000000000000.json      21974203   \n", "11               21974203_wbtc-spx_10000000000.json      21974203   \n", "\n", "                                    from_token  \\\n", "3   ******************************************   \n", "2   ******************************************   \n", "5   ******************************************   \n", "8   ******************************************   \n", "11  ******************************************   \n", "\n", "                                      to_token from_token_name to_token_name  \\\n", "3   ******************************************            USDT          FRAX   \n", "2   ******************************************            WBTC           SPX   \n", "5   ******************************************             SPX          PEAS   \n", "8   ******************************************            rETH        wstETH   \n", "11  ******************************************            WBTC           SPX   \n", "\n", "                                       pool_addresses  \\\n", "3   [******************************************, 0...   \n", "2   [******************************************, 0...   \n", "5   [******************************************, 0...   \n", "8   [******************************************, 0...   \n", "11  [******************************************, 0...   \n", "\n", "                                            exchanges            from_amount  \\\n", "3    [Uniswap_V3, Uniswap_V3, Uniswap_V3, Uniswap_V3]           200000000000   \n", "2   [Uniswap_V3, Uniswap_V3, Uniswap_V3, Uniswap_V...            60000000000   \n", "5   [Uniswap_V3, Uniswap_V2, Uniswap_V3, Uniswap_V...        200000000000000   \n", "8                [Uniswap_V3, Uniswap_V3, Uniswap_V3]  100000000000000000000   \n", "11  [Uniswap_V3, Uniswap_V3, Uniswap_V3, Uniswap_V...            10000000000   \n", "\n", "                   to_amount  \n", "3   200583152656090259415807  \n", "2           1080276420915361  \n", "5   166564453758174295778158  \n", "8       93938198144672799714  \n", "11           770767285841052  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Path to benchmark files\n", "benchmark_path = \"../data/benchmark/*.json\"\n", "\n", "# Dictionary to store benchmark data\n", "benchmark_data = {}\n", "\n", "# Read all benchmark files\n", "benchmark_files = glob.glob(benchmark_path)\n", "print(f\"Found {len(benchmark_files)} benchmark files\")\n", "\n", "# Process each file\n", "data_rows = []\n", "for file_path in benchmark_files:\n", "    # Extract filename without path and extension\n", "    filename = os.path.basename(file_path)\n", "    \n", "    # Read the JSON file\n", "    with open(file_path, 'r') as f:\n", "        data = json.load(f)\n", "    \n", "    # Get token names from route tokens if available\n", "    from_token_name = \"\"\n", "    to_token_name = \"\"\n", "    if 'route' in data and 'tokens' in data['route']:\n", "        tokens = data['route']['tokens']\n", "        for token in tokens:\n", "            if token['address'].lower() == data.get('from', '').lower():\n", "                from_token_name = token.get('symbol', '')\n", "            if token['address'].lower() == data.get('to', '').lower():\n", "                to_token_name = token.get('symbol', '')\n", "    \n", "    # Extract pool addresses and exchanges from fills\n", "    pool_addresses = []\n", "    exchanges = []\n", "    if 'route' in data and 'fills' in data['route']:\n", "        for fill in data['route']['fills']:\n", "            if isinstance(fill, dict):\n", "                if 'pool' in fill:\n", "                    pool_addresses.append(fill['pool'])\n", "                if 'source' in fill:\n", "                    exchanges.append(fill['source'])\n", "    \n", "    # Extract required information\n", "    row = {\n", "        'filename': filename,\n", "        'block_number': data.get('blockNumber'),\n", "        'from_token': data.get('from'),\n", "        'to_token': data.get('to'),\n", "        'from_token_name': from_token_name,\n", "        'to_token_name': to_token_name,\n", "        'pool_addresses': pool_addresses,\n", "        'exchanges': exchanges,\n", "        'from_amount': data.get('fromAmount'),\n", "        'to_amount': data.get('toAmount')\n", "    }\n", "    \n", "    # Add to data rows\n", "    data_rows.append(row)\n", "    \n", "    # Store in dictionary with filename as key\n", "    benchmark_data[filename] = data\n", "\n", "# Create DataFrame for easier analysis\n", "benchmark_df = pd.DataFrame(data_rows)\n", "\n", "benchmark_df.sort_values(by='block_number', ascending=True).head()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded prices for 1895 tokens from block 21976094\n", "Loaded prices for 1756 tokens from block 21974830\n", "Loaded prices for 1823 tokens from block 21974203\n"]}], "source": ["# Get unique block numbers from the benchmark data\n", "block_numbers = benchmark_df['block_number'].unique()\n", "\n", "# Dictionary to store token prices for each block\n", "block_token_prices = {}\n", "\n", "# Load prices for each block\n", "for block_number in block_numbers:\n", "    price_file_path = f'../data/prices/block-{block_number}/tokens-quotes.json'\n", "    token_prices = load_token_prices(price_file_path)\n", "    block_token_prices[block_number] = token_prices\n", "    print(f\"Loaded prices for {len(token_prices)} tokens from block {block_number}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Calculate pool balance"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def calculate_pool_liquidity_value(pool, token_prices):\n", "    try:\n", "        # Extract pool data\n", "        pool_state = pool['poolState']['poolState']\n", "        pool_static_info = pool['poolState']['poolStaticInfo']\n", "        \n", "        # Get token addresses and convert to lowercase for consistent comparison\n", "        token0_address = pool_static_info['token0'].lower()\n", "        token1_address = pool_static_info['token1'].lower()\n", "        \n", "        # Get token decimals\n", "        token0_decimals = int(pool_static_info['token0Decimals'])\n", "        token1_decimals = int(pool_static_info['token1Decimals'])\n", "        \n", "        # Get token balances and convert to human-readable format\n", "        token0_balance = float(pool_state['balance0']) / (10 ** token0_decimals)\n", "        token1_balance = float(pool_state['balance1']) / (10 ** token1_decimals)\n", "        \n", "        # Get token prices in ETH\n", "        token0_price_eth = token_prices.get(token0_address, 0)\n", "        # print(token0_balance, token0_price_eth)\n", "        token1_price_eth = token_prices.get(token1_address, 0)\n", "        # print(token1_balance, token1_price_eth)\n", "        \n", "        # Calculate ETH value of liquidity\n", "        liquidity_value_eth = (token0_balance * token0_price_eth) + (token1_balance * token1_price_eth)\n", "        \n", "        \n", "        return {\n", "            'pool_address': pool_state['poolAddress'],\n", "            'token0': pool_static_info['token0Symbol'],\n", "            'token1': pool_static_info['token1Symbol'],\n", "            'token0_address': token0_address,\n", "            'token1_address': token1_address,\n", "            'token0_balance': token0_balance,\n", "            'token1_balance': token1_balance,\n", "            'liquidity_value_eth': liquidity_value_eth  # Note: This is actually in ETH, keeping the name for compatibility\n", "        }\n", "    except Exception as e:\n", "        # Return minimal info if there's an error\n", "        print(e)\n", "        return {\n", "            'pool_address': pool.get('poolAddress', 'unknown'),\n", "            'error': str(e),\n", "            'liquidity_value_eth': 0  # This is actually in ETH\n", "        }"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Top 10 Uniswap V3 pools by liquidity value (in ETH):\n", "1. VVV-WETH Pool: 90,145.069997 ETH\n", "2. WBTC-USDC Pool: 74,368.779791 ETH\n", "3. USDC-WETH Pool: 68,152.279084 ETH\n", "4. WETH-USDT Pool: 50,222.590012 ETH\n", "5. WBTC-WETH Pool: 35,958.701212 ETH\n", "6. USDC-WETH Pool: 33,235.112275 ETH\n", "7. beraSTONE-WETH Pool: 32,872.872325 ETH\n", "8. DAI-USDC Pool: 29,080.342412 ETH\n", "9. USDC-USDT Pool: 21,977.490668 ETH\n", "10. WBTC-cbBTC Pool: 19,161.076638 ETH\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>pool_address</th>\n", "      <th>token0</th>\n", "      <th>token1</th>\n", "      <th>token0_address</th>\n", "      <th>token1_address</th>\n", "      <th>token0_balance</th>\n", "      <th>token1_balance</th>\n", "      <th>liquidity_value_eth</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>******************************************</td>\n", "      <td>VVV</td>\n", "      <td>WETH</td>\n", "      <td>******************************************</td>\n", "      <td>******************************************</td>\n", "      <td>6.000000e+07</td>\n", "      <td>4.795395e-03</td>\n", "      <td>90145.069997</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>******************************************</td>\n", "      <td>WBTC</td>\n", "      <td>USDC</td>\n", "      <td>******************************************</td>\n", "      <td>******************************************</td>\n", "      <td>1.566829e+03</td>\n", "      <td>2.330529e+07</td>\n", "      <td>74368.779791</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>******************************************</td>\n", "      <td>USDC</td>\n", "      <td>WETH</td>\n", "      <td>******************************************</td>\n", "      <td>******************************************</td>\n", "      <td>4.522947e+07</td>\n", "      <td>4.718307e+04</td>\n", "      <td>68152.279084</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>******************************************</td>\n", "      <td>WETH</td>\n", "      <td>USDT</td>\n", "      <td>******************************************</td>\n", "      <td>******************************************</td>\n", "      <td>4.297963e+04</td>\n", "      <td>1.578303e+07</td>\n", "      <td>50222.590012</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>******************************************</td>\n", "      <td>WBTC</td>\n", "      <td>WETH</td>\n", "      <td>******************************************</td>\n", "      <td>******************************************</td>\n", "      <td>4.430227e+01</td>\n", "      <td>3.428366e+04</td>\n", "      <td>35958.701212</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                 pool_address token0 token1  \\\n", "0  ******************************************    VVV   WETH   \n", "1  ******************************************   WBTC   USDC   \n", "2  ******************************************   USDC   WETH   \n", "3  ******************************************   WETH   USDT   \n", "4  ******************************************   WBTC   WETH   \n", "\n", "                               token0_address  \\\n", "0  ******************************************   \n", "1  ******************************************   \n", "2  ******************************************   \n", "3  ******************************************   \n", "4  ******************************************   \n", "\n", "                               token1_address  token0_balance  token1_balance  \\\n", "0  ******************************************    6.000000e+07    4.795395e-03   \n", "1  ******************************************    1.566829e+03    2.330529e+07   \n", "2  ******************************************    4.522947e+07    4.718307e+04   \n", "3  ******************************************    4.297963e+04    1.578303e+07   \n", "4  ******************************************    4.430227e+01    3.428366e+04   \n", "\n", "   liquidity_value_eth  \n", "0         90145.069997  \n", "1         74368.779791  \n", "2         68152.279084  \n", "3         50222.590012  \n", "4         35958.701212  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# Calculate liquidity of all ticks for all pools for v3\n", "liquidity_values = []\n", "for pool in v3_pools[block_num]:\n", "    if pool['poolType'] == 'uniswap_v3_like_pool':\n", "        liquidity_info = calculate_pool_liquidity_value(pool, prices)\n", "        liquidity_values.append(liquidity_info)\n", "\n", "# Sort pools by liquidity value (descending)\n", "ranked_pools = sorted(liquidity_values, key=lambda x: x['liquidity_value_eth'], reverse=True)\n", "\n", "# Display top 10 pools by liquidity\n", "print(\"Top 10 Uniswap V3 pools by liquidity value (in ETH):\")\n", "for i, pool in enumerate(ranked_pools[:10]):\n", "    print(f\"{i+1}. {pool['token0']}-{pool['token1']} Pool: {pool['liquidity_value_eth']:,.6f} ETH\")\n", "\n", "liquidity_df = pd.DataFrame(ranked_pools)\n", "\n", "liquidity_df.head()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Top 10 Uniswap V2 pools by liquidity value (in ETH):\n", "1. WETH-SPX Pool: 4,123.378559 ETH\n", "2. USDC-USDT Pool: 2,207.678321 ETH\n", "3. WETH-CAW Pool: 1,973.679689 ETH\n", "4. $PAAL-WETH Pool: 1,930.992069 ETH\n", "5. WOO-WETH Pool: 1,696.467245 ETH\n", "6. WETH-FOX Pool: 1,532.164326 ETH\n", "7. WHITE-USDC Pool: 1,441.088288 ETH\n", "8. NPC-WETH Pool: 1,119.511035 ETH\n", "9. APU-WETH Pool: 996.292486 ETH\n", "10. ANDY-WETH Pool: 987.750823 ETH\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>pool_address</th>\n", "      <th>token0</th>\n", "      <th>token1</th>\n", "      <th>token0_address</th>\n", "      <th>token1_address</th>\n", "      <th>token0_balance</th>\n", "      <th>token1_balance</th>\n", "      <th>liquidity_value_eth</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>******************************************</td>\n", "      <td>WETH</td>\n", "      <td>SPX</td>\n", "      <td>******************************************</td>\n", "      <td>******************************************</td>\n", "      <td>2.069905e+03</td>\n", "      <td>9.826740e+06</td>\n", "      <td>4123.378559</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>******************************************</td>\n", "      <td>USDC</td>\n", "      <td>USDT</td>\n", "      <td>******************************************</td>\n", "      <td>******************************************</td>\n", "      <td>2.357162e+06</td>\n", "      <td>2.361648e+06</td>\n", "      <td>2207.678321</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>******************************************</td>\n", "      <td>WETH</td>\n", "      <td>CAW</td>\n", "      <td>******************************************</td>\n", "      <td>******************************************</td>\n", "      <td>9.913955e+02</td>\n", "      <td>5.230487e+13</td>\n", "      <td>1973.679689</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>******************************************</td>\n", "      <td>$PAAL</td>\n", "      <td>WETH</td>\n", "      <td>******************************************</td>\n", "      <td>******************************************</td>\n", "      <td>1.711835e+07</td>\n", "      <td>9.686685e+02</td>\n", "      <td>1930.992069</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>******************************************</td>\n", "      <td>WOO</td>\n", "      <td>WETH</td>\n", "      <td>******************************************</td>\n", "      <td>******************************************</td>\n", "      <td>1.896400e+07</td>\n", "      <td>8.510000e+02</td>\n", "      <td>1696.467245</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                 pool_address token0 token1  \\\n", "0  ******************************************   WETH    SPX   \n", "1  ******************************************   USDC   USDT   \n", "2  ******************************************   WETH    CAW   \n", "3  ******************************************  $PAAL   WETH   \n", "4  ******************************************    WOO   WETH   \n", "\n", "                               token0_address  \\\n", "0  ******************************************   \n", "1  ******************************************   \n", "2  ******************************************   \n", "3  ******************************************   \n", "4  ******************************************   \n", "\n", "                               token1_address  token0_balance  token1_balance  \\\n", "0  ******************************************    2.069905e+03    9.826740e+06   \n", "1  ******************************************    2.357162e+06    2.361648e+06   \n", "2  ******************************************    9.913955e+02    5.230487e+13   \n", "3  ******************************************    1.711835e+07    9.686685e+02   \n", "4  ******************************************    1.896400e+07    8.510000e+02   \n", "\n", "   liquidity_value_eth  \n", "0          4123.378559  \n", "1          2207.678321  \n", "2          1973.679689  \n", "3          1930.992069  \n", "4          1696.467245  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# Define a function to calculate liquidity value for V2 pools\n", "def calculate_v2_pool_liquidity_value(pool, prices):\n", "    try:\n", "        # Extract pool information\n", "        pool_address = pool.get('poolAddress', 'unknown')\n", "        \n", "        # Extract pool state and static info\n", "        pool_state = pool.get('poolState', {}).get('poolState', {})\n", "        pool_static_info = pool.get('poolState', {}).get('poolStaticInfo', {})\n", "        \n", "        token0_address = pool_static_info.get('token0', '').lower()\n", "        token1_address = pool_static_info.get('token1', '').lower()\n", "        token0_symbol = pool_static_info.get('token0Symbol', 'unknown')\n", "        token1_symbol = pool_static_info.get('token1Symbol', 'unknown')\n", "        token0_decimals = int(pool_static_info.get('token0Decimals', 18))\n", "        token1_decimals = int(pool_static_info.get('token1Decimals', 18))\n", "        \n", "        # Get token balances\n", "        token0_balance = float(pool_state.get('tokenBalance0', 0)) / (10 ** token0_decimals)\n", "        token1_balance = float(pool_state.get('tokenBalance1', 0)) / (10 ** token1_decimals)\n", "        \n", "        # Calculate value in ETH\n", "        token0_price_eth = prices.get(token0_address, 0)\n", "        token1_price_eth = prices.get(token1_address, 0)\n", "        \n", "        liquidity_value_eth = (token0_balance * token0_price_eth) + (token1_balance * token1_price_eth)\n", "        \n", "        return {\n", "            'pool_address': pool_address,\n", "            'token0': token0_symbol,\n", "            'token1': token1_symbol,\n", "            'token0_address': token0_address,\n", "            'token1_address': token1_address,\n", "            'token0_balance': token0_balance,\n", "            'token1_balance': token1_balance,\n", "            'liquidity_value_eth': liquidity_value_eth\n", "        }\n", "    except Exception as e:\n", "        print(f\"Error calculating V2 pool liquidity: {e}\")\n", "        return {\n", "            'pool_address': pool.get('poolAddress', 'unknown'),\n", "            'token0': 'unknown',\n", "            'token1': 'unknown',\n", "            'token0_address': '',\n", "            'token1_address': '',\n", "            'token0_balance': 0.0,\n", "            'token1_balance': 0.0,\n", "            'liquidity_value_eth': 0.0\n", "        }\n", "\n", "# Calculate liquidity value for all V2 pools\n", "v2_liquidity_values = []\n", "for pool in v2_pools[block_num]:\n", "    liquidity_info = calculate_v2_pool_liquidity_value(pool, prices)\n", "    v2_liquidity_values.append(liquidity_info)\n", "\n", "# Sort V2 pools by liquidity value (descending)\n", "ranked_v2_pools = sorted(v2_liquidity_values, key=lambda x: x['liquidity_value_eth'], reverse=True)\n", "\n", "# Display top 10 V2 pools by liquidity\n", "print(\"Top 10 Uniswap V2 pools by liquidity value (in ETH):\")\n", "for i, pool in enumerate(ranked_v2_pools[:10]):\n", "    print(f\"{i+1}. {pool['token0']}-{pool['token1']} Pool: {pool['liquidity_value_eth']:,.6f} ETH\")\n", "\n", "v2_liquidity_df = pd.DataFrame(ranked_v2_pools)\n", "v2_liquidity_df.head()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# Combine V2 and V3 pools and add a column to indicate pool type\n", "v2_liquidity_df['pool_type'] = 'v2'\n", "liquidity_df['pool_type'] = 'v3'\n", "\n", "# Concatenate the dataframes\n", "combined_liquidity_df = pd.concat([v2_liquidity_df, liquidity_df])\n", "\n", "# Sort by liquidity value (descending)\n", "combined_liquidity_df = combined_liquidity_df.sort_values('liquidity_value_eth', ascending=False)\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# Save to a text file with pool addresses, liquidity values, and pool type\n", "with open('../data/prices/pruned_pools/{}_ranked_pool_addresses_v2v3.txt'.format(block_num), 'w') as f:\n", "    for _, pool in combined_liquidity_df.iterrows():\n", "        f.write(f\"{pool['pool_address']} {pool['liquidity_value_eth']} {pool['pool_type']}\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Rank pools by st"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# Read in the ranked pool addresses from the text file\n", "ranked_pools = []\n", "with open('../data/prices/pruned_pools/{}_ranked_pool_addresses_v2v3.txt'.format(block_num), 'r') as f:\n", "    for line in f:\n", "        parts = line.strip().split()\n", "        if len(parts) >= 3:\n", "            pool_address = parts[0]\n", "            liquidity_value = float(parts[1])\n", "            pool_type = parts[2]\n", "            ranked_pools.append({\n", "                'pool_address': pool_address,\n", "                'liquidity_value_eth': liquidity_value,\n", "                'pool_type': pool_type\n", "            })"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1705, 807)"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["len(v3_pools[block_num]), len(v2_pools[block_num])"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["def load_data_from_json(file_path, filtered_pools=None):\n", "    \"\"\"\n", "    Load data from a JSON file\n", "    \n", "    Args:\n", "        file_path: Path to the JSON file\n", "        filtered_pools: Optional set of pool addresses to filter by\n", "    \"\"\"\n", "    with open(file_path, 'r') as file:\n", "        data = json.load(file)\n", "    \n", "    # If filtered_pools is provided, only return pools in the filter list\n", "    if filtered_pools:\n", "        filtered_data = []\n", "        for pool in data:\n", "            try:\n", "                pool_data = json.loads(pool) if isinstance(pool, str) else pool\n", "                pool_address = pool_data['poolState']['poolState']['poolAddress'].lower()\n", "                if pool_address in filtered_pools:\n", "                    filtered_data.append(pool)\n", "            except (<PERSON><PERSON><PERSON><PERSON>, j<PERSON>.J<PERSON>NDecodeError):\n", "                continue\n", "        return filtered_data\n", "    \n", "    return data"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["def load_ranked_pools(ranked_pools_file, top_k=None):\n", "    \"\"\"\n", "    Load ranked pools from a text file and return the top k pools\n", "    \n", "    Args:\n", "        ranked_pools_file: Path to the text file with ranked pools\n", "        top_k: Number of top pools to return (None for all)\n", "    \n", "    Returns:\n", "        Set of pool addresses (lowercase)\n", "    \"\"\"\n", "    pools = {}\n", "    try:\n", "        with open(ranked_pools_file, 'r') as file:\n", "            for line in file:\n", "                parts = line.strip().split()\n", "                if len(parts) >= 2:\n", "                    pool_address = parts[0].lower()\n", "                    liquidity = float(parts[1])\n", "                    pools[pool_address] = liquidity\n", "    except Exception as e:\n", "        print(\"Error loading ranked pools: {}\".format(e))\n", "        return set()\n", "    \n", "    # Sort by liquidity (descending)\n", "    # pools.sort(key=lambda x: x[1], reverse=True)\n", "\n", "    \n", "    # Return set of pool addresses\n", "    return pools\n", "\n", "def load_token_names(file_path):\n", "    \"\"\"\n", "    Load token names from node_name.txt file\n", "    \n", "    Args:\n", "        file_path: Path to the node_name.txt file\n", "    \n", "    Returns:\n", "        Dictionary mapping token addresses (lowercase) to their names\n", "    \"\"\"\n", "    token_names = {}\n", "    try:\n", "        with open(file_path, 'r') as file:\n", "            for line in file:\n", "                parts = line.strip().split()\n", "                if len(parts) >= 2:\n", "                    # Note: address is in the second column\n", "                    address = parts[1].lower()\n", "                    name = parts[0]\n", "                    token_names[address] = name\n", "        return token_names\n", "    except Exception as e:\n", "        print(f\"Error loading token names: {e}\")\n", "        return {}\n", "\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["def build_initial_graph(v3_data, v2_data):\n", "    \"\"\"\n", "    Build initial graph where nodes are tokens and edges are pools\n", "    \n", "    Args:\n", "        v3_data: List of V3 pool data\n", "        v2_data: List of V2 pool data\n", "    \n", "    Returns:\n", "        G: NetworkX graph with tokens as nodes and pools as edges\n", "    \"\"\"\n", "    G = nx.MultiDiGraph()\n", "    \n", "    # Process V3 pools\n", "    for pool in v3_data:\n", "        # try:\n", "            pool_data = json.loads(pool) if isinstance(pool, str) else pool\n", "            \n", "            if pool_data['poolType'] == 'uniswap_v3_like_pool':\n", "                pool_static_info = pool_data['poolState']['poolStaticInfo']\n", "                if pool_static_info['dexExchange'] == 'uniswap':\n", "                    pool_state = pool_data['poolState']['poolState']\n", "                    pool_address = pool_state['poolAddress'].lower()\n", "                    \n", "                    # Extract token addresses\n", "                    token0 = pool_static_info['token0'].lower()\n", "                    token1 = pool_static_info['token1'].lower()\n", "                    \n", "                    # Get liquidity\n", "                    liquidity = int(pool_state.get('liquidity', 0))\n", "                    \n", "                    # Add nodes if they don't exist\n", "                    if not <PERSON><PERSON>has_node(token0):\n", "                        G.add_node(token0)\n", "                    if not <PERSON><PERSON>has_node(token1):\n", "                        G.add_node(token1)\n", "                    \n", "                    # Add edge between tokens with pool information\n", "                    # Using MultiDiGraph to allow multiple pools between the same token pair\n", "                    G.add_edge(token0, token1,\n", "                             pool_address=pool_address,\n", "                             liquidity=liquidity,\n", "                             pool_type='v3')\n", "\n", "                    G.add_edge(token1, token0,\n", "                             pool_address=pool_address,\n", "                             liquidity=liquidity,\n", "                             pool_type='v3')\n", "        # except Exception as e:\n", "        #     continue\n", "    \n", "    # Process V2 pools\n", "    for pool in v2_data:\n", "        # try:\n", "            pool_data = json.loads(pool) if isinstance(pool, str) else pool\n", "            \n", "            if pool_data['poolType'] == 'uniswap_v2_like_pool':\n", "                pool_static_info = pool_data['poolState']['poolStaticInfo']\n", "                if pool_static_info['dexExchange'] == 'uniswap':\n", "                    pool_state = pool_data['poolState']['poolState']\n", "                    pool_address = pool_state['poolAddress'].lower()\n", "                    \n", "                    # Extract token addresses\n", "                    token0 = pool_static_info['token0'].lower()\n", "                    token1 = pool_static_info['token1'].lower()\n", "                    \n", "                    # Calculate liquidity from token balances\n", "                    token_balance0 = float(pool_state['tokenBalance0'])/10**int(pool_static_info['token0Decimals'])\n", "                    token_balance1 = float(pool_state['tokenBalance1'])/10**int(pool_static_info['token1Decimals'])\n", "                    liquidity = token_balance0 * token_balance1  # Simple liquidity measure\n", "                    \n", "                    # Add nodes if they don't exist\n", "                    if not <PERSON><PERSON>has_node(token0):\n", "                        G.add_node(token0)\n", "                    if not <PERSON><PERSON>has_node(token1):\n", "                        G.add_node(token1)\n", "                    \n", "                    # Add edge between tokens with pool information\n", "                    # MultiDiGraph already handles multiple edges between the same token pair\n", "                    G.add_edge(token0, token1,\n", "                             pool_address=pool_address,\n", "                             liquidity=liquidity,\n", "                             pool_type='v2')\n", "                    \n", "                    G.add_edge(token1, token0,\n", "                             pool_address=pool_address,\n", "                             liquidity=liquidity,\n", "                             pool_type='v2')\n", "        # except Exception as e:\n", "        #     continue\n", "    \n", "    return G\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["block_number = 21974203\n", "# File paths\n", "v2_file_path = f'../data/prices/block-{block_number}/filtered-v2pools.json'\n", "v3_file_path = '../data/prices/block-{}/filtered-v3pools.json'.format(block_number)\n", "price_file_path = f'../data/prices/block-{block_number}/tokens-quotes.json'\n", "node_name_file = '../data/node_name.txt'\n", "ranked_pools_file = f'{block_number}_ranked_pool_addresses_v2v3.txt'\n", "ranked_pools = load_ranked_pools(ranked_pools_file)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# Load all pool data\n", "v2_data = load_data_from_json(v2_file_path)\n", "v3_data = load_data_from_json(v3_file_path)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 9 token names\n", "\n", "Processing token pairs:\n", "usdt(******************************************)-ohm(******************************************)\n"]}], "source": ["# Load token names\n", "token_names = load_token_names(node_name_file)\n", "print(f\"Loaded {len(token_names)} token names\")\n", "\n", "# Define token pairs to analyze using addresses\n", "token_pairs = [\n", "    ('******************************************', '******************************************'),  # usdt-ohm\n", "    # ('******************************************', '******************************************') # reth-wsteth\n", "]\n", "\n", "# Print token pairs with names\n", "print(\"\\nProcessing token pairs:\")\n", "for token0_address, token1_address in token_pairs:\n", "    token0_name = token_names.get(token0_address.lower(), \"Unknown\")\n", "    token1_name = token_names.get(token1_address.lower(), \"Unknown\")\n", "    print(f\"{token0_name}({token0_address})-{token1_name}({token1_address})\")"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Initial graph has 1823 nodes and 4928 edges\n"]}], "source": ["# Find relevant pools using bidirectional BFS and top-k selection\n", "initial_graph = build_initial_graph(v3_data, v2_data)\n", "print(f\"\\nInitial graph has {initial_graph.number_of_nodes()} nodes and {initial_graph.number_of_edges()} edges\")"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["7"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["from_to_pair = set([(row['from_token'], row['to_token']) for _, row in benchmark_df.query('block_number == @block_number').iterrows()])\n", "len(from_to_pair)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["def find_relevant_pools(G, token_pairs, max_hop=3, top_n=50, ranked_pools=None):\n", "    \"\"\"\n", "    Find top-N liquidity-significant pools between token0 and token1 using bidirectional BFS.\n", "    Also adds any pool with liquidity >= 100 if not already selected.\n", "\n", "    Args:\n", "        G: NetworkX MultiGraph or MultiDiGraph\n", "        token_pairs: List of one (token0, token1) pair\n", "        max_hop: Max number of hops from token0 to token1\n", "        top_n: Number of pool addresses to return\n", "        ranked_pools: Dict {pool_address: liquidity}\n", "\n", "    Returns:\n", "        set: Top-N pool addresses on highest-min-liquidity paths between token0 and token1\n", "    \"\"\"\n", "    if len(token_pairs) != 1:\n", "        print(f\"Warning: Expected one token pair, got {len(token_pairs)}. Using first.\")\n", "\n", "    token0, token1 = token_pairs[0]\n", "    token0, token1 = token0.lower(), token1.lower()\n", "\n", "    if not <PERSON><PERSON>has_node(token0) or not <PERSON><PERSON>has_node(token1):\n", "        print(f\"One or both tokens not in graph: {token0}, {token1}\")\n", "        return set()\n", "\n", "    # Use bidirectional BFS to find meeting nodes within max_hop\n", "    forward = {token0: []}\n", "    backward = {token1: []}\n", "    f_queue = deque([(token0, [])])\n", "    b_queue = deque([(token1, [])])\n", "\n", "    candidate_paths = []\n", "\n", "    for _ in range(max_hop // 2 + 1):\n", "        def expand(queue, visited, other_visited, reverse=False):\n", "            for _ in range(len(queue)):\n", "                current, path = queue.popleft()\n", "                for neighbor in G.neighbors(current):\n", "                    edge_data_dict = G.get_edge_data(current, neighbor)\n", "                    for key, edge_data in edge_data_dict.items():\n", "                        pool = edge_data.get(\"pool_address\")\n", "                        liquidity = ranked_pools.get(pool, 0) if ranked_pools else 0\n", "                        edge = (current, neighbor, pool, liquidity) if not reverse else (neighbor, current, pool, liquidity)\n", "                        if neighbor not in visited:\n", "                            new_path = path + [edge]\n", "                            visited[neighbor] = new_path\n", "                            queue.append((neighbor, new_path))\n", "                        elif neighbor in other_visited:\n", "                            path1 = path + [edge]\n", "                            path2 = list(reversed(other_visited[neighbor]))\n", "                            full_path = path1 + [(b, a, p, l) for a, b, p, l in path2]\n", "                            if len(full_path) <= max_hop:\n", "                                liqs = [l for _, _, _, l in full_path]\n", "                                if liqs:\n", "                                    min_liq = min(liqs)\n", "                                    heapq.heappush(candidate_paths, (-min_liq, full_path))  # Max-heap by min liquidity\n", "\n", "        expand(f_queue, forward, backward)\n", "        expand(b_queue, backward, forward, reverse=True)\n", "\n", "    if not candidate_paths:\n", "        print(f\"No path found between {token0} and {token1} within {max_hop} hops.\")\n", "        return set()\n", "\n", "    selected_pools = set()\n", "\n", "    while candidate_paths and len(selected_pools) < top_n:\n", "        _, path = heapq.heappop(candidate_paths)\n", "        pools_in_path = [pool for _, _, pool, _ in path if pool and pool not in selected_pools]\n", "        for pool in pools_in_path:\n", "            selected_pools.add(pool)\n", "            if len(selected_pools) >= top_n:\n", "                break\n", "\n", "    # # Rule 2: Add remaining pools with liquidity >= 100 if still under top_n\n", "    # if ranked_pools and len(selected_pools) < top_n:\n", "    #     high_liq_pools = sorted([(pool, liq) for pool, liq in ranked_pools.items()\n", "    #                              if liq >= 100 and pool not in selected_pools],\n", "    #                             key=lambda x: -x[1])\n", "    #     for pool, _ in high_liq_pools:\n", "    #         selected_pools.add(pool)\n", "    #         if len(selected_pools) >= top_n:\n", "    #             break\n", "\n", "    return selected_pools\n"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["usdt(******************************************)-cbbtc(******************************************)\n", "length of miss_pools: 3 2 1\n", "\n", "spx(******************************************)-peas(******************************************)\n", "length of miss_pools: 3 2 2\n", "\n", "usdt(******************************************)-frax(******************************************)\n", "length of miss_pools: 0 0 0\n", "\n", "reth(******************************************)-wsteth(******************************************)\n", "length of miss_pools: 1 1 2\n", "\n", "wbtc(******************************************)-usdt(******************************************)\n", "length of miss_pools: 1 1 2\n", "\n", "wbtc(******************************************)-spx(******************************************)\n", "length of miss_pools: 2 2 3\n", "\n", "usdt(******************************************)-ohm(******************************************)\n", "length of miss_pools: 0 1 1\n", "\n"]}], "source": ["pools_dict = {}\n", "miss_pools_dict = {}\n", "relevant_pools_dict = {}\n", "\n", "for pair in from_to_pair:\n", "    from_addr = pair[0]\n", "    from_name = token_names.get(from_addr.lower(), \"Unknown\")\n", "    to_addr = pair[1]\n", "    to_name = token_names.get(to_addr.lower(), \"Unknown\")\n", "    print(f\"{from_name}({from_addr})-{to_name}({to_addr})\")\n", "    \n", "    block = block_number\n", "    benchmark_df['from_amount'] = benchmark_df['from_amount'].astype(float)\n", "    df_query = benchmark_df.query('from_token == @from_addr and to_token == @to_addr and block_number == @block').sort_values(by='from_amount', ascending=True)\n", "    pools1 = df_query['pool_addresses'].values[0]\n", "    pools2 = df_query['pool_addresses'].values[1]\n", "    pools3 = df_query['pool_addresses'].values[2]\n", "\n", "    pools_dict[pair] = [pools1, pools2, pools3]\n", "    \n", "    # print('length of pools1, pools2, pools3:', len(pools1), len(pools2), len(pools3))\n", "    # will those pools be inclusive?\n", "    inclu1 = [i for i in pools1 if i not in pools2]\n", "    inclu2 = [i for i in pools2 if i not in pools3]\n", "    inclu3 = [i for i in pools1 if i not in pools3]\n", "\n", "    # print('length of inclu1, inclu2, inclu3:', len(inclu1), len(inclu2), len(inclu3))\n", "\n", "    # max hop, top n, ranked_pools\n", "    initial_graph = build_initial_graph(v3_data, v2_data)\n", "    relevant_pools = find_relevant_pools(initial_graph, [(from_addr, to_addr)], 5, 2500, ranked_pools)\n", "    relevant_pools_dict[pair] = relevant_pools\n", "    # print(len(relevant_pools))\n", "    \n", "    # print(relevant_pools_pools)\n", "    miss_pools = [i for i in pools1 if i not in relevant_pools], \\\n", "                [i for i in pools2 if i not in relevant_pools], \\\n", "                [i for i in pools3 if i not in relevant_pools]\n", "    \n", "    print('length of miss_pools:', len(miss_pools[0]), len(miss_pools[1]), len(miss_pools[2]))\n", "    miss_pools_dict[pair] = miss_pools\n", "    print()\n"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}