Source: 3, Sink: 69
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277342 seconds
Optimal solution found.
Minimum total cost = -33987916
Max flow value      : 1984
Min cost value      : -33987916
Total time          : 0.151716 seconds

========================================

Source: 3, Sink: 410
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27463 seconds
Optimal solution found.
Minimum total cost = -33489119
Max flow value      : 59817
Min cost value      : -33489119
Total time          : 0.153592 seconds

========================================

Source: 3, Sink: 117
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276054 seconds
Optimal solution found.
Minimum total cost = -33258911
Max flow value      : 61494
Min cost value      : -33258911
Total time          : 0.155248 seconds

========================================

Source: 3, Sink: 635
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.308426 seconds
Optimal solution found.
Minimum total cost = -33527390
Max flow value      : 39882
Min cost value      : -33527390
Total time          : 0.152516 seconds

========================================

Source: 3, Sink: 125
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275442 seconds
Optimal solution found.
Minimum total cost = -33476935
Max flow value      : 61425
Min cost value      : -33476935
Total time          : 0.22717 seconds

========================================

Source: 3, Sink: 0
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278651 seconds
Optimal solution found.
Minimum total cost = -32861384
Max flow value      : 50214
Min cost value      : -32861384
Total time          : 0.159506 seconds

========================================

Source: 3, Sink: 1
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.285073 seconds
Optimal solution found.
Minimum total cost = -33291984
Max flow value      : 32990
Min cost value      : -33291984
Total time          : 0.15423 seconds

========================================

Source: 3, Sink: 4
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.283615 seconds
Optimal solution found.
Minimum total cost = -33188640
Max flow value      : 32990
Min cost value      : -33188640
Total time          : 0.152952 seconds

========================================

Source: 3, Sink: 2
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.297799 seconds
Optimal solution found.
Minimum total cost = -33792666
Max flow value      : 32990
Min cost value      : -33792666
Total time          : 0.161224 seconds

========================================

Source: 3, Sink: 5
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27764 seconds
Optimal solution found.
Minimum total cost = -33800549
Max flow value      : 32990
Min cost value      : -33800549
Total time          : 0.158295 seconds

========================================

Source: 37564, Sink: 20845
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.288707 seconds
Optimal solution found.
Minimum total cost = -33411188
Max flow value      : 49700
Min cost value      : -33411188
Total time          : 0.147107 seconds

========================================

Source: 41826, Sink: 21425
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275211 seconds
Optimal solution found.
Minimum total cost = -32645074
Max flow value      : 57461
Min cost value      : -32645074
Total time          : 0.162395 seconds

========================================

Source: 12205, Sink: 4460
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275393 seconds
Optimal solution found.
Minimum total cost = -33315594
Max flow value      : 14596
Min cost value      : -33315594
Total time          : 0.168409 seconds

========================================

Source: 27934, Sink: 48015
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.229991 seconds
Optimal solution found.
Minimum total cost = -33918572
Max flow value      : 31680
Min cost value      : -33918572
Total time          : 0.101627 seconds

========================================

Source: 60515, Sink: 18244
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279579 seconds
Optimal solution found.
Minimum total cost = -33845776
Max flow value      : 7996
Min cost value      : -33845776
Total time          : 0.115934 seconds

========================================

Source: 50243, Sink: 47520
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278544 seconds
Optimal solution found.
Minimum total cost = -33257750
Max flow value      : 31098
Min cost value      : -33257750
Total time          : 0.15845 seconds

========================================

Source: 3079, Sink: 8079
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278715 seconds
Optimal solution found.
Minimum total cost = -33756014
Max flow value      : 19098
Min cost value      : -33756014
Total time          : 0.110643 seconds

========================================

Source: 30251, Sink: 8418
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.231828 seconds
Optimal solution found.
Minimum total cost = -33966092
Max flow value      : 0
Min cost value      : -33966092
Total time          : 0.123599 seconds

========================================

Source: 27132, Sink: 25301
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277011 seconds
Optimal solution found.
Minimum total cost = -33728960
Max flow value      : 13174
Min cost value      : -33728960
Total time          : 0.178153 seconds

========================================

Source: 11640, Sink: 3634
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.231872 seconds
Optimal solution found.
Minimum total cost = -33757272
Max flow value      : 7018
Min cost value      : -33757272
Total time          : 0.143867 seconds

========================================

Source: 28182, Sink: 30526
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275704 seconds
Optimal solution found.
Minimum total cost = -33866252
Max flow value      : 24960
Min cost value      : -33866252
Total time          : 0.170035 seconds

========================================

Source: 11031, Sink: 40702
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280454 seconds
Optimal solution found.
Minimum total cost = -33028838
Max flow value      : 57180
Min cost value      : -33028838
Total time          : 0.163477 seconds

========================================

Source: 8350, Sink: 44137
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275154 seconds
Optimal solution found.
Minimum total cost = -33121052
Max flow value      : 43976
Min cost value      : -33121052
Total time          : 0.161018 seconds

========================================

Source: 61471, Sink: 22377
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27573 seconds
Optimal solution found.
Minimum total cost = -33083967
Max flow value      : 28541
Min cost value      : -33083967
Total time          : 0.133723 seconds

========================================

Source: 3654, Sink: 9988
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277359 seconds
Optimal solution found.
Minimum total cost = -34017497
Max flow value      : 20562
Min cost value      : -34017497
Total time          : 0.178738 seconds

========================================

Source: 34870, Sink: 48082
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277942 seconds
Optimal solution found.
Minimum total cost = -33868002
Max flow value      : 39236
Min cost value      : -33868002
Total time          : 0.175437 seconds

========================================

Source: 27108, Sink: 52059
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275848 seconds
Optimal solution found.
Minimum total cost = -32074718
Max flow value      : 45726
Min cost value      : -32074718
Total time          : 0.15236 seconds

========================================

Source: 28300, Sink: 3982
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27612 seconds
Optimal solution found.
Minimum total cost = -32686480
Max flow value      : 58686
Min cost value      : -32686480
Total time          : 0.15183 seconds

========================================

Source: 31468, Sink: 44058
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27675 seconds
Optimal solution found.
Minimum total cost = -32593087
Max flow value      : 35728
Min cost value      : -32593087
Total time          : 0.175579 seconds

========================================

Source: 29710, Sink: 28558
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276075 seconds
Optimal solution found.
Minimum total cost = -32907708
Max flow value      : 40042
Min cost value      : -32907708
Total time          : 0.135756 seconds

========================================

Source: 62604, Sink: 15283
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278144 seconds
Optimal solution found.
Minimum total cost = -31648436
Max flow value      : 55234
Min cost value      : -31648436
Total time          : 0.165355 seconds

========================================

Source: 11776, Sink: 49068
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276386 seconds
Optimal solution found.
Minimum total cost = -32818089
Max flow value      : 55603
Min cost value      : -32818089
Total time          : 0.156799 seconds

========================================

Source: 55127, Sink: 48390
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280095 seconds
Optimal solution found.
Minimum total cost = -33605729
Max flow value      : 46484
Min cost value      : -33605729
Total time          : 0.162973 seconds

========================================

Source: 14613, Sink: 6498
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274817 seconds
Optimal solution found.
Minimum total cost = -32383060
Max flow value      : 71555
Min cost value      : -32383060
Total time          : 0.163764 seconds

========================================

Source: 15532, Sink: 55734
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275588 seconds
Optimal solution found.
Minimum total cost = -33190490
Max flow value      : 42086
Min cost value      : -33190490
Total time          : 0.174591 seconds

========================================

Source: 65986, Sink: 52317
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27468 seconds
Optimal solution found.
Minimum total cost = -33937490
Max flow value      : 4086
Min cost value      : -33937490
Total time          : 0.170852 seconds

========================================

Source: 20223, Sink: 53716
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279503 seconds
Optimal solution found.
Minimum total cost = -33125672
Max flow value      : 50016
Min cost value      : -33125672
Total time          : 0.173152 seconds

========================================

Source: 19125, Sink: 31079
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275304 seconds
Optimal solution found.
Minimum total cost = -33646683
Max flow value      : 33622
Min cost value      : -33646683
Total time          : 0.1832 seconds

========================================

Source: 31400, Sink: 3606
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280447 seconds
Optimal solution found.
Minimum total cost = -33719534
Max flow value      : 18966
Min cost value      : -33719534
Total time          : 0.125888 seconds

========================================

Source: 6843, Sink: 64219
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.285606 seconds
Optimal solution found.
Minimum total cost = -33665126
Max flow value      : 59100
Min cost value      : -33665126
Total time          : 0.235106 seconds

========================================

Source: 38491, Sink: 59863
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274713 seconds
Optimal solution found.
Minimum total cost = -33500974
Max flow value      : 12527
Min cost value      : -33500974
Total time          : 0.172305 seconds

========================================

Source: 51655, Sink: 50273
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278466 seconds
Optimal solution found.
Minimum total cost = -32522726
Max flow value      : 49442
Min cost value      : -32522726
Total time          : 0.161698 seconds

========================================

Source: 35225, Sink: 10543
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275562 seconds
Optimal solution found.
Minimum total cost = -33691769
Max flow value      : 15369
Min cost value      : -33691769
Total time          : 0.124449 seconds

========================================

Source: 20320, Sink: 26885
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.232505 seconds
Optimal solution found.
Minimum total cost = -34070768
Max flow value      : 9516
Min cost value      : -34070768
Total time          : 0.154695 seconds

========================================

Source: 36017, Sink: 61462
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27522 seconds
Optimal solution found.
Minimum total cost = -33518220
Max flow value      : 30487
Min cost value      : -33518220
Total time          : 0.141237 seconds

========================================

Source: 26222, Sink: 5213
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275023 seconds
Optimal solution found.
Minimum total cost = -33505835
Max flow value      : 33154
Min cost value      : -33505835
Total time          : 0.174017 seconds

========================================

Source: 29976, Sink: 37547
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275923 seconds
Optimal solution found.
Minimum total cost = -33729358
Max flow value      : 21814
Min cost value      : -33729358
Total time          : 0.167432 seconds

========================================

Source: 6522, Sink: 6236
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275586 seconds
Optimal solution found.
Minimum total cost = -33741688
Max flow value      : 16840
Min cost value      : -33741688
Total time          : 0.117807 seconds

========================================

Source: 18436, Sink: 44024
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275995 seconds
Optimal solution found.
Minimum total cost = -32910374
Max flow value      : 42946
Min cost value      : -32910374
Total time          : 0.160793 seconds

========================================

Source: 45833, Sink: 39311
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277214 seconds
Optimal solution found.
Minimum total cost = -33966092
Max flow value      : 2054
Min cost value      : -33966092
Total time          : 0.171805 seconds

========================================

Source: 45522, Sink: 55580
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275536 seconds
Optimal solution found.
Minimum total cost = -33571434
Max flow value      : 35968
Min cost value      : -33571434
Total time          : 0.123383 seconds

========================================

Source: 1363, Sink: 17645
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280234 seconds
Optimal solution found.
Minimum total cost = -33946864
Max flow value      : 31436
Min cost value      : -33946864
Total time          : 0.166204 seconds

========================================

Source: 54356, Sink: 65756
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276312 seconds
Optimal solution found.
Minimum total cost = -33754626
Max flow value      : 18179
Min cost value      : -33754626
Total time          : 0.169788 seconds

========================================

Source: 45672, Sink: 66052
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27942 seconds
Optimal solution found.
Minimum total cost = -33920378
Max flow value      : 6631
Min cost value      : -33920378
Total time          : 0.171974 seconds

========================================

Source: 33664, Sink: 16702
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.283775 seconds
Optimal solution found.
Minimum total cost = -33576736
Max flow value      : 31518
Min cost value      : -33576736
Total time          : 0.155993 seconds

========================================

Source: 53305, Sink: 42971
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277511 seconds
Optimal solution found.
Minimum total cost = -33669292
Max flow value      : 22292
Min cost value      : -33669292
Total time          : 0.161927 seconds

========================================

Source: 26826, Sink: 55966
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276005 seconds
Optimal solution found.
Minimum total cost = -33768196
Max flow value      : 6824
Min cost value      : -33768196
Total time          : 0.115969 seconds

========================================

Source: 27209, Sink: 58526
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276054 seconds
Optimal solution found.
Minimum total cost = -33362426
Max flow value      : 23052
Min cost value      : -33362426
Total time          : 0.129036 seconds

========================================

Source: 36080, Sink: 1324
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278696 seconds
Optimal solution found.
Minimum total cost = -32884888
Max flow value      : 65949
Min cost value      : -32884888
Total time          : 0.235069 seconds

========================================

Source: 37221, Sink: 40477
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27836 seconds
Optimal solution found.
Minimum total cost = -32759698
Max flow value      : 30581
Min cost value      : -32759698
Total time          : 0.163966 seconds

========================================

Source: 25110, Sink: 18286
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.283577 seconds
Optimal solution found.
Minimum total cost = -34037441
Max flow value      : 2798
Min cost value      : -34037441
Total time          : 0.159835 seconds

========================================

Source: 45620, Sink: 32190
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276044 seconds
Optimal solution found.
Minimum total cost = -33963194
Max flow value      : 966
Min cost value      : -33963194
Total time          : 0.219286 seconds

========================================

Source: 40515, Sink: 41042
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275066 seconds
Optimal solution found.
Minimum total cost = -32149677
Max flow value      : 73868
Min cost value      : -32149677
Total time          : 0.155337 seconds

========================================

Source: 41308, Sink: 63351
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275601 seconds
Optimal solution found.
Minimum total cost = -31579810
Max flow value      : 42730
Min cost value      : -31579810
Total time          : 0.169855 seconds

========================================

Source: 26888, Sink: 53396
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280647 seconds
Optimal solution found.
Minimum total cost = -33575782
Max flow value      : 41684
Min cost value      : -33575782
Total time          : 0.207366 seconds

========================================

Source: 33135, Sink: 59657
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274466 seconds
Optimal solution found.
Minimum total cost = -33836300
Max flow value      : 10945
Min cost value      : -33836300
Total time          : 0.126771 seconds

========================================

Source: 26148, Sink: 48416
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275074 seconds
Optimal solution found.
Minimum total cost = -33477356
Max flow value      : 27291
Min cost value      : -33477356
Total time          : 0.175006 seconds

========================================

Source: 59767, Sink: 34498
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.298603 seconds
Optimal solution found.
Minimum total cost = -33011652
Max flow value      : 52285
Min cost value      : -33011652
Total time          : 0.153176 seconds

========================================

Source: 60673, Sink: 47039
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.283898 seconds
Optimal solution found.
Minimum total cost = -33579214
Max flow value      : 42062
Min cost value      : -33579214
Total time          : 0.170617 seconds

========================================

Source: 10026, Sink: 15525
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277647 seconds
Optimal solution found.
Minimum total cost = -32624360
Max flow value      : 42684
Min cost value      : -32624360
Total time          : 0.152327 seconds

========================================

Source: 34590, Sink: 54769
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275265 seconds
Optimal solution found.
Minimum total cost = -33467789
Max flow value      : 25554
Min cost value      : -33467789
Total time          : 0.134282 seconds

========================================

Source: 39353, Sink: 33421
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274646 seconds
Optimal solution found.
Minimum total cost = -34062348
Max flow value      : 6016
Min cost value      : -34062348
Total time          : 0.17136 seconds

========================================

Source: 1719, Sink: 50404
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27502 seconds
Optimal solution found.
Minimum total cost = -33978672
Max flow value      : 1258
Min cost value      : -33978672
Total time          : 0.179155 seconds

========================================

Source: 61969, Sink: 42288
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275419 seconds
Optimal solution found.
Minimum total cost = -33452891
Max flow value      : 42192
Min cost value      : -33452891
Total time          : 0.164071 seconds

========================================

Source: 53475, Sink: 43839
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275477 seconds
Optimal solution found.
Minimum total cost = -32781528
Max flow value      : 27548
Min cost value      : -32781528
Total time          : 0.131554 seconds

========================================

Source: 50865, Sink: 1976
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278738 seconds
Optimal solution found.
Minimum total cost = -33154881
Max flow value      : 43186
Min cost value      : -33154881
Total time          : 0.225848 seconds

========================================

Source: 29897, Sink: 54888
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280961 seconds
Optimal solution found.
Minimum total cost = -33449410
Max flow value      : 42502
Min cost value      : -33449410
Total time          : 0.162978 seconds

========================================

Source: 50426, Sink: 50126
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275937 seconds
Optimal solution found.
Minimum total cost = -33289328
Max flow value      : 38655
Min cost value      : -33289328
Total time          : 0.159772 seconds

========================================

Source: 34811, Sink: 8008
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280106 seconds
Optimal solution found.
Minimum total cost = -27606612
Max flow value      : 62488
Min cost value      : -27606612
Total time          : 0.148286 seconds

========================================

Source: 56453, Sink: 55147
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.290817 seconds
Optimal solution found.
Minimum total cost = -33931732
Max flow value      : 34360
Min cost value      : -33931732
Total time          : 0.12596 seconds

========================================

Source: 42523, Sink: 29240
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277575 seconds
Optimal solution found.
Minimum total cost = -33433072
Max flow value      : 43234
Min cost value      : -33433072
Total time          : 0.165998 seconds

========================================

Source: 53071, Sink: 65481
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276885 seconds
Optimal solution found.
Minimum total cost = -32051487
Max flow value      : 34324
Min cost value      : -32051487
Total time          : 0.168651 seconds

========================================

Source: 20522, Sink: 54172
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276099 seconds
Optimal solution found.
Minimum total cost = -33857722
Max flow value      : 20350
Min cost value      : -33857722
Total time          : 0.126211 seconds

========================================

Source: 65799, Sink: 59379
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275074 seconds
Optimal solution found.
Minimum total cost = -32632621
Max flow value      : 60712
Min cost value      : -32632621
Total time          : 0.170561 seconds

========================================

Source: 43518, Sink: 59066
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.287928 seconds
Optimal solution found.
Minimum total cost = -33292415
Max flow value      : 49103
Min cost value      : -33292415
Total time          : 0.156479 seconds

========================================

Source: 14232, Sink: 42976
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27496 seconds
Optimal solution found.
Minimum total cost = -33897782
Max flow value      : 4554
Min cost value      : -33897782
Total time          : 0.176305 seconds

========================================

Source: 11575, Sink: 26960
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278091 seconds
Optimal solution found.
Minimum total cost = -33253553
Max flow value      : 52430
Min cost value      : -33253553
Total time          : 0.168477 seconds

========================================

Source: 34464, Sink: 55262
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276052 seconds
Optimal solution found.
Minimum total cost = -32725769
Max flow value      : 50554
Min cost value      : -32725769
Total time          : 0.17496 seconds

========================================

Source: 17485, Sink: 13129
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275361 seconds
Optimal solution found.
Minimum total cost = -33805751
Max flow value      : 9449
Min cost value      : -33805751
Total time          : 0.104113 seconds

========================================

Source: 48568, Sink: 63314
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27532 seconds
Optimal solution found.
Minimum total cost = -33519290
Max flow value      : 33679
Min cost value      : -33519290
Total time          : 0.152789 seconds

========================================

Source: 5413, Sink: 65139
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.285982 seconds
Optimal solution found.
Minimum total cost = -33702353
Max flow value      : 27762
Min cost value      : -33702353
Total time          : 0.175562 seconds

========================================

Source: 60742, Sink: 36963
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.28237 seconds
Optimal solution found.
Minimum total cost = -33426570
Max flow value      : 40615
Min cost value      : -33426570
Total time          : 0.249189 seconds

========================================

Source: 58181, Sink: 99
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276227 seconds
Optimal solution found.
Minimum total cost = -32366166
Max flow value      : 62836
Min cost value      : -32366166
Total time          : 0.17176 seconds

========================================

Source: 30485, Sink: 35703
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275629 seconds
Optimal solution found.
Minimum total cost = -33310780
Max flow value      : 19948
Min cost value      : -33310780
Total time          : 0.162729 seconds

========================================

Source: 32403, Sink: 38078
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278124 seconds
Optimal solution found.
Minimum total cost = -33008737
Max flow value      : 38606
Min cost value      : -33008737
Total time          : 0.172308 seconds

========================================

Source: 58736, Sink: 47916
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274042 seconds
Optimal solution found.
Minimum total cost = -33158340
Max flow value      : 51798
Min cost value      : -33158340
Total time          : 0.157199 seconds

========================================

Source: 34978, Sink: 82
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.232156 seconds
Optimal solution found.
Minimum total cost = -33938855
Max flow value      : 29966
Min cost value      : -33938855
Total time          : 0.146386 seconds

========================================

Source: 58483, Sink: 18586
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275437 seconds
Optimal solution found.
Minimum total cost = -33736990
Max flow value      : 34570
Min cost value      : -33736990
Total time          : 0.209951 seconds

========================================

Source: 18273, Sink: 55726
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.231688 seconds
Optimal solution found.
Minimum total cost = -33512939
Max flow value      : 17676
Min cost value      : -33512939
Total time          : 0.0958485 seconds

========================================

Source: 23243, Sink: 61019
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276704 seconds
Optimal solution found.
Minimum total cost = -33037411
Max flow value      : 56659
Min cost value      : -33037411
Total time          : 0.232771 seconds

========================================

Source: 42308, Sink: 43902
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27631 seconds
Optimal solution found.
Minimum total cost = -32969411
Max flow value      : 66248
Min cost value      : -32969411
Total time          : 0.152027 seconds

========================================

Source: 33771, Sink: 52164
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.235496 seconds
Optimal solution found.
Minimum total cost = -33334743
Max flow value      : 30611
Min cost value      : -33334743
Total time          : 0.115914 seconds

========================================

Source: 55913, Sink: 57591
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274967 seconds
Optimal solution found.
Minimum total cost = -33014006
Max flow value      : 25956
Min cost value      : -33014006
Total time          : 0.134829 seconds

========================================

Source: 4770, Sink: 40472
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275333 seconds
Optimal solution found.
Minimum total cost = -32080619
Max flow value      : 45141
Min cost value      : -32080619
Total time          : 0.170653 seconds

========================================

Source: 10434, Sink: 45452
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.286432 seconds
Optimal solution found.
Minimum total cost = -30422812
Max flow value      : 34070
Min cost value      : -30422812
Total time          : 0.171604 seconds

========================================

Source: 21923, Sink: 16243
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.281218 seconds
Optimal solution found.
Minimum total cost = -33030689
Max flow value      : 66879
Min cost value      : -33030689
Total time          : 0.168596 seconds

========================================

Source: 45460, Sink: 1624
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276221 seconds
Optimal solution found.
Minimum total cost = -33557201
Max flow value      : 29350
Min cost value      : -33557201
Total time          : 0.16579 seconds

========================================

Source: 11640, Sink: 51450
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276207 seconds
Optimal solution found.
Minimum total cost = -32274249
Max flow value      : 44987
Min cost value      : -32274249
Total time          : 0.15316 seconds

========================================

Source: 45377, Sink: 56986
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275936 seconds
Optimal solution found.
Minimum total cost = -33589762
Max flow value      : 28634
Min cost value      : -33589762
Total time          : 0.172861 seconds

========================================

Source: 114, Sink: 8910
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278629 seconds
Optimal solution found.
Minimum total cost = -33771032
Max flow value      : 3856
Min cost value      : -33771032
Total time          : 0.170438 seconds

========================================

Source: 4123, Sink: 43118
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278734 seconds
Optimal solution found.
Minimum total cost = -33822271
Max flow value      : 24589
Min cost value      : -33822271
Total time          : 0.14349 seconds

========================================

Source: 7202, Sink: 44572
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274996 seconds
Optimal solution found.
Minimum total cost = -32595591
Max flow value      : 48451
Min cost value      : -32595591
Total time          : 0.222522 seconds

========================================

Source: 10414, Sink: 13817
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.231485 seconds
Optimal solution found.
Minimum total cost = -33943092
Max flow value      : 11500
Min cost value      : -33943092
Total time          : 0.148086 seconds

========================================

Source: 50210, Sink: 61854
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276198 seconds
Optimal solution found.
Minimum total cost = -32343874
Max flow value      : 43782
Min cost value      : -32343874
Total time          : 0.169789 seconds

========================================

Source: 37917, Sink: 36160
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277203 seconds
Optimal solution found.
Minimum total cost = -33839702
Max flow value      : 20198
Min cost value      : -33839702
Total time          : 0.154931 seconds

========================================

Source: 62136, Sink: 50419
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.285388 seconds
Optimal solution found.
Minimum total cost = -34016692
Max flow value      : 2300
Min cost value      : -34016692
Total time          : 0.156055 seconds

========================================

Source: 37433, Sink: 16843
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275703 seconds
Optimal solution found.
Minimum total cost = -33459281
Max flow value      : 42226
Min cost value      : -33459281
Total time          : 0.14624 seconds

========================================

Source: 45294, Sink: 34740
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27734 seconds
Optimal solution found.
Minimum total cost = -33895535
Max flow value      : 4866
Min cost value      : -33895535
Total time          : 0.171069 seconds

========================================

Source: 59641, Sink: 63457
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.283404 seconds
Optimal solution found.
Minimum total cost = -33948182
Max flow value      : 5970
Min cost value      : -33948182
Total time          : 0.173967 seconds

========================================

Source: 24902, Sink: 51889
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276088 seconds
Optimal solution found.
Minimum total cost = -33983636
Max flow value      : 1032
Min cost value      : -33983636
Total time          : 0.107068 seconds

========================================

Source: 55262, Sink: 14191
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276727 seconds
Optimal solution found.
Minimum total cost = -33976011
Max flow value      : 19838
Min cost value      : -33976011
Total time          : 0.119784 seconds

========================================

Source: 23618, Sink: 3425
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275135 seconds
Optimal solution found.
Minimum total cost = -33809702
Max flow value      : 49208
Min cost value      : -33809702
Total time          : 0.172498 seconds

========================================

Source: 56642, Sink: 29369
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276831 seconds
Optimal solution found.
Minimum total cost = -33850872
Max flow value      : 8230
Min cost value      : -33850872
Total time          : 0.117211 seconds

========================================

Source: 61060, Sink: 2734
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275344 seconds
Optimal solution found.
Minimum total cost = -32958617
Max flow value      : 51452
Min cost value      : -32958617
Total time          : 0.145883 seconds

========================================

Source: 30907, Sink: 64827
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275916 seconds
Optimal solution found.
Minimum total cost = -34083932
Max flow value      : 8728
Min cost value      : -34083932
Total time          : 0.17937 seconds

========================================

Source: 10743, Sink: 64850
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.284867 seconds
Optimal solution found.
Minimum total cost = -32835217
Max flow value      : 37444
Min cost value      : -32835217
Total time          : 0.183737 seconds

========================================

Source: 26728, Sink: 27094
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275623 seconds
Optimal solution found.
Minimum total cost = -33509397
Max flow value      : 27634
Min cost value      : -33509397
Total time          : 0.164211 seconds

========================================

Source: 26994, Sink: 39725
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274441 seconds
Optimal solution found.
Minimum total cost = -33055184
Max flow value      : 47025
Min cost value      : -33055184
Total time          : 0.160771 seconds

========================================

Source: 16750, Sink: 57811
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274626 seconds
Optimal solution found.
Minimum total cost = -33491710
Max flow value      : 13000
Min cost value      : -33491710
Total time          : 0.119923 seconds

========================================

Source: 40109, Sink: 32045
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279292 seconds
Optimal solution found.
Minimum total cost = -31243748
Max flow value      : 40354
Min cost value      : -31243748
Total time          : 0.168855 seconds

========================================

Source: 8268, Sink: 1218
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275634 seconds
Optimal solution found.
Minimum total cost = -33768466
Max flow value      : 17966
Min cost value      : -33768466
Total time          : 0.11254 seconds

========================================

Source: 7062, Sink: 43511
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278872 seconds
Optimal solution found.
Minimum total cost = -32647508
Max flow value      : 66429
Min cost value      : -32647508
Total time          : 0.15777 seconds

========================================

Source: 38396, Sink: 51823
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277611 seconds
Optimal solution found.
Minimum total cost = -30591242
Max flow value      : 67448
Min cost value      : -30591242
Total time          : 0.176471 seconds

========================================

Source: 19494, Sink: 28249
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.281287 seconds
Optimal solution found.
Minimum total cost = -34002392
Max flow value      : 7260
Min cost value      : -34002392
Total time          : 0.115358 seconds

========================================

Source: 18069, Sink: 8653
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277812 seconds
Optimal solution found.
Minimum total cost = -33808495
Max flow value      : 28654
Min cost value      : -33808495
Total time          : 0.172723 seconds

========================================

Source: 17716, Sink: 40598
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.295328 seconds
Optimal solution found.
Minimum total cost = -31784764
Max flow value      : 53047
Min cost value      : -31784764
Total time          : 0.169801 seconds

========================================

Source: 63952, Sink: 6580
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280465 seconds
Optimal solution found.
Minimum total cost = -33507602
Max flow value      : 26021
Min cost value      : -33507602
Total time          : 0.146722 seconds

========================================

Source: 47066, Sink: 51302
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278529 seconds
Optimal solution found.
Minimum total cost = -33493810
Max flow value      : 20534
Min cost value      : -33493810
Total time          : 0.177621 seconds

========================================

Source: 19945, Sink: 58787
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275338 seconds
Optimal solution found.
Minimum total cost = -33367466
Max flow value      : 39722
Min cost value      : -33367466
Total time          : 0.246709 seconds

========================================

Source: 50573, Sink: 11100
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275774 seconds
Optimal solution found.
Minimum total cost = -33696919
Max flow value      : 24332
Min cost value      : -33696919
Total time          : 0.15527 seconds

========================================

Source: 17531, Sink: 36411
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277051 seconds
Optimal solution found.
Minimum total cost = -32273456
Max flow value      : 70344
Min cost value      : -32273456
Total time          : 0.156019 seconds

========================================

Source: 6557, Sink: 14250
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275884 seconds
Optimal solution found.
Minimum total cost = -33988628
Max flow value      : 7394
Min cost value      : -33988628
Total time          : 0.148191 seconds

========================================

Source: 47803, Sink: 2789
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27581 seconds
Optimal solution found.
Minimum total cost = -32781969
Max flow value      : 69470
Min cost value      : -32781969
Total time          : 0.148142 seconds

========================================

Source: 22948, Sink: 53351
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27813 seconds
Optimal solution found.
Minimum total cost = -33868204
Max flow value      : 2174
Min cost value      : -33868204
Total time          : 0.176243 seconds

========================================

Source: 39398, Sink: 57608
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27571 seconds
Optimal solution found.
Minimum total cost = -32789943
Max flow value      : 51476
Min cost value      : -32789943
Total time          : 0.174297 seconds

========================================

Source: 14413, Sink: 61518
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.233387 seconds
Optimal solution found.
Minimum total cost = -33882864
Max flow value      : 21508
Min cost value      : -33882864
Total time          : 0.137562 seconds

========================================

Source: 50595, Sink: 55808
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.229567 seconds
Optimal solution found.
Minimum total cost = -33470305
Max flow value      : 20772
Min cost value      : -33470305
Total time          : 0.139179 seconds

========================================

Source: 38606, Sink: 5688
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276648 seconds
Optimal solution found.
Minimum total cost = -34063486
Max flow value      : 8854
Min cost value      : -34063486
Total time          : 0.10613 seconds

========================================

Source: 46159, Sink: 38415
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280007 seconds
Optimal solution found.
Minimum total cost = -33049028
Max flow value      : 39024
Min cost value      : -33049028
Total time          : 0.137082 seconds

========================================

Source: 65597, Sink: 4409
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275688 seconds
Optimal solution found.
Minimum total cost = -33882910
Max flow value      : 4378
Min cost value      : -33882910
Total time          : 0.102374 seconds

========================================

Source: 13942, Sink: 5067
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277903 seconds
Optimal solution found.
Minimum total cost = -31990307
Max flow value      : 65601
Min cost value      : -31990307
Total time          : 0.269979 seconds

========================================

Source: 1284, Sink: 24799
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275209 seconds
Optimal solution found.
Minimum total cost = -33580500
Max flow value      : 35940
Min cost value      : -33580500
Total time          : 0.128621 seconds

========================================

Source: 53317, Sink: 57075
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276468 seconds
Optimal solution found.
Minimum total cost = -33598256
Max flow value      : 35032
Min cost value      : -33598256
Total time          : 0.18425 seconds

========================================

Source: 2540, Sink: 56874
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279094 seconds
Optimal solution found.
Minimum total cost = -32163221
Max flow value      : 55660
Min cost value      : -32163221
Total time          : 0.157752 seconds

========================================

Source: 38126, Sink: 9664
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276117 seconds
Optimal solution found.
Minimum total cost = -34029115
Max flow value      : 4066
Min cost value      : -34029115
Total time          : 0.100819 seconds

========================================

Source: 43306, Sink: 7505
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278106 seconds
Optimal solution found.
Minimum total cost = -33918527
Max flow value      : 31710
Min cost value      : -33918527
Total time          : 0.162739 seconds

========================================

Source: 29364, Sink: 44999
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274401 seconds
Optimal solution found.
Minimum total cost = -33284176
Max flow value      : 30520
Min cost value      : -33284176
Total time          : 0.22858 seconds

========================================

Source: 33301, Sink: 55845
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280645 seconds
Optimal solution found.
Minimum total cost = -33562879
Max flow value      : 35062
Min cost value      : -33562879
Total time          : 0.174261 seconds

========================================

Source: 34258, Sink: 49607
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276451 seconds
Optimal solution found.
Minimum total cost = -33459547
Max flow value      : 35302
Min cost value      : -33459547
Total time          : 0.172767 seconds

========================================

Source: 49777, Sink: 63189
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279102 seconds
Optimal solution found.
Minimum total cost = -32728917
Max flow value      : 28299
Min cost value      : -32728917
Total time          : 0.158742 seconds

========================================

Source: 25063, Sink: 20675
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.281095 seconds
Optimal solution found.
Minimum total cost = -34033709
Max flow value      : 4098
Min cost value      : -34033709
Total time          : 0.104032 seconds

========================================

Source: 4423, Sink: 16580
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276544 seconds
Optimal solution found.
Minimum total cost = -32681537
Max flow value      : 69728
Min cost value      : -32681537
Total time          : 0.186409 seconds

========================================

Source: 17179, Sink: 33669
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276197 seconds
Optimal solution found.
Minimum total cost = -33878774
Max flow value      : 7938
Min cost value      : -33878774
Total time          : 0.105149 seconds

========================================

Source: 1225, Sink: 32275
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277233 seconds
Optimal solution found.
Minimum total cost = -33919978
Max flow value      : 35708
Min cost value      : -33919978
Total time          : 0.156961 seconds

========================================

Source: 42709, Sink: 42293
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275262 seconds
Optimal solution found.
Minimum total cost = -33714038
Max flow value      : 5094
Min cost value      : -33714038
Total time          : 0.163811 seconds

========================================

Source: 50973, Sink: 31490
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277002 seconds
Optimal solution found.
Minimum total cost = -33534546
Max flow value      : 45769
Min cost value      : -33534546
Total time          : 0.163358 seconds

========================================

Source: 13887, Sink: 58375
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275918 seconds
Optimal solution found.
Minimum total cost = -33895364
Max flow value      : 14000
Min cost value      : -33895364
Total time          : 0.151468 seconds

========================================

Source: 23584, Sink: 49348
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276286 seconds
Optimal solution found.
Minimum total cost = -33510480
Max flow value      : 42316
Min cost value      : -33510480
Total time          : 0.228689 seconds

========================================

Source: 25186, Sink: 43877
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274964 seconds
Optimal solution found.
Minimum total cost = -33760612
Max flow value      : 37360
Min cost value      : -33760612
Total time          : 0.172887 seconds

========================================

Source: 51630, Sink: 25319
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276025 seconds
Optimal solution found.
Minimum total cost = -33430998
Max flow value      : 28924
Min cost value      : -33430998
Total time          : 0.126622 seconds

========================================

Source: 52512, Sink: 17678
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277569 seconds
Optimal solution found.
Minimum total cost = -33932447
Max flow value      : 32328
Min cost value      : -33932447
Total time          : 0.144965 seconds

========================================

Source: 5119, Sink: 26831
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275629 seconds
Optimal solution found.
Minimum total cost = -33142037
Max flow value      : 34150
Min cost value      : -33142037
Total time          : 0.161769 seconds

========================================

Source: 19576, Sink: 6261
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.285975 seconds
Optimal solution found.
Minimum total cost = -33590270
Max flow value      : 37680
Min cost value      : -33590270
Total time          : 0.147931 seconds

========================================

Source: 934, Sink: 17788
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276958 seconds
Optimal solution found.
Minimum total cost = -32772663
Max flow value      : 43619
Min cost value      : -32772663
Total time          : 0.234794 seconds

========================================

Source: 41568, Sink: 47343
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275348 seconds
Optimal solution found.
Minimum total cost = -33572312
Max flow value      : 24288
Min cost value      : -33572312
Total time          : 0.187893 seconds

========================================

Source: 16658, Sink: 14480
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278849 seconds
Optimal solution found.
Minimum total cost = -31954662
Max flow value      : 61417
Min cost value      : -31954662
Total time          : 0.150752 seconds

========================================

Source: 54209, Sink: 31777
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.284647 seconds
Optimal solution found.
Minimum total cost = -33187171
Max flow value      : 45710
Min cost value      : -33187171
Total time          : 0.15482 seconds

========================================

Source: 29828, Sink: 6920
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280721 seconds
Optimal solution found.
Minimum total cost = -33386292
Max flow value      : 41006
Min cost value      : -33386292
Total time          : 0.164239 seconds

========================================

Source: 37768, Sink: 45483
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275121 seconds
Optimal solution found.
Minimum total cost = -33893266
Max flow value      : 18820
Min cost value      : -33893266
Total time          : 0.179581 seconds

========================================

Source: 55668, Sink: 47163
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276168 seconds
Optimal solution found.
Minimum total cost = -33986393
Max flow value      : 606
Min cost value      : -33986393
Total time          : 0.168376 seconds

========================================

Source: 19408, Sink: 24087
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276197 seconds
Optimal solution found.
Minimum total cost = -32183604
Max flow value      : 47226
Min cost value      : -32183604
Total time          : 0.14834 seconds

========================================

Source: 1676, Sink: 23669
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276647 seconds
Optimal solution found.
Minimum total cost = -32939951
Max flow value      : 25732
Min cost value      : -32939951
Total time          : 0.171571 seconds

========================================

Source: 2984, Sink: 8496
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.282776 seconds
Optimal solution found.
Minimum total cost = -32001862
Max flow value      : 65475
Min cost value      : -32001862
Total time          : 0.15609 seconds

========================================

Source: 51106, Sink: 23891
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.244414 seconds
Optimal solution found.
Minimum total cost = -30353630
Max flow value      : 44211
Min cost value      : -30353630
Total time          : 0.170578 seconds

========================================

Source: 15062, Sink: 1110
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27584 seconds
Optimal solution found.
Minimum total cost = -33200429
Max flow value      : 35585
Min cost value      : -33200429
Total time          : 0.145282 seconds

========================================

Source: 51322, Sink: 25287
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277207 seconds
Optimal solution found.
Minimum total cost = -31625347
Max flow value      : 54880
Min cost value      : -31625347
Total time          : 0.176008 seconds

========================================

Source: 2046, Sink: 42407
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278474 seconds
Optimal solution found.
Minimum total cost = -33618167
Max flow value      : 27834
Min cost value      : -33618167
Total time          : 0.16837 seconds

========================================

Source: 55813, Sink: 42822
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278278 seconds
Optimal solution found.
Minimum total cost = -32707419
Max flow value      : 22602
Min cost value      : -32707419
Total time          : 0.163301 seconds

========================================

Source: 50355, Sink: 6114
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280646 seconds
Optimal solution found.
Minimum total cost = -33818192
Max flow value      : 5800
Min cost value      : -33818192
Total time          : 0.16581 seconds

========================================

Source: 65954, Sink: 21093
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275267 seconds
Optimal solution found.
Minimum total cost = -32909496
Max flow value      : 33238
Min cost value      : -32909496
Total time          : 0.151661 seconds

========================================

Source: 17788, Sink: 53775
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.286807 seconds
Optimal solution found.
Minimum total cost = -33967084
Max flow value      : 16
Min cost value      : -33967084
Total time          : 0.17019 seconds

========================================

Source: 15062, Sink: 54140
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275234 seconds
Optimal solution found.
Minimum total cost = -32834379
Max flow value      : 27375
Min cost value      : -32834379
Total time          : 0.169777 seconds

========================================

Source: 53891, Sink: 20035
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27704 seconds
Optimal solution found.
Minimum total cost = -32718296
Max flow value      : 76402
Min cost value      : -32718296
Total time          : 0.159624 seconds

========================================

Source: 5588, Sink: 32828
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277293 seconds
Optimal solution found.
Minimum total cost = -33746933
Max flow value      : 39550
Min cost value      : -33746933
Total time          : 0.153038 seconds

========================================

Source: 36562, Sink: 32828
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277061 seconds
Optimal solution found.
Minimum total cost = -33479774
Max flow value      : 36588
Min cost value      : -33479774
Total time          : 0.152414 seconds

========================================

Source: 41429, Sink: 34498
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27582 seconds
Optimal solution found.
Minimum total cost = -33356291
Max flow value      : 52285
Min cost value      : -33356291
Total time          : 0.153761 seconds

========================================

Source: 6527, Sink: 52317
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280804 seconds
Optimal solution found.
Minimum total cost = -33978350
Max flow value      : 4086
Min cost value      : -33978350
Total time          : 0.161465 seconds

========================================

Source: 40303, Sink: 12377
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.233057 seconds
Optimal solution found.
Minimum total cost = -33173590
Max flow value      : 44692
Min cost value      : -33173590
Total time          : 0.146524 seconds

========================================

Source: 59384, Sink: 33821
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.2824 seconds
Optimal solution found.
Minimum total cost = -30757040
Max flow value      : 39336
Min cost value      : -30757040
Total time          : 0.25522 seconds

========================================

Source: 42280, Sink: 6829
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.236934 seconds
Optimal solution found.
Minimum total cost = -33605480
Max flow value      : 16896
Min cost value      : -33605480
Total time          : 0.0962105 seconds

========================================

Source: 27101, Sink: 41366
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.232816 seconds
Optimal solution found.
Minimum total cost = -34061768
Max flow value      : 5628
Min cost value      : -34061768
Total time          : 0.096444 seconds

========================================

Source: 54853, Sink: 43664
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274227 seconds
Optimal solution found.
Minimum total cost = -33128349
Max flow value      : 44600
Min cost value      : -33128349
Total time          : 0.155613 seconds

========================================

Source: 45781, Sink: 280
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275017 seconds
Optimal solution found.
Minimum total cost = -32498766
Max flow value      : 61067
Min cost value      : -32498766
Total time          : 0.158333 seconds

========================================

Source: 20500, Sink: 208
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.232733 seconds
Optimal solution found.
Minimum total cost = -34038427
Max flow value      : 24426
Min cost value      : -34038427
Total time          : 0.117078 seconds

========================================

Source: 38168, Sink: 13803
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275658 seconds
Optimal solution found.
Minimum total cost = -33139007
Max flow value      : 51798
Min cost value      : -33139007
Total time          : 0.157789 seconds

========================================

Source: 50131, Sink: 32790
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.231937 seconds
Optimal solution found.
Minimum total cost = -32727302
Max flow value      : 25628
Min cost value      : -32727302
Total time          : 0.110745 seconds

========================================

Source: 65731, Sink: 9500
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277851 seconds
Optimal solution found.
Minimum total cost = -33267668
Max flow value      : 30810
Min cost value      : -33267668
Total time          : 0.211447 seconds

========================================

Source: 35735, Sink: 47240
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276305 seconds
Optimal solution found.
Minimum total cost = -32979016
Max flow value      : 52590
Min cost value      : -32979016
Total time          : 0.265705 seconds

========================================

Source: 62486, Sink: 53970
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274494 seconds
Optimal solution found.
Minimum total cost = -33915512
Max flow value      : 11240
Min cost value      : -33915512
Total time          : 0.115235 seconds

========================================

Source: 27453, Sink: 64439
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275508 seconds
Optimal solution found.
Minimum total cost = -33382991
Max flow value      : 18430
Min cost value      : -33382991
Total time          : 0.170547 seconds

========================================

Source: 20401, Sink: 33473
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274836 seconds
Optimal solution found.
Minimum total cost = -33871691
Max flow value      : 12367
Min cost value      : -33871691
Total time          : 0.173272 seconds

========================================

Source: 33057, Sink: 819
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275057 seconds
Optimal solution found.
Minimum total cost = -33833009
Max flow value      : 22204
Min cost value      : -33833009
Total time          : 0.244989 seconds

========================================

Source: 24142, Sink: 2179
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.289173 seconds
Optimal solution found.
Minimum total cost = -33722784
Max flow value      : 18716
Min cost value      : -33722784
Total time          : 0.122754 seconds

========================================

Source: 1676, Sink: 32169
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.29549 seconds
Optimal solution found.
Minimum total cost = -32973672
Max flow value      : 48450
Min cost value      : -32973672
Total time          : 0.163236 seconds

========================================

Source: 11982, Sink: 34035
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275977 seconds
Optimal solution found.
Minimum total cost = -31878665
Max flow value      : 53940
Min cost value      : -31878665
Total time          : 0.156332 seconds

========================================

Source: 35195, Sink: 31173
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275672 seconds
Optimal solution found.
Minimum total cost = -33926646
Max flow value      : 3586
Min cost value      : -33926646
Total time          : 0.162814 seconds

========================================

Source: 43949, Sink: 55750
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.22771 seconds
Optimal solution found.
Minimum total cost = -32372866
Max flow value      : 49686
Min cost value      : -32372866
Total time          : 0.141344 seconds

========================================

Source: 4846, Sink: 59350
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27641 seconds
Optimal solution found.
Minimum total cost = -33576602
Max flow value      : 25966
Min cost value      : -33576602
Total time          : 0.177776 seconds

========================================

Source: 51876, Sink: 24999
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.281745 seconds
Optimal solution found.
Minimum total cost = -33180533
Max flow value      : 34842
Min cost value      : -33180533
Total time          : 0.292015 seconds

========================================

Source: 44300, Sink: 45318
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275775 seconds
Optimal solution found.
Minimum total cost = -32835858
Max flow value      : 46132
Min cost value      : -32835858
Total time          : 0.172711 seconds

========================================

Source: 11060, Sink: 49277
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277122 seconds
Optimal solution found.
Minimum total cost = -33629056
Max flow value      : 6972
Min cost value      : -33629056
Total time          : 0.183595 seconds

========================================

Source: 16421, Sink: 62277
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.281326 seconds
Optimal solution found.
Minimum total cost = -33514124
Max flow value      : 25268
Min cost value      : -33514124
Total time          : 0.158381 seconds

========================================

Source: 33488, Sink: 64705
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.281394 seconds
Optimal solution found.
Minimum total cost = -33672812
Max flow value      : 12220
Min cost value      : -33672812
Total time          : 0.126901 seconds

========================================

Source: 20867, Sink: 21201
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275867 seconds
Optimal solution found.
Minimum total cost = -33835980
Max flow value      : 4066
Min cost value      : -33835980
Total time          : 0.175136 seconds

========================================

Source: 64259, Sink: 25557
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276017 seconds
Optimal solution found.
Minimum total cost = -34008908
Max flow value      : 2676
Min cost value      : -34008908
Total time          : 0.103914 seconds

========================================

Source: 15144, Sink: 15921
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276521 seconds
Optimal solution found.
Minimum total cost = -33577183
Max flow value      : 24647
Min cost value      : -33577183
Total time          : 0.124419 seconds

========================================

Source: 43432, Sink: 59087
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.288616 seconds
Optimal solution found.
Minimum total cost = -33826344
Max flow value      : 17178
Min cost value      : -33826344
Total time          : 0.213773 seconds

========================================

Source: 5210, Sink: 24625
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276961 seconds
Optimal solution found.
Minimum total cost = -33090294
Max flow value      : 48743
Min cost value      : -33090294
Total time          : 0.157334 seconds

========================================

Source: 397, Sink: 39072
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27668 seconds
Optimal solution found.
Minimum total cost = -33173619
Max flow value      : 54999
Min cost value      : -33173619
Total time          : 0.235322 seconds

========================================

Source: 11766, Sink: 50375
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277227 seconds
Optimal solution found.
Minimum total cost = -33815129
Max flow value      : 34145
Min cost value      : -33815129
Total time          : 0.14996 seconds

========================================

Source: 22018, Sink: 29796
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27566 seconds
Optimal solution found.
Minimum total cost = -33529798
Max flow value      : 28502
Min cost value      : -33529798
Total time          : 0.130622 seconds

========================================

Source: 34828, Sink: 17815
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274737 seconds
Optimal solution found.
Minimum total cost = -33103272
Max flow value      : 42361
Min cost value      : -33103272
Total time          : 0.147849 seconds

========================================

Source: 13317, Sink: 49953
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276994 seconds
Optimal solution found.
Minimum total cost = -33616062
Max flow value      : 20590
Min cost value      : -33616062
Total time          : 0.17127 seconds

========================================

Source: 50444, Sink: 40771
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274729 seconds
Optimal solution found.
Minimum total cost = -33738946
Max flow value      : 23032
Min cost value      : -33738946
Total time          : 0.157766 seconds

========================================

Source: 93, Sink: 48479
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277378 seconds
Optimal solution found.
Minimum total cost = -33242615
Max flow value      : 34674
Min cost value      : -33242615
Total time          : 0.172449 seconds

========================================

Source: 43802, Sink: 49373
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278648 seconds
Optimal solution found.
Minimum total cost = -33492692
Max flow value      : 33864
Min cost value      : -33492692
Total time          : 0.264757 seconds

========================================

Source: 40360, Sink: 54150
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276755 seconds
Optimal solution found.
Minimum total cost = -31992746
Max flow value      : 36892
Min cost value      : -31992746
Total time          : 0.142111 seconds

========================================

Source: 48522, Sink: 20845
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277848 seconds
Optimal solution found.
Minimum total cost = -32729442
Max flow value      : 61783
Min cost value      : -32729442
Total time          : 0.171372 seconds

========================================

Source: 33405, Sink: 25952
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279536 seconds
Optimal solution found.
Minimum total cost = -30844232
Max flow value      : 44580
Min cost value      : -30844232
Total time          : 0.158885 seconds

========================================

Source: 37797, Sink: 48749
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277662 seconds
Optimal solution found.
Minimum total cost = -33085316
Max flow value      : 24466
Min cost value      : -33085316
Total time          : 0.129761 seconds

========================================

Source: 40732, Sink: 44730
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27715 seconds
Optimal solution found.
Minimum total cost = -32477394
Max flow value      : 58739
Min cost value      : -32477394
Total time          : 0.255856 seconds

========================================

Source: 35149, Sink: 3634
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275372 seconds
Optimal solution found.
Minimum total cost = -33888894
Max flow value      : 7018
Min cost value      : -33888894
Total time          : 0.170863 seconds

========================================

Source: 39155, Sink: 60742
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.283125 seconds
Optimal solution found.
Minimum total cost = -33807180
Max flow value      : 12224
Min cost value      : -33807180
Total time          : 0.116906 seconds

========================================

Source: 59548, Sink: 11214
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279851 seconds
Optimal solution found.
Minimum total cost = -33341669
Max flow value      : 55635
Min cost value      : -33341669
Total time          : 0.151899 seconds

========================================

Source: 61102, Sink: 45111
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27756 seconds
Optimal solution found.
Minimum total cost = -32867218
Max flow value      : 35102
Min cost value      : -32867218
Total time          : 0.150285 seconds

========================================

Source: 57308, Sink: 40838
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276085 seconds
Optimal solution found.
Minimum total cost = -32342808
Max flow value      : 38875
Min cost value      : -32342808
Total time          : 0.158958 seconds

========================================

Source: 63417, Sink: 28083
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274944 seconds
Optimal solution found.
Minimum total cost = -33256668
Max flow value      : 28807
Min cost value      : -33256668
Total time          : 0.116132 seconds

========================================

Source: 57577, Sink: 64670
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277507 seconds
Optimal solution found.
Minimum total cost = -34041148
Max flow value      : 18764
Min cost value      : -34041148
Total time          : 0.177397 seconds

========================================

Source: 43844, Sink: 36534
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278394 seconds
Optimal solution found.
Minimum total cost = -33606741
Max flow value      : 33345
Min cost value      : -33606741
Total time          : 0.199765 seconds

========================================

Source: 57274, Sink: 37980
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27745 seconds
Optimal solution found.
Minimum total cost = -33966092
Max flow value      : 0
Min cost value      : -33966092
Total time          : 0.150331 seconds

========================================

Source: 61756, Sink: 45052
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277228 seconds
Optimal solution found.
Minimum total cost = -33294694
Max flow value      : 20108
Min cost value      : -33294694
Total time          : 0.173244 seconds

========================================

Source: 2286, Sink: 35249
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278902 seconds
Optimal solution found.
Minimum total cost = -33525900
Max flow value      : 52900
Min cost value      : -33525900
Total time          : 0.163603 seconds

========================================

Source: 28295, Sink: 63289
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.281605 seconds
Optimal solution found.
Minimum total cost = -33582362
Max flow value      : 31312
Min cost value      : -33582362
Total time          : 0.128742 seconds

========================================

Source: 54666, Sink: 24056
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280812 seconds
Optimal solution found.
Minimum total cost = -32260805
Max flow value      : 77182
Min cost value      : -32260805
Total time          : 0.161327 seconds

========================================

Source: 39063, Sink: 51167
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275786 seconds
Optimal solution found.
Minimum total cost = -33073019
Max flow value      : 48055
Min cost value      : -33073019
Total time          : 0.159573 seconds

========================================

Source: 63391, Sink: 17997
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275702 seconds
Optimal solution found.
Minimum total cost = -33892762
Max flow value      : 29503
Min cost value      : -33892762
Total time          : 0.160897 seconds

========================================

Source: 41695, Sink: 29730
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27491 seconds
Optimal solution found.
Minimum total cost = -34042987
Max flow value      : 21970
Min cost value      : -34042987
Total time          : 0.121438 seconds

========================================

Source: 36111, Sink: 54666
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278314 seconds
Optimal solution found.
Minimum total cost = -32109242
Max flow value      : 71393
Min cost value      : -32109242
Total time          : 0.163025 seconds

========================================

Source: 24710, Sink: 4198
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276613 seconds
Optimal solution found.
Minimum total cost = -34008482
Max flow value      : 20148
Min cost value      : -34008482
Total time          : 0.149114 seconds

========================================

Source: 38800, Sink: 36493
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274778 seconds
Optimal solution found.
Minimum total cost = -33733168
Max flow value      : 29452
Min cost value      : -33733168
Total time          : 0.24045 seconds

========================================

Source: 19252, Sink: 17561
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280337 seconds
Optimal solution found.
Minimum total cost = -31854152
Max flow value      : 56455
Min cost value      : -31854152
Total time          : 0.174752 seconds

========================================

Source: 39368, Sink: 19393
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274412 seconds
Optimal solution found.
Minimum total cost = -33856241
Max flow value      : 17628
Min cost value      : -33856241
Total time          : 0.11466 seconds

========================================

Source: 1284, Sink: 2497
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275721 seconds
Optimal solution found.
Minimum total cost = -33846812
Max flow value      : 8520
Min cost value      : -33846812
Total time          : 0.166131 seconds

========================================

Source: 2046, Sink: 63166
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276176 seconds
Optimal solution found.
Minimum total cost = -33540619
Max flow value      : 12029
Min cost value      : -33540619
Total time          : 0.168397 seconds

========================================

Source: 39060, Sink: 439
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277893 seconds
Optimal solution found.
Minimum total cost = -33854574
Max flow value      : 28932
Min cost value      : -33854574
Total time          : 0.186788 seconds

========================================

Source: 57248, Sink: 51611
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.295308 seconds
Optimal solution found.
Minimum total cost = -33949702
Max flow value      : 1490
Min cost value      : -33949702
Total time          : 0.101954 seconds

========================================

Source: 12329, Sink: 45580
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278256 seconds
Optimal solution found.
Minimum total cost = -33722438
Max flow value      : 13116
Min cost value      : -33722438
Total time          : 0.117092 seconds

========================================

Source: 25105, Sink: 18012
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.28139 seconds
Optimal solution found.
Minimum total cost = -32275246
Max flow value      : 53494
Min cost value      : -32275246
Total time          : 0.170214 seconds

========================================

Source: 17342, Sink: 334
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275294 seconds
Optimal solution found.
Minimum total cost = -33905108
Max flow value      : 5544
Min cost value      : -33905108
Total time          : 0.162712 seconds

========================================

Source: 26921, Sink: 63818
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275375 seconds
Optimal solution found.
Minimum total cost = -31723886
Max flow value      : 55890
Min cost value      : -31723886
Total time          : 0.172083 seconds

========================================

Source: 18363, Sink: 59500
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278416 seconds
Optimal solution found.
Minimum total cost = -33557652
Max flow value      : 47021
Min cost value      : -33557652
Total time          : 0.18478 seconds

========================================

Source: 13589, Sink: 10645
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276494 seconds
Optimal solution found.
Minimum total cost = -32882279
Max flow value      : 33597
Min cost value      : -32882279
Total time          : 0.120268 seconds

========================================

Source: 38183, Sink: 26217
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275905 seconds
Optimal solution found.
Minimum total cost = -33341996
Max flow value      : 44769
Min cost value      : -33341996
Total time          : 0.160364 seconds

========================================

Source: 19974, Sink: 34880
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.285042 seconds
Optimal solution found.
Minimum total cost = -32342585
Max flow value      : 43537
Min cost value      : -32342585
Total time          : 0.158351 seconds

========================================

Source: 49207, Sink: 22876
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275338 seconds
Optimal solution found.
Minimum total cost = -33803216
Max flow value      : 21264
Min cost value      : -33803216
Total time          : 0.158704 seconds

========================================

Source: 1110, Sink: 39063
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277402 seconds
Optimal solution found.
Minimum total cost = -33086486
Max flow value      : 36216
Min cost value      : -33086486
Total time          : 0.167063 seconds

========================================

Source: 24245, Sink: 33771
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278319 seconds
Optimal solution found.
Minimum total cost = -33562920
Max flow value      : 30611
Min cost value      : -33562920
Total time          : 0.220328 seconds

========================================

Source: 6311, Sink: 63113
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276545 seconds
Optimal solution found.
Minimum total cost = -33914137
Max flow value      : 24526
Min cost value      : -33914137
Total time          : 0.188475 seconds

========================================

Source: 64993, Sink: 25017
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.282669 seconds
Optimal solution found.
Minimum total cost = -33559868
Max flow value      : 46963
Min cost value      : -33559868
Total time          : 0.167667 seconds

========================================

Source: 32641, Sink: 62926
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.296409 seconds
Optimal solution found.
Minimum total cost = -32419983
Max flow value      : 34278
Min cost value      : -32419983
Total time          : 0.164979 seconds

========================================

Source: 49098, Sink: 37256
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279396 seconds
Optimal solution found.
Minimum total cost = -33477215
Max flow value      : 33338
Min cost value      : -33477215
Total time          : 0.166394 seconds

========================================

Source: 12121, Sink: 56660
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275451 seconds
Optimal solution found.
Minimum total cost = -33679590
Max flow value      : 14844
Min cost value      : -33679590
Total time          : 0.134861 seconds

========================================

Source: 25071, Sink: 63600
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277287 seconds
Optimal solution found.
Minimum total cost = -33885933
Max flow value      : 2258
Min cost value      : -33885933
Total time          : 0.0912876 seconds

========================================

Source: 36672, Sink: 8830
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274505 seconds
Optimal solution found.
Minimum total cost = -33205256
Max flow value      : 34053
Min cost value      : -33205256
Total time          : 0.124248 seconds

========================================

Source: 2754, Sink: 15834
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275974 seconds
Optimal solution found.
Minimum total cost = -33938636
Max flow value      : 2496
Min cost value      : -33938636
Total time          : 0.17994 seconds

========================================

Source: 64341, Sink: 10338
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275433 seconds
Optimal solution found.
Minimum total cost = -32226140
Max flow value      : 79759
Min cost value      : -32226140
Total time          : 0.148814 seconds

========================================

Source: 40880, Sink: 48133
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275676 seconds
Optimal solution found.
Minimum total cost = -33548630
Max flow value      : 56058
Min cost value      : -33548630
Total time          : 0.223382 seconds

========================================

Source: 59384, Sink: 13413
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.281024 seconds
Optimal solution found.
Minimum total cost = -32453842
Max flow value      : 41725
Min cost value      : -32453842
Total time          : 0.146223 seconds

========================================

Source: 29627, Sink: 12197
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274333 seconds
Optimal solution found.
Minimum total cost = -33866236
Max flow value      : 10480
Min cost value      : -33866236
Total time          : 0.111886 seconds

========================================

Source: 15955, Sink: 20870
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277455 seconds
Optimal solution found.
Minimum total cost = -30071967
Max flow value      : 42711
Min cost value      : -30071967
Total time          : 0.194101 seconds

========================================

Source: 28706, Sink: 18538
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.230941 seconds
Optimal solution found.
Minimum total cost = -32625051
Max flow value      : 33634
Min cost value      : -32625051
Total time          : 0.148381 seconds

========================================

Source: 1168, Sink: 15220
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277959 seconds
Optimal solution found.
Minimum total cost = -33313677
Max flow value      : 31498
Min cost value      : -33313677
Total time          : 0.207712 seconds

========================================

Source: 17193, Sink: 6210
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277145 seconds
Optimal solution found.
Minimum total cost = -34049474
Max flow value      : 12828
Min cost value      : -34049474
Total time          : 0.164244 seconds

========================================

Source: 42366, Sink: 44823
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275242 seconds
Optimal solution found.
Minimum total cost = -33303616
Max flow value      : 39148
Min cost value      : -33303616
Total time          : 0.161378 seconds

========================================

Source: 62806, Sink: 13714
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278051 seconds
Optimal solution found.
Minimum total cost = -33922878
Max flow value      : 1394
Min cost value      : -33922878
Total time          : 0.17844 seconds

========================================

Source: 38716, Sink: 42390
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277735 seconds
Optimal solution found.
Minimum total cost = -34001347
Max flow value      : 14102
Min cost value      : -34001347
Total time          : 0.176789 seconds

========================================

Source: 62272, Sink: 17731
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277303 seconds
Optimal solution found.
Minimum total cost = -33664792
Max flow value      : 24104
Min cost value      : -33664792
Total time          : 0.164115 seconds

========================================

Source: 21462, Sink: 61200
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274413 seconds
Optimal solution found.
Minimum total cost = -33757548
Max flow value      : 21952
Min cost value      : -33757548
Total time          : 0.136024 seconds

========================================

Source: 61940, Sink: 49482
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274331 seconds
Optimal solution found.
Minimum total cost = -33086193
Max flow value      : 41476
Min cost value      : -33086193
Total time          : 0.167111 seconds

========================================

Source: 50311, Sink: 44900
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276199 seconds
Optimal solution found.
Minimum total cost = -33376999
Max flow value      : 24429
Min cost value      : -33376999
Total time          : 0.15968 seconds

========================================

Source: 42270, Sink: 9756
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277537 seconds
Optimal solution found.
Minimum total cost = -33378838
Max flow value      : 51962
Min cost value      : -33378838
Total time          : 0.16315 seconds

========================================

Source: 63677, Sink: 19469
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.273947 seconds
Optimal solution found.
Minimum total cost = -32530176
Max flow value      : 34499
Min cost value      : -32530176
Total time          : 0.147471 seconds

========================================

Source: 38723, Sink: 57337
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276739 seconds
Optimal solution found.
Minimum total cost = -33025907
Max flow value      : 54800
Min cost value      : -33025907
Total time          : 0.176724 seconds

========================================

Source: 61697, Sink: 49646
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27517 seconds
Optimal solution found.
Minimum total cost = -33945302
Max flow value      : 1890
Min cost value      : -33945302
Total time          : 0.0938067 seconds

========================================

Source: 31015, Sink: 22827
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.31538 seconds
Optimal solution found.
Minimum total cost = -33966092
Max flow value      : 9768
Min cost value      : -33966092
Total time          : 0.166002 seconds

========================================

Source: 6389, Sink: 42941
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275991 seconds
Optimal solution found.
Minimum total cost = -33325159
Max flow value      : 56227
Min cost value      : -33325159
Total time          : 0.157398 seconds

========================================

Source: 3900, Sink: 3933
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278658 seconds
Optimal solution found.
Minimum total cost = -33682988
Max flow value      : 27348
Min cost value      : -33682988
Total time          : 0.134547 seconds

========================================

Source: 36758, Sink: 12529
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275645 seconds
Optimal solution found.
Minimum total cost = -33715625
Max flow value      : 28856
Min cost value      : -33715625
Total time          : 0.118365 seconds

========================================

Source: 59994, Sink: 20171
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27629 seconds
Optimal solution found.
Minimum total cost = -33880699
Max flow value      : 6983
Min cost value      : -33880699
Total time          : 0.132272 seconds

========================================

Source: 15972, Sink: 36697
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275358 seconds
Optimal solution found.
Minimum total cost = -34020300
Max flow value      : 6776
Min cost value      : -34020300
Total time          : 0.104364 seconds

========================================

Source: 26860, Sink: 39504
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274894 seconds
Optimal solution found.
Minimum total cost = -33847253
Max flow value      : 11318
Min cost value      : -33847253
Total time          : 0.160315 seconds

========================================

Source: 60804, Sink: 11249
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276897 seconds
Optimal solution found.
Minimum total cost = -33213020
Max flow value      : 41270
Min cost value      : -33213020
Total time          : 0.150321 seconds

========================================

Source: 45948, Sink: 38584
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27572 seconds
Optimal solution found.
Minimum total cost = -33848720
Max flow value      : 6366
Min cost value      : -33848720
Total time          : 0.108878 seconds

========================================

Source: 24608, Sink: 49679
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277766 seconds
Optimal solution found.
Minimum total cost = -33358067
Max flow value      : 24188
Min cost value      : -33358067
Total time          : 0.136034 seconds

========================================

Source: 9729, Sink: 12626
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275581 seconds
Optimal solution found.
Minimum total cost = -33919232
Max flow value      : 18744
Min cost value      : -33919232
Total time          : 0.111018 seconds

========================================

Source: 18495, Sink: 64061
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.288939 seconds
Optimal solution found.
Minimum total cost = -33717842
Max flow value      : 17882
Min cost value      : -33717842
Total time          : 0.130299 seconds

========================================

Source: 34243, Sink: 45877
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276688 seconds
Optimal solution found.
Minimum total cost = -31892436
Max flow value      : 59758
Min cost value      : -31892436
Total time          : 0.168077 seconds

========================================

Source: 64834, Sink: 39601
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279126 seconds
Optimal solution found.
Minimum total cost = -33746852
Max flow value      : 20880
Min cost value      : -33746852
Total time          : 0.115484 seconds

========================================

Source: 5996, Sink: 9926
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275361 seconds
Optimal solution found.
Minimum total cost = -33479367
Max flow value      : 38938
Min cost value      : -33479367
Total time          : 0.178217 seconds

========================================

Source: 53176, Sink: 4826
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275651 seconds
Optimal solution found.
Minimum total cost = -33433391
Max flow value      : 30378
Min cost value      : -33433391
Total time          : 0.150591 seconds

========================================

Source: 38780, Sink: 4770
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276566 seconds
Optimal solution found.
Minimum total cost = -33905042
Max flow value      : 45141
Min cost value      : -33905042
Total time          : 0.196487 seconds

========================================

Source: 59811, Sink: 20770
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278362 seconds
Optimal solution found.
Minimum total cost = -33147764
Max flow value      : 47884
Min cost value      : -33147764
Total time          : 0.163935 seconds

========================================

Source: 3634, Sink: 7006
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280119 seconds
Optimal solution found.
Minimum total cost = -34025324
Max flow value      : 7404
Min cost value      : -34025324
Total time          : 0.165357 seconds

========================================

Source: 62033, Sink: 8835
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27882 seconds
Optimal solution found.
Minimum total cost = -32144776
Max flow value      : 45622
Min cost value      : -32144776
Total time          : 0.159245 seconds

========================================

Source: 43552, Sink: 33449
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276996 seconds
Optimal solution found.
Minimum total cost = -33567448
Max flow value      : 25782
Min cost value      : -33567448
Total time          : 0.165687 seconds

========================================

Source: 28099, Sink: 28888
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.2773 seconds
Optimal solution found.
Minimum total cost = -33415114
Max flow value      : 43072
Min cost value      : -33415114
Total time          : 0.165279 seconds

========================================

Source: 2134, Sink: 15795
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276165 seconds
Optimal solution found.
Minimum total cost = -33059724
Max flow value      : 36203
Min cost value      : -33059724
Total time          : 0.15209 seconds

========================================

Source: 50291, Sink: 5940
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274543 seconds
Optimal solution found.
Minimum total cost = -33898977
Max flow value      : 26846
Min cost value      : -33898977
Total time          : 0.120873 seconds

========================================

Source: 40828, Sink: 28393
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275541 seconds
Optimal solution found.
Minimum total cost = -33412913
Max flow value      : 49584
Min cost value      : -33412913
Total time          : 0.148677 seconds

========================================

Source: 54096, Sink: 41865
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274742 seconds
Optimal solution found.
Minimum total cost = -32615918
Max flow value      : 55325
Min cost value      : -32615918
Total time          : 0.171627 seconds

========================================

Source: 11414, Sink: 62764
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275799 seconds
Optimal solution found.
Minimum total cost = -32716951
Max flow value      : 46014
Min cost value      : -32716951
Total time          : 0.181772 seconds

========================================

Source: 27077, Sink: 25403
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27605 seconds
Optimal solution found.
Minimum total cost = -33788406
Max flow value      : 23283
Min cost value      : -33788406
Total time          : 0.140969 seconds

========================================

Source: 11209, Sink: 66071
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275322 seconds
Optimal solution found.
Minimum total cost = -32401928
Max flow value      : 34050
Min cost value      : -32401928
Total time          : 0.143442 seconds

========================================

Source: 24331, Sink: 53386
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275981 seconds
Optimal solution found.
Minimum total cost = -32809712
Max flow value      : 48202
Min cost value      : -32809712
Total time          : 0.175309 seconds

========================================

Source: 18995, Sink: 35445
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.291201 seconds
Optimal solution found.
Minimum total cost = -33920816
Max flow value      : 4116
Min cost value      : -33920816
Total time          : 0.0946009 seconds

========================================

Source: 39018, Sink: 34998
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27966 seconds
Optimal solution found.
Minimum total cost = -33316247
Max flow value      : 31940
Min cost value      : -33316247
Total time          : 0.148937 seconds

========================================

Source: 43738, Sink: 45274
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.3197 seconds
Optimal solution found.
Minimum total cost = -34003309
Max flow value      : 22644
Min cost value      : -34003309
Total time          : 0.169407 seconds

========================================

Source: 62869, Sink: 51332
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278252 seconds
Optimal solution found.
Minimum total cost = -32959953
Max flow value      : 45691
Min cost value      : -32959953
Total time          : 0.161863 seconds

========================================

Source: 8575, Sink: 34610
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279101 seconds
Optimal solution found.
Minimum total cost = -33926012
Max flow value      : 10020
Min cost value      : -33926012
Total time          : 0.116587 seconds

========================================

Source: 41303, Sink: 28376
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275182 seconds
Optimal solution found.
Minimum total cost = -33729856
Max flow value      : 13619
Min cost value      : -33729856
Total time          : 0.120859 seconds

========================================

Source: 8069, Sink: 318
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.295494 seconds
Optimal solution found.
Minimum total cost = -33684104
Max flow value      : 31138
Min cost value      : -33684104
Total time          : 0.154848 seconds

========================================

Source: 40163, Sink: 12294
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276566 seconds
Optimal solution found.
Minimum total cost = -33963456
Max flow value      : 5272
Min cost value      : -33963456
Total time          : 0.174475 seconds

========================================

Source: 55480, Sink: 45794
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277423 seconds
Optimal solution found.
Minimum total cost = -32572664
Max flow value      : 48987
Min cost value      : -32572664
Total time          : 0.163876 seconds

========================================

Source: 46629, Sink: 33840
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274971 seconds
Optimal solution found.
Minimum total cost = -33966092
Max flow value      : 22598
Min cost value      : -33966092
Total time          : 0.124451 seconds

========================================

Source: 56010, Sink: 50465
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.259231 seconds
Optimal solution found.
Minimum total cost = -34052037
Max flow value      : 12700
Min cost value      : -34052037
Total time          : 0.153443 seconds

========================================

Source: 40532, Sink: 24359
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.287793 seconds
Optimal solution found.
Minimum total cost = -33384742
Max flow value      : 14330
Min cost value      : -33384742
Total time          : 0.213854 seconds

========================================

Source: 4690, Sink: 40242
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276404 seconds
Optimal solution found.
Minimum total cost = -33924134
Max flow value      : 18560
Min cost value      : -33924134
Total time          : 0.123824 seconds

========================================

Source: 6607, Sink: 6104
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.232584 seconds
Optimal solution found.
Minimum total cost = -31671257
Max flow value      : 32889
Min cost value      : -31671257
Total time          : 0.127392 seconds

========================================

Source: 20805, Sink: 25832
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.230779 seconds
Optimal solution found.
Minimum total cost = -33913752
Max flow value      : 18464
Min cost value      : -33913752
Total time          : 0.127108 seconds

========================================

Source: 49931, Sink: 34995
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.232876 seconds
Optimal solution found.
Minimum total cost = -33437854
Max flow value      : 19853
Min cost value      : -33437854
Total time          : 0.126885 seconds

========================================

Source: 11795, Sink: 25658
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278132 seconds
Optimal solution found.
Minimum total cost = -32808932
Max flow value      : 69390
Min cost value      : -32808932
Total time          : 0.168568 seconds

========================================

Source: 47854, Sink: 54124
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276591 seconds
Optimal solution found.
Minimum total cost = -33493988
Max flow value      : 35659
Min cost value      : -33493988
Total time          : 0.167388 seconds

========================================

Source: 16421, Sink: 28689
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276299 seconds
Optimal solution found.
Minimum total cost = -33834538
Max flow value      : 10684
Min cost value      : -33834538
Total time          : 0.160778 seconds

========================================

Source: 14298, Sink: 39320
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275051 seconds
Optimal solution found.
Minimum total cost = -33656173
Max flow value      : 33422
Min cost value      : -33656173
Total time          : 0.161314 seconds

========================================

Source: 3385, Sink: 31675
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277076 seconds
Optimal solution found.
Minimum total cost = -33037067
Max flow value      : 54347
Min cost value      : -33037067
Total time          : 0.151548 seconds

========================================

Source: 20451, Sink: 32703
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275809 seconds
Optimal solution found.
Minimum total cost = -31697532
Max flow value      : 53790
Min cost value      : -31697532
Total time          : 0.159996 seconds

========================================

Source: 23163, Sink: 41754
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276871 seconds
Optimal solution found.
Minimum total cost = -33668451
Max flow value      : 30795
Min cost value      : -33668451
Total time          : 0.169871 seconds

========================================

Source: 117, Sink: 18037
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275474 seconds
Optimal solution found.
Minimum total cost = -33203637
Max flow value      : 51556
Min cost value      : -33203637
Total time          : 0.161679 seconds

========================================

Source: 19919, Sink: 13832
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277201 seconds
Optimal solution found.
Minimum total cost = -33348700
Max flow value      : 25988
Min cost value      : -33348700
Total time          : 0.227124 seconds

========================================

Source: 27662, Sink: 26578
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276194 seconds
Optimal solution found.
Minimum total cost = -33923184
Max flow value      : 2524
Min cost value      : -33923184
Total time          : 0.108851 seconds

========================================

Source: 11016, Sink: 9776
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275378 seconds
Optimal solution found.
Minimum total cost = -33816620
Max flow value      : 37368
Min cost value      : -33816620
Total time          : 0.123937 seconds

========================================

Source: 125, Sink: 44639
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276838 seconds
Optimal solution found.
Minimum total cost = -33799688
Max flow value      : 15848
Min cost value      : -33799688
Total time          : 0.191413 seconds

========================================

Source: 37065, Sink: 46486
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.283492 seconds
Optimal solution found.
Minimum total cost = -33640404
Max flow value      : 19496
Min cost value      : -33640404
Total time          : 0.169216 seconds

========================================

Source: 50852, Sink: 24305
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276326 seconds
Optimal solution found.
Minimum total cost = -32644166
Max flow value      : 57966
Min cost value      : -32644166
Total time          : 0.147401 seconds

========================================

Source: 18685, Sink: 23874
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275886 seconds
Optimal solution found.
Minimum total cost = -33438201
Max flow value      : 42498
Min cost value      : -33438201
Total time          : 0.158583 seconds

========================================

Source: 58858, Sink: 5359
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27647 seconds
Optimal solution found.
Minimum total cost = -33564196
Max flow value      : 24802
Min cost value      : -33564196
Total time          : 0.160299 seconds

========================================

Source: 2392, Sink: 43390
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276098 seconds
Optimal solution found.
Minimum total cost = -33236757
Max flow value      : 41777
Min cost value      : -33236757
Total time          : 0.151516 seconds

========================================

Source: 58459, Sink: 35046
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.234891 seconds
Optimal solution found.
Minimum total cost = -32125488
Max flow value      : 36092
Min cost value      : -32125488
Total time          : 0.180251 seconds

========================================

Source: 65339, Sink: 5448
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27568 seconds
Optimal solution found.
Minimum total cost = -33473844
Max flow value      : 49526
Min cost value      : -33473844
Total time          : 0.193496 seconds

========================================

Source: 99, Sink: 19224
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276426 seconds
Optimal solution found.
Minimum total cost = -33513834
Max flow value      : 43983
Min cost value      : -33513834
Total time          : 0.177556 seconds

========================================

Source: 20018, Sink: 5608
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27903 seconds
Optimal solution found.
Minimum total cost = -33725623
Max flow value      : 21077
Min cost value      : -33725623
Total time          : 0.162138 seconds

========================================

Source: 23734, Sink: 48769
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276966 seconds
Optimal solution found.
Minimum total cost = -33703596
Max flow value      : 10096
Min cost value      : -33703596
Total time          : 0.174204 seconds

========================================

Source: 16318, Sink: 18423
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275195 seconds
Optimal solution found.
Minimum total cost = -33933950
Max flow value      : 26038
Min cost value      : -33933950
Total time          : 0.176531 seconds

========================================

Source: 6563, Sink: 9525
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275461 seconds
Optimal solution found.
Minimum total cost = -32770671
Max flow value      : 63894
Min cost value      : -32770671
Total time          : 0.147518 seconds

========================================

Source: 45781, Sink: 14953
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279373 seconds
Optimal solution found.
Minimum total cost = -33826379
Max flow value      : 13306
Min cost value      : -33826379
Total time          : 0.17646 seconds

========================================

Source: 12524, Sink: 38731
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.281915 seconds
Optimal solution found.
Minimum total cost = -32891980
Max flow value      : 41312
Min cost value      : -32891980
Total time          : 0.179291 seconds

========================================

Source: 17159, Sink: 5996
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276834 seconds
Optimal solution found.
Minimum total cost = -32171782
Max flow value      : 63242
Min cost value      : -32171782
Total time          : 0.149462 seconds

========================================

Source: 26955, Sink: 58476
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.283234 seconds
Optimal solution found.
Minimum total cost = -33570436
Max flow value      : 32650
Min cost value      : -33570436
Total time          : 0.180114 seconds

========================================

Source: 13485, Sink: 44940
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.292584 seconds
Optimal solution found.
Minimum total cost = -33887731
Max flow value      : 6814
Min cost value      : -33887731
Total time          : 0.104116 seconds

========================================

Source: 18505, Sink: 11409
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277301 seconds
Optimal solution found.
Minimum total cost = -34076252
Max flow value      : 11016
Min cost value      : -34076252
Total time          : 0.112779 seconds

========================================

Source: 7840, Sink: 17447
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276627 seconds
Optimal solution found.
Minimum total cost = -33509237
Max flow value      : 34458
Min cost value      : -33509237
Total time          : 0.168895 seconds

========================================

Source: 39385, Sink: 47270
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274531 seconds
Optimal solution found.
Minimum total cost = -33060250
Max flow value      : 55496
Min cost value      : -33060250
Total time          : 0.15747 seconds

========================================

Source: 24029, Sink: 28060
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275897 seconds
Optimal solution found.
Minimum total cost = -33211394
Max flow value      : 46298
Min cost value      : -33211394
Total time          : 0.170276 seconds

========================================

Source: 8020, Sink: 62092
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275432 seconds
Optimal solution found.
Minimum total cost = -33484968
Max flow value      : 31827
Min cost value      : -33484968
Total time          : 0.182247 seconds

========================================

Source: 32449, Sink: 59801
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278866 seconds
Optimal solution found.
Minimum total cost = -33562780
Max flow value      : 30074
Min cost value      : -33562780
Total time          : 0.144462 seconds

========================================

Source: 39651, Sink: 28325
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27571 seconds
Optimal solution found.
Minimum total cost = -33890012
Max flow value      : 2536
Min cost value      : -33890012
Total time          : 0.0980953 seconds

========================================

Source: 52580, Sink: 50389
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276487 seconds
Optimal solution found.
Minimum total cost = -33078956
Max flow value      : 36964
Min cost value      : -33078956
Total time          : 0.169379 seconds

========================================

Source: 38521, Sink: 15416
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275751 seconds
Optimal solution found.
Minimum total cost = -33934207
Max flow value      : 3472
Min cost value      : -33934207
Total time          : 0.117989 seconds

========================================

Source: 53092, Sink: 48546
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.23271 seconds
Optimal solution found.
Minimum total cost = -33776042
Max flow value      : 18100
Min cost value      : -33776042
Total time          : 0.108735 seconds

========================================

Source: 18535, Sink: 5727
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.281578 seconds
Optimal solution found.
Minimum total cost = -31686011
Max flow value      : 55369
Min cost value      : -31686011
Total time          : 0.154455 seconds

========================================

Source: 36040, Sink: 11727
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280029 seconds
Optimal solution found.
Minimum total cost = -33874528
Max flow value      : 23098
Min cost value      : -33874528
Total time          : 0.116524 seconds

========================================

Source: 20108, Sink: 3029
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275239 seconds
Optimal solution found.
Minimum total cost = -32388477
Max flow value      : 55797
Min cost value      : -32388477
Total time          : 0.214581 seconds

========================================

Source: 22976, Sink: 41710
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278123 seconds
Optimal solution found.
Minimum total cost = -32429030
Max flow value      : 45621
Min cost value      : -32429030
Total time          : 0.145683 seconds

========================================

Source: 8283, Sink: 31421
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276301 seconds
Optimal solution found.
Minimum total cost = -31925009
Max flow value      : 66990
Min cost value      : -31925009
Total time          : 0.153444 seconds

========================================

Source: 2734, Sink: 27709
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277 seconds
Optimal solution found.
Minimum total cost = -33742601
Max flow value      : 37505
Min cost value      : -33742601
Total time          : 0.159287 seconds

========================================

Source: 46791, Sink: 49706
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277065 seconds
Optimal solution found.
Minimum total cost = -31225523
Max flow value      : 68914
Min cost value      : -31225523
Total time          : 0.162073 seconds

========================================

Source: 50511, Sink: 41017
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276916 seconds
Optimal solution found.
Minimum total cost = -33856046
Max flow value      : 4976
Min cost value      : -33856046
Total time          : 0.11821 seconds

========================================

Source: 45884, Sink: 63825
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.2777 seconds
Optimal solution found.
Minimum total cost = -33187183
Max flow value      : 25538
Min cost value      : -33187183
Total time          : 0.151023 seconds

========================================

Source: 39529, Sink: 52946
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275444 seconds
Optimal solution found.
Minimum total cost = -33350461
Max flow value      : 42662
Min cost value      : -33350461
Total time          : 0.155858 seconds

========================================

Source: 26300, Sink: 16
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274752 seconds
Optimal solution found.
Minimum total cost = -33863150
Max flow value      : 14706
Min cost value      : -33863150
Total time          : 0.176298 seconds

========================================

Source: 64912, Sink: 37105
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27729 seconds
Optimal solution found.
Minimum total cost = -33822366
Max flow value      : 29229
Min cost value      : -33822366
Total time          : 0.174149 seconds

========================================

Source: 35582, Sink: 60256
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276442 seconds
Optimal solution found.
Minimum total cost = -31996813
Max flow value      : 44639
Min cost value      : -31996813
Total time          : 0.172747 seconds

========================================

Source: 20731, Sink: 13527
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279182 seconds
Optimal solution found.
Minimum total cost = -33562015
Max flow value      : 50493
Min cost value      : -33562015
Total time          : 0.159126 seconds

========================================

Source: 40094, Sink: 49026
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.285345 seconds
Optimal solution found.
Minimum total cost = -33586946
Max flow value      : 28196
Min cost value      : -33586946
Total time          : 0.165323 seconds

========================================

Source: 4934, Sink: 24474
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277558 seconds
Optimal solution found.
Minimum total cost = -33477391
Max flow value      : 33211
Min cost value      : -33477391
Total time          : 0.22865 seconds

========================================

Source: 60882, Sink: 32518
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277119 seconds
Optimal solution found.
Minimum total cost = -33813181
Max flow value      : 27802
Min cost value      : -33813181
Total time          : 0.166789 seconds

========================================

Source: 30075, Sink: 13009
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27525 seconds
Optimal solution found.
Minimum total cost = -32732562
Max flow value      : 25103
Min cost value      : -32732562
Total time          : 0.296284 seconds

========================================

Source: 21065, Sink: 38180
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278799 seconds
Optimal solution found.
Minimum total cost = -32392833
Max flow value      : 40662
Min cost value      : -32392833
Total time          : 0.155859 seconds

========================================

Source: 6121, Sink: 29520
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275301 seconds
Optimal solution found.
Minimum total cost = -34026984
Max flow value      : 17888
Min cost value      : -34026984
Total time          : 0.185563 seconds

========================================

Source: 9288, Sink: 6311
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.232144 seconds
Optimal solution found.
Minimum total cost = -30716884
Max flow value      : 80317
Min cost value      : -30716884
Total time          : 0.188879 seconds

========================================

Source: 19519, Sink: 47795
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277232 seconds
Optimal solution found.
Minimum total cost = -33734828
Max flow value      : 25696
Min cost value      : -33734828
Total time          : 0.170706 seconds

========================================

Source: 64500, Sink: 16565
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.230067 seconds
Optimal solution found.
Minimum total cost = -33769871
Max flow value      : 27156
Min cost value      : -33769871
Total time          : 0.126513 seconds

========================================

Source: 6304, Sink: 12846
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277562 seconds
Optimal solution found.
Minimum total cost = -33994017
Max flow value      : 2234
Min cost value      : -33994017
Total time          : 0.156732 seconds

========================================

Source: 64210, Sink: 58330
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275975 seconds
Optimal solution found.
Minimum total cost = -30887390
Max flow value      : 76700
Min cost value      : -30887390
Total time          : 0.165303 seconds

========================================

Source: 44187, Sink: 3775
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27804 seconds
Optimal solution found.
Minimum total cost = -33452125
Max flow value      : 27456
Min cost value      : -33452125
Total time          : 0.242934 seconds

========================================

Source: 50736, Sink: 43902
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.290004 seconds
Optimal solution found.
Minimum total cost = -33501298
Max flow value      : 39855
Min cost value      : -33501298
Total time          : 0.12294 seconds

========================================

Source: 12244, Sink: 8218
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275503 seconds
Optimal solution found.
Minimum total cost = -33514280
Max flow value      : 48562
Min cost value      : -33514280
Total time          : 0.182086 seconds

========================================

Source: 22817, Sink: 615
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277747 seconds
Optimal solution found.
Minimum total cost = -33469772
Max flow value      : 16544
Min cost value      : -33469772
Total time          : 0.168079 seconds

========================================

Source: 64796, Sink: 63914
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275169 seconds
Optimal solution found.
Minimum total cost = -32351652
Max flow value      : 64618
Min cost value      : -32351652
Total time          : 0.169213 seconds

========================================

Source: 34253, Sink: 12037
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.281832 seconds
Optimal solution found.
Minimum total cost = -31714940
Max flow value      : 43953
Min cost value      : -31714940
Total time          : 0.193167 seconds

========================================

Source: 28268, Sink: 46651
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274317 seconds
Optimal solution found.
Minimum total cost = -32976048
Max flow value      : 48025
Min cost value      : -32976048
Total time          : 0.155268 seconds

========================================

Source: 46929, Sink: 41797
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.231286 seconds
Optimal solution found.
Minimum total cost = -33925940
Max flow value      : 5736
Min cost value      : -33925940
Total time          : 0.116335 seconds

========================================

Source: 44142, Sink: 4007
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278715 seconds
Optimal solution found.
Minimum total cost = -33880558
Max flow value      : 26310
Min cost value      : -33880558
Total time          : 0.192167 seconds

========================================

Source: 7931, Sink: 26256
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275454 seconds
Optimal solution found.
Minimum total cost = -32862571
Max flow value      : 39056
Min cost value      : -32862571
Total time          : 0.149023 seconds

========================================

Source: 44818, Sink: 48828
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277711 seconds
Optimal solution found.
Minimum total cost = -33363959
Max flow value      : 47565
Min cost value      : -33363959
Total time          : 0.16761 seconds

========================================

Source: 18845, Sink: 20924
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276079 seconds
Optimal solution found.
Minimum total cost = -33024707
Max flow value      : 19410
Min cost value      : -33024707
Total time          : 0.121503 seconds

========================================

Source: 22910, Sink: 14052
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277687 seconds
Optimal solution found.
Minimum total cost = -33995462
Max flow value      : 2670
Min cost value      : -33995462
Total time          : 0.109818 seconds

========================================

Source: 12599, Sink: 57816
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276214 seconds
Optimal solution found.
Minimum total cost = -33746540
Max flow value      : 27444
Min cost value      : -33746540
Total time          : 0.134904 seconds

========================================

Source: 20045, Sink: 8620
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276984 seconds
Optimal solution found.
Minimum total cost = -31063051
Max flow value      : 45869
Min cost value      : -31063051
Total time          : 0.15758 seconds

========================================

Source: 16666, Sink: 14160
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278555 seconds
Optimal solution found.
Minimum total cost = -33026262
Max flow value      : 51384
Min cost value      : -33026262
Total time          : 0.173688 seconds

========================================

Source: 57211, Sink: 34283
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275667 seconds
Optimal solution found.
Minimum total cost = -33190769
Max flow value      : 47106
Min cost value      : -33190769
Total time          : 0.157259 seconds

========================================

Source: 555, Sink: 35533
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275174 seconds
Optimal solution found.
Minimum total cost = -33368327
Max flow value      : 40032
Min cost value      : -33368327
Total time          : 0.165492 seconds

========================================

Source: 2452, Sink: 48808
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27857 seconds
Optimal solution found.
Minimum total cost = -33680492
Max flow value      : 11200
Min cost value      : -33680492
Total time          : 0.119443 seconds

========================================

Source: 35395, Sink: 57226
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276204 seconds
Optimal solution found.
Minimum total cost = -33308175
Max flow value      : 32469
Min cost value      : -33308175
Total time          : 0.168174 seconds

========================================

Source: 55359, Sink: 18431
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275033 seconds
Optimal solution found.
Minimum total cost = -33834812
Max flow value      : 8752
Min cost value      : -33834812
Total time          : 0.177506 seconds

========================================

Source: 5910, Sink: 52444
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275579 seconds
Optimal solution found.
Minimum total cost = -33753980
Max flow value      : 37099
Min cost value      : -33753980
Total time          : 0.121897 seconds

========================================

Source: 45269, Sink: 44448
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274488 seconds
Optimal solution found.
Minimum total cost = -33704373
Max flow value      : 34343
Min cost value      : -33704373
Total time          : 0.22703 seconds

========================================

Source: 63570, Sink: 17047
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276055 seconds
Optimal solution found.
Minimum total cost = -32625738
Max flow value      : 51107
Min cost value      : -32625738
Total time          : 0.225234 seconds

========================================

Source: 14809, Sink: 12205
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276488 seconds
Optimal solution found.
Minimum total cost = -32162708
Max flow value      : 44069
Min cost value      : -32162708
Total time          : 0.238747 seconds

========================================

Source: 58476, Sink: 21998
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275737 seconds
Optimal solution found.
Minimum total cost = -30084550
Max flow value      : 42440
Min cost value      : -30084550
Total time          : 0.285233 seconds

========================================

Source: 17876, Sink: 10903
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276041 seconds
Optimal solution found.
Minimum total cost = -33770362
Max flow value      : 39146
Min cost value      : -33770362
Total time          : 0.125098 seconds

========================================

Source: 21978, Sink: 19477
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.324107 seconds
Optimal solution found.
Minimum total cost = -33603528
Max flow value      : 16426
Min cost value      : -33603528
Total time          : 0.153578 seconds

========================================

Source: 5955, Sink: 4069
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.28185 seconds
Optimal solution found.
Minimum total cost = -33878758
Max flow value      : 3254
Min cost value      : -33878758
Total time          : 0.179153 seconds

========================================

Source: 40006, Sink: 30129
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280426 seconds
Optimal solution found.
Minimum total cost = -32388548
Max flow value      : 54664
Min cost value      : -32388548
Total time          : 0.16972 seconds

========================================

Source: 65572, Sink: 35161
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276658 seconds
Optimal solution found.
Minimum total cost = -34068296
Max flow value      : 17034
Min cost value      : -34068296
Total time          : 0.176257 seconds

========================================

Source: 39072, Sink: 28993
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277239 seconds
Optimal solution found.
Minimum total cost = -33424256
Max flow value      : 20068
Min cost value      : -33424256
Total time          : 0.141893 seconds

========================================

Source: 10803, Sink: 13976
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275906 seconds
Optimal solution found.
Minimum total cost = -33875160
Max flow value      : 13226
Min cost value      : -33875160
Total time          : 0.17155 seconds

========================================

Source: 38183, Sink: 17090
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274663 seconds
Optimal solution found.
Minimum total cost = -32947570
Max flow value      : 54922
Min cost value      : -32947570
Total time          : 0.146056 seconds

========================================

Source: 11189, Sink: 14923
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275571 seconds
Optimal solution found.
Minimum total cost = -33290330
Max flow value      : 18785
Min cost value      : -33290330
Total time          : 0.183436 seconds

========================================

Source: 10169, Sink: 42149
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275945 seconds
Optimal solution found.
Minimum total cost = -33343220
Max flow value      : 49325
Min cost value      : -33343220
Total time          : 0.220702 seconds

========================================

Source: 50082, Sink: 37925
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274664 seconds
Optimal solution found.
Minimum total cost = -33858956
Max flow value      : 5952
Min cost value      : -33858956
Total time          : 0.179958 seconds

========================================

Source: 63026, Sink: 32708
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275521 seconds
Optimal solution found.
Minimum total cost = -33236572
Max flow value      : 47378
Min cost value      : -33236572
Total time          : 0.161312 seconds

========================================

Source: 43868, Sink: 37533
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277589 seconds
Optimal solution found.
Minimum total cost = -33187204
Max flow value      : 16729
Min cost value      : -33187204
Total time          : 0.17898 seconds

========================================

Source: 18460, Sink: 16068
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.242112 seconds
Optimal solution found.
Minimum total cost = -34086104
Max flow value      : 20094
Min cost value      : -34086104
Total time          : 0.152894 seconds

========================================

Source: 13829, Sink: 26324
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276397 seconds
Optimal solution found.
Minimum total cost = -33875771
Max flow value      : 31226
Min cost value      : -33875771
Total time          : 0.151818 seconds

========================================

Source: 32809, Sink: 449
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276051 seconds
Optimal solution found.
Minimum total cost = -33551554
Max flow value      : 42142
Min cost value      : -33551554
Total time          : 0.129169 seconds

========================================

Source: 44757, Sink: 42342
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278195 seconds
Optimal solution found.
Minimum total cost = -33221080
Max flow value      : 38026
Min cost value      : -33221080
Total time          : 0.166818 seconds

========================================

Source: 9027, Sink: 6086
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275899 seconds
Optimal solution found.
Minimum total cost = -33506612
Max flow value      : 30632
Min cost value      : -33506612
Total time          : 0.173798 seconds

========================================

Source: 61974, Sink: 6731
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276269 seconds
Optimal solution found.
Minimum total cost = -33903942
Max flow value      : 18276
Min cost value      : -33903942
Total time          : 0.173129 seconds

========================================

Source: 15305, Sink: 33907
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278686 seconds
Optimal solution found.
Minimum total cost = -32887238
Max flow value      : 53922
Min cost value      : -32887238
Total time          : 0.164554 seconds

========================================

Source: 59320, Sink: 12969
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.281891 seconds
Optimal solution found.
Minimum total cost = -33489788
Max flow value      : 45136
Min cost value      : -33489788
Total time          : 0.162301 seconds

========================================

Source: 32892, Sink: 33794
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275396 seconds
Optimal solution found.
Minimum total cost = -33042778
Max flow value      : 46336
Min cost value      : -33042778
Total time          : 0.150491 seconds

========================================

Source: 9877, Sink: 61236
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276614 seconds
Optimal solution found.
Minimum total cost = -33600610
Max flow value      : 53586
Min cost value      : -33600610
Total time          : 0.169912 seconds

========================================

Source: 57903, Sink: 28846
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.2765 seconds
Optimal solution found.
Minimum total cost = -33787852
Max flow value      : 8912
Min cost value      : -33787852
Total time          : 0.117744 seconds

========================================

Source: 32932, Sink: 62204
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274718 seconds
Optimal solution found.
Minimum total cost = -33966092
Max flow value      : 10238
Min cost value      : -33966092
Total time          : 0.172706 seconds

========================================

Source: 55034, Sink: 22881
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.297122 seconds
Optimal solution found.
Minimum total cost = -33842513
Max flow value      : 27462
Min cost value      : -33842513
Total time          : 0.180245 seconds

========================================

Source: 11334, Sink: 40388
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274483 seconds
Optimal solution found.
Minimum total cost = -33968652
Max flow value      : 40
Min cost value      : -33968652
Total time          : 0.094194 seconds

========================================

Source: 1719, Sink: 51085
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277442 seconds
Optimal solution found.
Minimum total cost = -33437522
Max flow value      : 51264
Min cost value      : -33437522
Total time          : 0.16924 seconds

========================================

Source: 56071, Sink: 8779
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.288917 seconds
Optimal solution found.
Minimum total cost = -33956786
Max flow value      : 6204
Min cost value      : -33956786
Total time          : 0.176724 seconds

========================================

Source: 15743, Sink: 14178
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276187 seconds
Optimal solution found.
Minimum total cost = -33816030
Max flow value      : 21010
Min cost value      : -33816030
Total time          : 0.118262 seconds

========================================

Source: 4489, Sink: 38393
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275784 seconds
Optimal solution found.
Minimum total cost = -33945914
Max flow value      : 16756
Min cost value      : -33945914
Total time          : 0.164783 seconds

========================================

Source: 18586, Sink: 31193
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27628 seconds
Optimal solution found.
Minimum total cost = -31973787
Max flow value      : 55113
Min cost value      : -31973787
Total time          : 0.306158 seconds

========================================

Source: 24012, Sink: 62749
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275043 seconds
Optimal solution found.
Minimum total cost = -33966989
Max flow value      : 598
Min cost value      : -33966989
Total time          : 0.10569 seconds

========================================

Source: 37802, Sink: 12626
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279402 seconds
Optimal solution found.
Minimum total cost = -32522153
Max flow value      : 56798
Min cost value      : -32522153
Total time          : 0.167283 seconds

========================================

Source: 2101, Sink: 57522
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275373 seconds
Optimal solution found.
Minimum total cost = -33896374
Max flow value      : 30844
Min cost value      : -33896374
Total time          : 0.172935 seconds

========================================

Source: 25542, Sink: 22557
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27677 seconds
Optimal solution found.
Minimum total cost = -32058518
Max flow value      : 60368
Min cost value      : -32058518
Total time          : 0.164883 seconds

========================================

Source: 14539, Sink: 26595
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274665 seconds
Optimal solution found.
Minimum total cost = -33744005
Max flow value      : 24540
Min cost value      : -33744005
Total time          : 0.177508 seconds

========================================

Source: 4345, Sink: 18037
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.2742 seconds
Optimal solution found.
Minimum total cost = -33171038
Max flow value      : 51556
Min cost value      : -33171038
Total time          : 0.161811 seconds

========================================

Source: 59297, Sink: 9776
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274948 seconds
Optimal solution found.
Minimum total cost = -33430907
Max flow value      : 38142
Min cost value      : -33430907
Total time          : 0.142456 seconds

========================================

Source: 63770, Sink: 28622
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275385 seconds
Optimal solution found.
Minimum total cost = -34006886
Max flow value      : 13598
Min cost value      : -34006886
Total time          : 0.120932 seconds

========================================

Source: 36093, Sink: 48906
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27512 seconds
Optimal solution found.
Minimum total cost = -33263909
Max flow value      : 58400
Min cost value      : -33263909
Total time          : 0.160232 seconds

========================================

Source: 15623, Sink: 60467
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276484 seconds
Optimal solution found.
Minimum total cost = -32073100
Max flow value      : 54237
Min cost value      : -32073100
Total time          : 0.158873 seconds

========================================

Source: 52881, Sink: 41409
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27982 seconds
Optimal solution found.
Minimum total cost = -33996872
Max flow value      : 2052
Min cost value      : -33996872
Total time          : 0.105096 seconds

========================================

Source: 4232, Sink: 789
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274637 seconds
Optimal solution found.
Minimum total cost = -33844824
Max flow value      : 36738
Min cost value      : -33844824
Total time          : 0.159961 seconds

========================================

Source: 64850, Sink: 41956
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.294087 seconds
Optimal solution found.
Minimum total cost = -33975140
Max flow value      : 18096
Min cost value      : -33975140
Total time          : 0.119407 seconds

========================================

Source: 29001, Sink: 53571
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275603 seconds
Optimal solution found.
Minimum total cost = -33853824
Max flow value      : 14186
Min cost value      : -33853824
Total time          : 0.108326 seconds

========================================

Source: 22164, Sink: 9411
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275498 seconds
Optimal solution found.
Minimum total cost = -33869461
Max flow value      : 18719
Min cost value      : -33869461
Total time          : 0.129522 seconds

========================================

Source: 35779, Sink: 12151
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276508 seconds
Optimal solution found.
Minimum total cost = -33763761
Max flow value      : 9311
Min cost value      : -33763761
Total time          : 0.174322 seconds

========================================

Source: 57436, Sink: 11467
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278641 seconds
Optimal solution found.
Minimum total cost = -33998509
Max flow value      : 5894
Min cost value      : -33998509
Total time          : 0.202703 seconds

========================================

Source: 9848, Sink: 21083
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.248768 seconds
Optimal solution found.
Minimum total cost = -34006152
Max flow value      : 4006
Min cost value      : -34006152
Total time          : 0.150098 seconds

========================================

Source: 25905, Sink: 32270
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275662 seconds
Optimal solution found.
Minimum total cost = -33557064
Max flow value      : 27884
Min cost value      : -33557064
Total time          : 0.152536 seconds

========================================

Source: 10976, Sink: 52098
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276931 seconds
Optimal solution found.
Minimum total cost = -33126238
Max flow value      : 36970
Min cost value      : -33126238
Total time          : 0.160584 seconds

========================================

Source: 65050, Sink: 22397
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276055 seconds
Optimal solution found.
Minimum total cost = -33356313
Max flow value      : 53203
Min cost value      : -33356313
Total time          : 0.256803 seconds

========================================

Source: 40192, Sink: 47535
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.239048 seconds
Optimal solution found.
Minimum total cost = -33784592
Max flow value      : 19547
Min cost value      : -33784592
Total time          : 0.14702 seconds

========================================

Source: 733, Sink: 9298
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274084 seconds
Optimal solution found.
Minimum total cost = -32654410
Max flow value      : 63050
Min cost value      : -32654410
Total time          : 0.148518 seconds

========================================

Source: 13307, Sink: 1655
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.289423 seconds
Optimal solution found.
Minimum total cost = -33966092
Max flow value      : 0
Min cost value      : -33966092
Total time          : 0.181705 seconds

========================================

Source: 40045, Sink: 41359
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.234076 seconds
Optimal solution found.
Minimum total cost = -32284953
Max flow value      : 45355
Min cost value      : -32284953
Total time          : 0.139923 seconds

========================================

Source: 19546, Sink: 53143
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277726 seconds
Optimal solution found.
Minimum total cost = -32788280
Max flow value      : 53458
Min cost value      : -32788280
Total time          : 0.166876 seconds

========================================

Source: 33040, Sink: 51314
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274992 seconds
Optimal solution found.
Minimum total cost = -33592652
Max flow value      : 11670
Min cost value      : -33592652
Total time          : 0.114169 seconds

========================================

Source: 12126, Sink: 33380
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274689 seconds
Optimal solution found.
Minimum total cost = -32321113
Max flow value      : 32002
Min cost value      : -32321113
Total time          : 0.190182 seconds

========================================

Source: 28836, Sink: 27578
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276426 seconds
Optimal solution found.
Minimum total cost = -33912314
Max flow value      : 30968
Min cost value      : -33912314
Total time          : 0.224481 seconds

========================================

Source: 509, Sink: 19741
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275775 seconds
Optimal solution found.
Minimum total cost = -33087528
Max flow value      : 22058
Min cost value      : -33087528
Total time          : 0.150059 seconds

========================================

Source: 40055, Sink: 1410
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277105 seconds
Optimal solution found.
Minimum total cost = -33032071
Max flow value      : 36439
Min cost value      : -33032071
Total time          : 0.122644 seconds

========================================

Source: 5635, Sink: 31291
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.294792 seconds
Optimal solution found.
Minimum total cost = -33571531
Max flow value      : 27818
Min cost value      : -33571531
Total time          : 0.159943 seconds

========================================

Source: 43626, Sink: 44474
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275271 seconds
Optimal solution found.
Minimum total cost = -32385140
Max flow value      : 60966
Min cost value      : -32385140
Total time          : 0.16043 seconds

========================================

Source: 23272, Sink: 16068
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275866 seconds
Optimal solution found.
Minimum total cost = -33741644
Max flow value      : 23892
Min cost value      : -33741644
Total time          : 0.161002 seconds

========================================

Source: 50641, Sink: 56754
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276844 seconds
Optimal solution found.
Minimum total cost = -33712107
Max flow value      : 26422
Min cost value      : -33712107
Total time          : 0.150596 seconds

========================================

Source: 31794, Sink: 36093
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274372 seconds
Optimal solution found.
Minimum total cost = -33952379
Max flow value      : 3918
Min cost value      : -33952379
Total time          : 0.106495 seconds

========================================

Source: 35620, Sink: 53829
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27492 seconds
Optimal solution found.
Minimum total cost = -33219566
Max flow value      : 58832
Min cost value      : -33219566
Total time          : 0.175354 seconds

========================================

Source: 8988, Sink: 8723
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.284906 seconds
Optimal solution found.
Minimum total cost = -32702093
Max flow value      : 35482
Min cost value      : -32702093
Total time          : 0.226053 seconds

========================================

Source: 53415, Sink: 35611
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278617 seconds
Optimal solution found.
Minimum total cost = -32236134
Max flow value      : 38877
Min cost value      : -32236134
Total time          : 0.277479 seconds

========================================

Source: 29520, Sink: 44112
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.288921 seconds
Optimal solution found.
Minimum total cost = -32964986
Max flow value      : 37078
Min cost value      : -32964986
Total time          : 0.178391 seconds

========================================

Source: 49646, Sink: 17645
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279459 seconds
Optimal solution found.
Minimum total cost = -33946864
Max flow value      : 31436
Min cost value      : -33946864
Total time          : 0.180667 seconds

========================================

Source: 63935, Sink: 17793
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275311 seconds
Optimal solution found.
Minimum total cost = -33375433
Max flow value      : 39884
Min cost value      : -33375433
Total time          : 0.170891 seconds

========================================

Source: 25900, Sink: 64377
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276957 seconds
Optimal solution found.
Minimum total cost = -34082516
Max flow value      : 4158
Min cost value      : -34082516
Total time          : 0.107144 seconds

========================================

Source: 25982, Sink: 13616
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276159 seconds
Optimal solution found.
Minimum total cost = -33408593
Max flow value      : 60630
Min cost value      : -33408593
Total time          : 0.150686 seconds

========================================

Source: 55359, Sink: 58454
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274331 seconds
Optimal solution found.
Minimum total cost = -33608438
Max flow value      : 21676
Min cost value      : -33608438
Total time          : 0.178396 seconds

========================================

Source: 64259, Sink: 35930
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277879 seconds
Optimal solution found.
Minimum total cost = -33974120
Max flow value      : 2676
Min cost value      : -33974120
Total time          : 0.112659 seconds

========================================

Source: 58200, Sink: 49953
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276331 seconds
Optimal solution found.
Minimum total cost = -33595472
Max flow value      : 20590
Min cost value      : -33595472
Total time          : 0.171694 seconds

========================================

Source: 41588, Sink: 59186
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276864 seconds
Optimal solution found.
Minimum total cost = -32929924
Max flow value      : 57783
Min cost value      : -32929924
Total time          : 0.201421 seconds

========================================

Source: 15243, Sink: 10783
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279813 seconds
Optimal solution found.
Minimum total cost = -33675808
Max flow value      : 18728
Min cost value      : -33675808
Total time          : 0.122998 seconds

========================================

Source: 50296, Sink: 13254
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276934 seconds
Optimal solution found.
Minimum total cost = -33889236
Max flow value      : 2956
Min cost value      : -33889236
Total time          : 0.105028 seconds

========================================

Source: 29897, Sink: 24934
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.286399 seconds
Optimal solution found.
Minimum total cost = -33372024
Max flow value      : 39004
Min cost value      : -33372024
Total time          : 0.167119 seconds

========================================

Source: 19432, Sink: 8678
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27455 seconds
Optimal solution found.
Minimum total cost = -33015875
Max flow value      : 39794
Min cost value      : -33015875
Total time          : 0.150403 seconds

========================================

Source: 65773, Sink: 39778
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275743 seconds
Optimal solution found.
Minimum total cost = -33853945
Max flow value      : 6062
Min cost value      : -33853945
Total time          : 0.181696 seconds

========================================

Source: 60103, Sink: 31379
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277237 seconds
Optimal solution found.
Minimum total cost = -32613440
Max flow value      : 26948
Min cost value      : -32613440
Total time          : 0.153147 seconds

========================================

Source: 2430, Sink: 58699
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.28419 seconds
Optimal solution found.
Minimum total cost = -33688971
Max flow value      : 14679
Min cost value      : -33688971
Total time          : 0.238773 seconds

========================================

Source: 895, Sink: 8045
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.283615 seconds
Optimal solution found.
Minimum total cost = -34030220
Max flow value      : 2672
Min cost value      : -34030220
Total time          : 0.137831 seconds

========================================

Source: 37725, Sink: 63609
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278268 seconds
Optimal solution found.
Minimum total cost = -33574752
Max flow value      : 21830
Min cost value      : -33574752
Total time          : 0.158251 seconds

========================================

Source: 11482, Sink: 41598
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.281484 seconds
Optimal solution found.
Minimum total cost = -33344627
Max flow value      : 30045
Min cost value      : -33344627
Total time          : 0.135046 seconds

========================================

Source: 34395, Sink: 9331
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277884 seconds
Optimal solution found.
Minimum total cost = -34035612
Max flow value      : 17380
Min cost value      : -34035612
Total time          : 0.173147 seconds

========================================

Source: 13373, Sink: 20795
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276204 seconds
Optimal solution found.
Minimum total cost = -33630038
Max flow value      : 30180
Min cost value      : -33630038
Total time          : 0.162992 seconds

========================================

Source: 21430, Sink: 63246
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277367 seconds
Optimal solution found.
Minimum total cost = -33907586
Max flow value      : 5572
Min cost value      : -33907586
Total time          : 0.0996607 seconds

========================================

Source: 14245, Sink: 55663
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27955 seconds
Optimal solution found.
Minimum total cost = -33509374
Max flow value      : 22844
Min cost value      : -33509374
Total time          : 0.164969 seconds

========================================

Source: 31875, Sink: 7966
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.232466 seconds
Optimal solution found.
Minimum total cost = -33849005
Max flow value      : 10544
Min cost value      : -33849005
Total time          : 0.0955939 seconds

========================================

Source: 23938, Sink: 57426
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276495 seconds
Optimal solution found.
Minimum total cost = -34004878
Max flow value      : 3526
Min cost value      : -34004878
Total time          : 0.178133 seconds

========================================

Source: 56286, Sink: 40284
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27544 seconds
Optimal solution found.
Minimum total cost = -33784916
Max flow value      : 31128
Min cost value      : -33784916
Total time          : 0.166049 seconds

========================================

Source: 39581, Sink: 2402
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.232897 seconds
Optimal solution found.
Minimum total cost = -33760739
Max flow value      : 30486
Min cost value      : -33760739
Total time          : 0.136866 seconds

========================================

Source: 799, Sink: 17329
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277112 seconds
Optimal solution found.
Minimum total cost = -34006742
Max flow value      : 2710
Min cost value      : -34006742
Total time          : 0.17579 seconds

========================================

Source: 52784, Sink: 11727
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277173 seconds
Optimal solution found.
Minimum total cost = -33577531
Max flow value      : 49187
Min cost value      : -33577531
Total time          : 0.177288 seconds

========================================

Source: 7441, Sink: 35987
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275172 seconds
Optimal solution found.
Minimum total cost = -32333872
Max flow value      : 71417
Min cost value      : -32333872
Total time          : 0.15932 seconds

========================================

Source: 13341, Sink: 26415
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276803 seconds
Optimal solution found.
Minimum total cost = -33935610
Max flow value      : 11074
Min cost value      : -33935610
Total time          : 0.184783 seconds

========================================

Source: 8504, Sink: 21191
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.230508 seconds
Optimal solution found.
Minimum total cost = -32858300
Max flow value      : 61078
Min cost value      : -32858300
Total time          : 0.139521 seconds

========================================

Source: 54223, Sink: 49405
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277139 seconds
Optimal solution found.
Minimum total cost = -34012952
Max flow value      : 7906
Min cost value      : -34012952
Total time          : 0.163273 seconds

========================================

Source: 62116, Sink: 11259
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280336 seconds
Optimal solution found.
Minimum total cost = -33963938
Max flow value      : 18642
Min cost value      : -33963938
Total time          : 0.168454 seconds

========================================

Source: 64018, Sink: 16472
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275842 seconds
Optimal solution found.
Minimum total cost = -33804044
Max flow value      : 20256
Min cost value      : -33804044
Total time          : 0.115565 seconds

========================================

Source: 5635, Sink: 5727
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27424 seconds
Optimal solution found.
Minimum total cost = -32487078
Max flow value      : 40024
Min cost value      : -32487078
Total time          : 0.128936 seconds

========================================

Source: 17381, Sink: 47314
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275596 seconds
Optimal solution found.
Minimum total cost = -32942356
Max flow value      : 53892
Min cost value      : -32942356
Total time          : 0.168421 seconds

========================================

Source: 10021, Sink: 6217
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276978 seconds
Optimal solution found.
Minimum total cost = -33638270
Max flow value      : 29802
Min cost value      : -33638270
Total time          : 0.177409 seconds

========================================

Source: 5077, Sink: 530
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280017 seconds
Optimal solution found.
Minimum total cost = -33376873
Max flow value      : 56427
Min cost value      : -33376873
Total time          : 0.240066 seconds

========================================

Source: 52005, Sink: 51655
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276266 seconds
Optimal solution found.
Minimum total cost = -33027013
Max flow value      : 29794
Min cost value      : -33027013
Total time          : 0.170595 seconds

========================================

Source: 12200, Sink: 41961
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27567 seconds
Optimal solution found.
Minimum total cost = -33954517
Max flow value      : 17161
Min cost value      : -33954517
Total time          : 0.106645 seconds

========================================

Source: 27654, Sink: 1473
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.281325 seconds
Optimal solution found.
Minimum total cost = -33630796
Max flow value      : 41912
Min cost value      : -33630796
Total time          : 0.120555 seconds

========================================

Source: 40525, Sink: 45550
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.229025 seconds
Optimal solution found.
Minimum total cost = -33990062
Max flow value      : 1410
Min cost value      : -33990062
Total time          : 0.137573 seconds

========================================

Source: 5453, Sink: 63128
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274708 seconds
Optimal solution found.
Minimum total cost = -33843842
Max flow value      : 40750
Min cost value      : -33843842
Total time          : 0.177802 seconds

========================================

Source: 62033, Sink: 15546
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276305 seconds
Optimal solution found.
Minimum total cost = -32781169
Max flow value      : 51848
Min cost value      : -32781169
Total time          : 0.152132 seconds

========================================

Source: 22180, Sink: 37154
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276511 seconds
Optimal solution found.
Minimum total cost = -31686302
Max flow value      : 71547
Min cost value      : -31686302
Total time          : 0.162542 seconds

========================================

Source: 30048, Sink: 48413
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275233 seconds
Optimal solution found.
Minimum total cost = -33828952
Max flow value      : 13714
Min cost value      : -33828952
Total time          : 0.118235 seconds

========================================

Source: 51738, Sink: 14235
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.281388 seconds
Optimal solution found.
Minimum total cost = -32274788
Max flow value      : 44508
Min cost value      : -32274788
Total time          : 0.143156 seconds

========================================

Source: 60666, Sink: 40833
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.232439 seconds
Optimal solution found.
Minimum total cost = -33437563
Max flow value      : 37046
Min cost value      : -33437563
Total time          : 0.131655 seconds

========================================

Source: 64228, Sink: 18135
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275892 seconds
Optimal solution found.
Minimum total cost = -32923845
Max flow value      : 30088
Min cost value      : -32923845
Total time          : 0.239535 seconds

========================================

Source: 54172, Sink: 31787
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.232085 seconds
Optimal solution found.
Minimum total cost = -33679002
Max flow value      : 33236
Min cost value      : -33679002
Total time          : 0.129987 seconds

========================================

Source: 60162, Sink: 1860
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.2764 seconds
Optimal solution found.
Minimum total cost = -34037540
Max flow value      : 5496
Min cost value      : -34037540
Total time          : 0.1624 seconds

========================================

Source: 5876, Sink: 54209
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276001 seconds
Optimal solution found.
Minimum total cost = -33825242
Max flow value      : 7361
Min cost value      : -33825242
Total time          : 0.116778 seconds

========================================

Source: 30031, Sink: 1938
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279562 seconds
Optimal solution found.
Minimum total cost = -34005992
Max flow value      : 7400
Min cost value      : -34005992
Total time          : 0.110244 seconds

========================================

Source: 15125, Sink: 22276
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275644 seconds
Optimal solution found.
Minimum total cost = -33496658
Max flow value      : 44708
Min cost value      : -33496658
Total time          : 0.127341 seconds

========================================

Source: 56943, Sink: 6508
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277297 seconds
Optimal solution found.
Minimum total cost = -33949795
Max flow value      : 32852
Min cost value      : -33949795
Total time          : 0.127994 seconds

========================================

Source: 44004, Sink: 39140
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276092 seconds
Optimal solution found.
Minimum total cost = -33611649
Max flow value      : 59646
Min cost value      : -33611649
Total time          : 0.181698 seconds

========================================

Source: 9789, Sink: 54810
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277523 seconds
Optimal solution found.
Minimum total cost = -33984524
Max flow value      : 3072
Min cost value      : -33984524
Total time          : 0.162789 seconds

========================================

Source: 5518, Sink: 39450
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275591 seconds
Optimal solution found.
Minimum total cost = -33638947
Max flow value      : 45429
Min cost value      : -33638947
Total time          : 0.225417 seconds

========================================

Source: 14840, Sink: 17373
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27494 seconds
Optimal solution found.
Minimum total cost = -33421731
Max flow value      : 28466
Min cost value      : -33421731
Total time          : 0.143526 seconds

========================================

Source: 57403, Sink: 33865
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279178 seconds
Optimal solution found.
Minimum total cost = -33631604
Max flow value      : 11946
Min cost value      : -33631604
Total time          : 0.114504 seconds

========================================

Source: 27241, Sink: 3489
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.232588 seconds
Optimal solution found.
Minimum total cost = -33940594
Max flow value      : 4636
Min cost value      : -33940594
Total time          : 0.138686 seconds

========================================

Source: 58253, Sink: 55179
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277476 seconds
Optimal solution found.
Minimum total cost = -31929981
Max flow value      : 50566
Min cost value      : -31929981
Total time          : 0.158448 seconds

========================================

Source: 43346, Sink: 9756
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27547 seconds
Optimal solution found.
Minimum total cost = -33957136
Max flow value      : 17912
Min cost value      : -33957136
Total time          : 0.120114 seconds

========================================

Source: 44464, Sink: 39033
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27513 seconds
Optimal solution found.
Minimum total cost = -32745293
Max flow value      : 51816
Min cost value      : -32745293
Total time          : 0.156636 seconds

========================================

Source: 31600, Sink: 29730
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275503 seconds
Optimal solution found.
Minimum total cost = -34023874
Max flow value      : 21220
Min cost value      : -34023874
Total time          : 0.115076 seconds

========================================

Source: 5460, Sink: 50983
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277111 seconds
Optimal solution found.
Minimum total cost = -33920798
Max flow value      : 13454
Min cost value      : -33920798
Total time          : 0.165073 seconds

========================================

Source: 49617, Sink: 26952
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.2779 seconds
Optimal solution found.
Minimum total cost = -33705283
Max flow value      : 34169
Min cost value      : -33705283
Total time          : 0.151024 seconds

========================================

Source: 28694, Sink: 49526
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275088 seconds
Optimal solution found.
Minimum total cost = -33679036
Max flow value      : 41008
Min cost value      : -33679036
Total time          : 0.143497 seconds

========================================

Source: 14250, Sink: 6904
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279273 seconds
Optimal solution found.
Minimum total cost = -30529247
Max flow value      : 69729
Min cost value      : -30529247
Total time          : 0.215544 seconds

========================================

Source: 17975, Sink: 55002
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275592 seconds
Optimal solution found.
Minimum total cost = -34080512
Max flow value      : 11442
Min cost value      : -34080512
Total time          : 0.109024 seconds

========================================

Source: 58421, Sink: 22824
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27627 seconds
Optimal solution found.
Minimum total cost = -33690492
Max flow value      : 13780
Min cost value      : -33690492
Total time          : 0.119021 seconds

========================================

Source: 16900, Sink: 26893
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.232333 seconds
Optimal solution found.
Minimum total cost = -32772961
Max flow value      : 66244
Min cost value      : -32772961
Total time          : 0.12635 seconds

========================================

Source: 31857, Sink: 57881
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275952 seconds
Optimal solution found.
Minimum total cost = -33321273
Max flow value      : 47578
Min cost value      : -33321273
Total time          : 0.190903 seconds

========================================

Source: 43858, Sink: 54375
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.273879 seconds
Optimal solution found.
Minimum total cost = -33350436
Max flow value      : 29104
Min cost value      : -33350436
Total time          : 0.154963 seconds

========================================

Source: 58523, Sink: 38506
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27671 seconds
Optimal solution found.
Minimum total cost = -33441763
Max flow value      : 32366
Min cost value      : -33441763
Total time          : 0.146806 seconds

========================================

Source: 12758, Sink: 5555
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279759 seconds
Optimal solution found.
Minimum total cost = -33849404
Max flow value      : 4862
Min cost value      : -33849404
Total time          : 0.17601 seconds

========================================

Source: 59956, Sink: 8183
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276927 seconds
Optimal solution found.
Minimum total cost = -31818665
Max flow value      : 38076
Min cost value      : -31818665
Total time          : 0.18868 seconds

========================================

Source: 56357, Sink: 21744
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278654 seconds
Optimal solution found.
Minimum total cost = -32510807
Max flow value      : 58115
Min cost value      : -32510807
Total time          : 0.146403 seconds

========================================

Source: 24415, Sink: 41047
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.2773 seconds
Optimal solution found.
Minimum total cost = -32561021
Max flow value      : 39741
Min cost value      : -32561021
Total time          : 0.147835 seconds

========================================

Source: 56397, Sink: 58592
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280344 seconds
Optimal solution found.
Minimum total cost = -32314594
Max flow value      : 66166
Min cost value      : -32314594
Total time          : 0.164436 seconds

========================================

Source: 45908, Sink: 59899
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280098 seconds
Optimal solution found.
Minimum total cost = -33910612
Max flow value      : 27740
Min cost value      : -33910612
Total time          : 0.175957 seconds

========================================

Source: 10538, Sink: 60795
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275684 seconds
Optimal solution found.
Minimum total cost = -33042964
Max flow value      : 48686
Min cost value      : -33042964
Total time          : 0.169845 seconds

========================================

Source: 19339, Sink: 21625
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.2863 seconds
Optimal solution found.
Minimum total cost = -33034887
Max flow value      : 52923
Min cost value      : -33034887
Total time          : 0.1583 seconds

========================================

Source: 24705, Sink: 53146
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275533 seconds
Optimal solution found.
Minimum total cost = -30698319
Max flow value      : 57710
Min cost value      : -30698319
Total time          : 0.163207 seconds

========================================

Source: 25403, Sink: 19305
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275528 seconds
Optimal solution found.
Minimum total cost = -33945500
Max flow value      : 1872
Min cost value      : -33945500
Total time          : 0.174062 seconds

========================================

Source: 52906, Sink: 53624
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.2753 seconds
Optimal solution found.
Minimum total cost = -33265709
Max flow value      : 36327
Min cost value      : -33265709
Total time          : 0.151632 seconds

========================================

Source: 25908, Sink: 31272
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279379 seconds
Optimal solution found.
Minimum total cost = -33813080
Max flow value      : 27884
Min cost value      : -33813080
Total time          : 0.221938 seconds

========================================

Source: 25183, Sink: 46275
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275864 seconds
Optimal solution found.
Minimum total cost = -33971552
Max flow value      : 140
Min cost value      : -33971552
Total time          : 0.102802 seconds

========================================

Source: 62633, Sink: 41342
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276189 seconds
Optimal solution found.
Minimum total cost = -33788954
Max flow value      : 4542
Min cost value      : -33788954
Total time          : 0.112366 seconds

========================================

Source: 5047, Sink: 4898
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276266 seconds
Optimal solution found.
Minimum total cost = -33066120
Max flow value      : 38730
Min cost value      : -33066120
Total time          : 0.161551 seconds

========================================

Source: 48366, Sink: 238
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276934 seconds
Optimal solution found.
Minimum total cost = -33930518
Max flow value      : 3234
Min cost value      : -33930518
Total time          : 0.100196 seconds

========================================

Source: 36483, Sink: 22326
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275982 seconds
Optimal solution found.
Minimum total cost = -32873173
Max flow value      : 44152
Min cost value      : -32873173
Total time          : 0.151077 seconds

========================================

Source: 7346, Sink: 52893
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.281516 seconds
Optimal solution found.
Minimum total cost = -31696848
Max flow value      : 40996
Min cost value      : -31696848
Total time          : 0.179793 seconds

========================================

Source: 60648, Sink: 47304
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275137 seconds
Optimal solution found.
Minimum total cost = -33021629
Max flow value      : 55734
Min cost value      : -33021629
Total time          : 0.162426 seconds

========================================

Source: 30354, Sink: 19786
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276902 seconds
Optimal solution found.
Minimum total cost = -33813632
Max flow value      : 14520
Min cost value      : -33813632
Total time          : 0.10925 seconds

========================================

Source: 4567, Sink: 8620
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279231 seconds
Optimal solution found.
Minimum total cost = -32062140
Max flow value      : 45869
Min cost value      : -32062140
Total time          : 0.169845 seconds

========================================

Source: 19176, Sink: 48479
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.281645 seconds
Optimal solution found.
Minimum total cost = -33483266
Max flow value      : 34674
Min cost value      : -33483266
Total time          : 0.163832 seconds

========================================

Source: 11861, Sink: 366
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274256 seconds
Optimal solution found.
Minimum total cost = -32956612
Max flow value      : 53512
Min cost value      : -32956612
Total time          : 0.239113 seconds

========================================

Source: 4037, Sink: 46786
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277485 seconds
Optimal solution found.
Minimum total cost = -33446048
Max flow value      : 31440
Min cost value      : -33446048
Total time          : 0.16543 seconds

========================================

Source: 41900, Sink: 15733
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27655 seconds
Optimal solution found.
Minimum total cost = -33818315
Max flow value      : 24555
Min cost value      : -33818315
Total time          : 0.157764 seconds

========================================

Source: 48158, Sink: 48380
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27681 seconds
Optimal solution found.
Minimum total cost = -33680622
Max flow value      : 22218
Min cost value      : -33680622
Total time          : 0.265217 seconds

========================================

Source: 1038, Sink: 48945
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276098 seconds
Optimal solution found.
Minimum total cost = -33786682
Max flow value      : 35882
Min cost value      : -33786682
Total time          : 0.233334 seconds

========================================

Source: 38224, Sink: 35752
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276663 seconds
Optimal solution found.
Minimum total cost = -32644057
Max flow value      : 49765
Min cost value      : -32644057
Total time          : 0.151962 seconds

========================================

Source: 494, Sink: 23930
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278253 seconds
Optimal solution found.
Minimum total cost = -33802919
Max flow value      : 14845
Min cost value      : -33802919
Total time          : 0.151422 seconds

========================================

Source: 23891, Sink: 23950
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275314 seconds
Optimal solution found.
Minimum total cost = -33366512
Max flow value      : 37224
Min cost value      : -33366512
Total time          : 0.168647 seconds

========================================

Source: 30019, Sink: 45699
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277415 seconds
Optimal solution found.
Minimum total cost = -33592543
Max flow value      : 25762
Min cost value      : -33592543
Total time          : 0.134589 seconds

========================================

Source: 15305, Sink: 9105
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279109 seconds
Optimal solution found.
Minimum total cost = -33464072
Max flow value      : 51671
Min cost value      : -33464072
Total time          : 0.206987 seconds

========================================

Source: 6, Sink: 22028
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279237 seconds
Optimal solution found.
Minimum total cost = -32461358
Max flow value      : 34944
Min cost value      : -32461358
Total time          : 0.145264 seconds

========================================

Source: 38546, Sink: 33434
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278063 seconds
Optimal solution found.
Minimum total cost = -33528292
Max flow value      : 17512
Min cost value      : -33528292
Total time          : 0.1645 seconds

========================================

Source: 171, Sink: 31605
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276635 seconds
Optimal solution found.
Minimum total cost = -33813850
Max flow value      : 10036
Min cost value      : -33813850
Total time          : 0.165014 seconds

========================================

Source: 3183, Sink: 52015
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274645 seconds
Optimal solution found.
Minimum total cost = -33705713
Max flow value      : 24798
Min cost value      : -33705713
Total time          : 0.18008 seconds

========================================

Source: 14499, Sink: 4753
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276187 seconds
Optimal solution found.
Minimum total cost = -34021344
Max flow value      : 2908
Min cost value      : -34021344
Total time          : 0.169174 seconds

========================================

Source: 24100, Sink: 5510
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277575 seconds
Optimal solution found.
Minimum total cost = -33441761
Max flow value      : 34504
Min cost value      : -33441761
Total time          : 0.144174 seconds

========================================

Source: 57206, Sink: 13837
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275634 seconds
Optimal solution found.
Minimum total cost = -32435647
Max flow value      : 62402
Min cost value      : -32435647
Total time          : 0.162539 seconds

========================================

Source: 33301, Sink: 11788
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278798 seconds
Optimal solution found.
Minimum total cost = -30793094
Max flow value      : 76903
Min cost value      : -30793094
Total time          : 0.175834 seconds

========================================

Source: 10439, Sink: 11174
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27463 seconds
Optimal solution found.
Minimum total cost = -32700554
Max flow value      : 38948
Min cost value      : -32700554
Total time          : 0.167633 seconds

========================================

Source: 11115, Sink: 13558
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.231946 seconds
Optimal solution found.
Minimum total cost = -33997751
Max flow value      : 21106
Min cost value      : -33997751
Total time          : 0.144721 seconds

========================================

Source: 64678, Sink: 32848
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280173 seconds
Optimal solution found.
Minimum total cost = -33113245
Max flow value      : 38798
Min cost value      : -33113245
Total time          : 0.161728 seconds

========================================

Source: 6319, Sink: 6131
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27596 seconds
Optimal solution found.
Minimum total cost = -33948100
Max flow value      : 21378
Min cost value      : -33948100
Total time          : 0.15921 seconds

========================================

Source: 43046, Sink: 60848
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.285966 seconds
Optimal solution found.
Minimum total cost = -33690153
Max flow value      : 14102
Min cost value      : -33690153
Total time          : 0.170997 seconds

========================================

Source: 5438, Sink: 32700
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.297987 seconds
Optimal solution found.
Minimum total cost = -33808940
Max flow value      : 26192
Min cost value      : -33808940
Total time          : 0.174773 seconds

========================================

Source: 37525, Sink: 37667
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278596 seconds
Optimal solution found.
Minimum total cost = -31683213
Max flow value      : 64128
Min cost value      : -31683213
Total time          : 0.170981 seconds

========================================

Source: 50184, Sink: 33316
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.295029 seconds
Optimal solution found.
Minimum total cost = -32615189
Max flow value      : 54519
Min cost value      : -32615189
Total time          : 0.156151 seconds

========================================

Source: 12964, Sink: 58442
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274898 seconds
Optimal solution found.
Minimum total cost = -33414086
Max flow value      : 21996
Min cost value      : -33414086
Total time          : 0.157529 seconds

========================================

Source: 14002, Sink: 39910
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277835 seconds
Optimal solution found.
Minimum total cost = -33286493
Max flow value      : 61624
Min cost value      : -33286493
Total time          : 0.178229 seconds

========================================

Source: 4816, Sink: 63304
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279852 seconds
Optimal solution found.
Minimum total cost = -31645519
Max flow value      : 77975
Min cost value      : -31645519
Total time          : 0.183902 seconds

========================================

Source: 50870, Sink: 39135
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277853 seconds
Optimal solution found.
Minimum total cost = -33648434
Max flow value      : 18932
Min cost value      : -33648434
Total time          : 0.130256 seconds

========================================

Source: 39586, Sink: 45560
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276454 seconds
Optimal solution found.
Minimum total cost = -33953412
Max flow value      : 25360
Min cost value      : -33953412
Total time          : 0.181163 seconds

========================================

Source: 26931, Sink: 31118
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275033 seconds
Optimal solution found.
Minimum total cost = -34045938
Max flow value      : 6142
Min cost value      : -34045938
Total time          : 0.107948 seconds

========================================

Source: 32605, Sink: 30850
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278827 seconds
Optimal solution found.
Minimum total cost = -33582316
Max flow value      : 13194
Min cost value      : -33582316
Total time          : 0.116312 seconds

========================================

Source: 37261, Sink: 6422
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277272 seconds
Optimal solution found.
Minimum total cost = -32778650
Max flow value      : 28962
Min cost value      : -32778650
Total time          : 0.123745 seconds

========================================

Source: 46187, Sink: 14343
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277129 seconds
Optimal solution found.
Minimum total cost = -32905925
Max flow value      : 25025
Min cost value      : -32905925
Total time          : 0.208905 seconds

========================================

Source: 46102, Sink: 12121
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275879 seconds
Optimal solution found.
Minimum total cost = -33861831
Max flow value      : 12266
Min cost value      : -33861831
Total time          : 0.178006 seconds

========================================

Source: 15901, Sink: 56410
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280636 seconds
Optimal solution found.
Minimum total cost = -32921950
Max flow value      : 70004
Min cost value      : -32921950
Total time          : 0.156951 seconds

========================================

Source: 26812, Sink: 38046
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278932 seconds
Optimal solution found.
Minimum total cost = -33908552
Max flow value      : 4110
Min cost value      : -33908552
Total time          : 0.17585 seconds

========================================

Source: 65075, Sink: 6379
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.234637 seconds
Optimal solution found.
Minimum total cost = -32860967
Max flow value      : 67544
Min cost value      : -32860967
Total time          : 0.12365 seconds

========================================

Source: 63351, Sink: 59500
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277811 seconds
Optimal solution found.
Minimum total cost = -34077226
Max flow value      : 16944
Min cost value      : -34077226
Total time          : 0.126078 seconds

========================================

Source: 1629, Sink: 49083
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.28214 seconds
Optimal solution found.
Minimum total cost = -33977878
Max flow value      : 18724
Min cost value      : -33977878
Total time          : 0.161627 seconds

========================================

Source: 14310, Sink: 17561
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275351 seconds
Optimal solution found.
Minimum total cost = -31980882
Max flow value      : 56455
Min cost value      : -31980882
Total time          : 0.157266 seconds

========================================

Source: 45610, Sink: 35254
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.2788 seconds
Optimal solution found.
Minimum total cost = -33951460
Max flow value      : 7316
Min cost value      : -33951460
Total time          : 0.1165 seconds

========================================

Source: 64508, Sink: 43532
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.285977 seconds
Optimal solution found.
Minimum total cost = -33493866
Max flow value      : 12670
Min cost value      : -33493866
Total time          : 0.293532 seconds

========================================

Source: 39562, Sink: 28244
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277323 seconds
Optimal solution found.
Minimum total cost = -33868844
Max flow value      : 4052
Min cost value      : -33868844
Total time          : 0.16726 seconds

========================================

Source: 24502, Sink: 7706
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.231941 seconds
Optimal solution found.
Minimum total cost = -33614671
Max flow value      : 18217
Min cost value      : -33614671
Total time          : 0.103808 seconds

========================================

Source: 9862, Sink: 5947
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276347 seconds
Optimal solution found.
Minimum total cost = -33917949
Max flow value      : 3106
Min cost value      : -33917949
Total time          : 0.123769 seconds

========================================

Source: 7421, Sink: 61795
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277244 seconds
Optimal solution found.
Minimum total cost = -33420866
Max flow value      : 49174
Min cost value      : -33420866
Total time          : 0.246132 seconds

========================================

Source: 6568, Sink: 31812
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275546 seconds
Optimal solution found.
Minimum total cost = -33245413
Max flow value      : 53839
Min cost value      : -33245413
Total time          : 0.183561 seconds

========================================

Source: 59531, Sink: 6344
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.282552 seconds
Optimal solution found.
Minimum total cost = -33872117
Max flow value      : 13927
Min cost value      : -33872117
Total time          : 0.109831 seconds

========================================

Source: 5881, Sink: 59607
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277765 seconds
Optimal solution found.
Minimum total cost = -34024168
Max flow value      : 19926
Min cost value      : -34024168
Total time          : 0.12803 seconds

========================================

Source: 25393, Sink: 42415
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276021 seconds
Optimal solution found.
Minimum total cost = -33768758
Max flow value      : 9597
Min cost value      : -33768758
Total time          : 0.163319 seconds

========================================

Source: 19907, Sink: 1048
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276706 seconds
Optimal solution found.
Minimum total cost = -33339509
Max flow value      : 37719
Min cost value      : -33339509
Total time          : 0.151717 seconds

========================================

Source: 61443, Sink: 46456
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275422 seconds
Optimal solution found.
Minimum total cost = -33789826
Max flow value      : 5686
Min cost value      : -33789826
Total time          : 0.118932 seconds

========================================

Source: 10258, Sink: 11663
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275342 seconds
Optimal solution found.
Minimum total cost = -33075057
Max flow value      : 39830
Min cost value      : -33075057
Total time          : 0.166991 seconds

========================================

Source: 35774, Sink: 849
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275151 seconds
Optimal solution found.
Minimum total cost = -33861684
Max flow value      : 24842
Min cost value      : -33861684
Total time          : 0.168529 seconds

========================================

Source: 29050, Sink: 16560
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277269 seconds
Optimal solution found.
Minimum total cost = -32018653
Max flow value      : 63820
Min cost value      : -32018653
Total time          : 0.195821 seconds

========================================

Source: 58877, Sink: 50025
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275496 seconds
Optimal solution found.
Minimum total cost = -33907052
Max flow value      : 7872
Min cost value      : -33907052
Total time          : 0.17515 seconds

========================================

Source: 38946, Sink: 13897
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278205 seconds
Optimal solution found.
Minimum total cost = -33290996
Max flow value      : 30720
Min cost value      : -33290996
Total time          : 0.125541 seconds

========================================

Source: 6716, Sink: 21850
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275009 seconds
Optimal solution found.
Minimum total cost = -33725623
Max flow value      : 21863
Min cost value      : -33725623
Total time          : 0.285341 seconds

========================================

Source: 56177, Sink: 35149
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276433 seconds
Optimal solution found.
Minimum total cost = -33824552
Max flow value      : 13480
Min cost value      : -33824552
Total time          : 0.177465 seconds

========================================

Source: 22996, Sink: 46816
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275663 seconds
Optimal solution found.
Minimum total cost = -32575042
Max flow value      : 22218
Min cost value      : -32575042
Total time          : 0.286632 seconds

========================================

Source: 55034, Sink: 59777
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276873 seconds
Optimal solution found.
Minimum total cost = -33905422
Max flow value      : 24268
Min cost value      : -33905422
Total time          : 0.183641 seconds

========================================

Source: 43437, Sink: 21923
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275126 seconds
Optimal solution found.
Minimum total cost = -33532402
Max flow value      : 38732
Min cost value      : -33532402
Total time          : 0.168273 seconds

========================================

Source: 43336, Sink: 21420
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279491 seconds
Optimal solution found.
Minimum total cost = -34115160
Max flow value      : 10822
Min cost value      : -34115160
Total time          : 0.153372 seconds

========================================

Source: 65850, Sink: 43181
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276846 seconds
Optimal solution found.
Minimum total cost = -33705167
Max flow value      : 24850
Min cost value      : -33705167
Total time          : 0.169927 seconds

========================================

Source: 13534, Sink: 7371
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279028 seconds
Optimal solution found.
Minimum total cost = -33782762
Max flow value      : 17460
Min cost value      : -33782762
Total time          : 0.11307 seconds

========================================

Source: 34916, Sink: 32368
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275812 seconds
Optimal solution found.
Minimum total cost = -33240224
Max flow value      : 37575
Min cost value      : -33240224
Total time          : 0.237961 seconds

========================================

Source: 25494, Sink: 25942
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279065 seconds
Optimal solution found.
Minimum total cost = -33080184
Max flow value      : 31038
Min cost value      : -33080184
Total time          : 0.133344 seconds

========================================

Source: 57562, Sink: 20770
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276714 seconds
Optimal solution found.
Minimum total cost = -33534788
Max flow value      : 47884
Min cost value      : -33534788
Total time          : 0.160369 seconds

========================================

Source: 37853, Sink: 3452
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278896 seconds
Optimal solution found.
Minimum total cost = -32823481
Max flow value      : 44044
Min cost value      : -32823481
Total time          : 0.143631 seconds

========================================

Source: 2864, Sink: 17526
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.28028 seconds
Optimal solution found.
Minimum total cost = -33845527
Max flow value      : 23652
Min cost value      : -33845527
Total time          : 0.153034 seconds

========================================

Source: 54291, Sink: 23552
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276152 seconds
Optimal solution found.
Minimum total cost = -32415587
Max flow value      : 62934
Min cost value      : -32415587
Total time          : 0.15501 seconds

========================================

Source: 24152, Sink: 183
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277572 seconds
Optimal solution found.
Minimum total cost = -32141746
Max flow value      : 59379
Min cost value      : -32141746
Total time          : 0.177046 seconds

========================================

Source: 42390, Sink: 7879
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276548 seconds
Optimal solution found.
Minimum total cost = -32125253
Max flow value      : 46088
Min cost value      : -32125253
Total time          : 0.144619 seconds

========================================

Source: 55825, Sink: 12799
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.28404 seconds
Optimal solution found.
Minimum total cost = -32255176
Max flow value      : 75319
Min cost value      : -32255176
Total time          : 0.152568 seconds

========================================

Source: 39101, Sink: 50092
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275265 seconds
Optimal solution found.
Minimum total cost = -33939062
Max flow value      : 10812
Min cost value      : -33939062
Total time          : 0.118161 seconds

========================================

Source: 8501, Sink: 60680
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274954 seconds
Optimal solution found.
Minimum total cost = -32173888
Max flow value      : 62288
Min cost value      : -32173888
Total time          : 0.222544 seconds

========================================

Source: 34666, Sink: 38591
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275581 seconds
Optimal solution found.
Minimum total cost = -33863414
Max flow value      : 11244
Min cost value      : -33863414
Total time          : 0.170568 seconds

========================================

Source: 24729, Sink: 5905
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274002 seconds
Optimal solution found.
Minimum total cost = -33444728
Max flow value      : 22668
Min cost value      : -33444728
Total time          : 0.115246 seconds

========================================

Source: 52903, Sink: 2829
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279204 seconds
Optimal solution found.
Minimum total cost = -33556320
Max flow value      : 37252
Min cost value      : -33556320
Total time          : 0.125959 seconds

========================================

Source: 47166, Sink: 13976
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274546 seconds
Optimal solution found.
Minimum total cost = -33919801
Max flow value      : 13226
Min cost value      : -33919801
Total time          : 0.16793 seconds

========================================

Source: 45057, Sink: 44196
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277146 seconds
Optimal solution found.
Minimum total cost = -33547718
Max flow value      : 38034
Min cost value      : -33547718
Total time          : 0.138014 seconds

========================================

Source: 51606, Sink: 55825
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278844 seconds
Optimal solution found.
Minimum total cost = -33663116
Max flow value      : 53233
Min cost value      : -33663116
Total time          : 0.194434 seconds

========================================

Source: 11668, Sink: 38871
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275651 seconds
Optimal solution found.
Minimum total cost = -33276202
Max flow value      : 29302
Min cost value      : -33276202
Total time          : 0.155054 seconds

========================================

Source: 58330, Sink: 25959
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277843 seconds
Optimal solution found.
Minimum total cost = -33560922
Max flow value      : 13070
Min cost value      : -33560922
Total time          : 0.118827 seconds

========================================

Source: 35634, Sink: 4782
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275454 seconds
Optimal solution found.
Minimum total cost = -33749204
Max flow value      : 30984
Min cost value      : -33749204
Total time          : 0.133977 seconds

========================================

Source: 19715, Sink: 9194
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27447 seconds
Optimal solution found.
Minimum total cost = -31902295
Max flow value      : 54975
Min cost value      : -31902295
Total time          : 0.146436 seconds

========================================

Source: 5588, Sink: 18189
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.235286 seconds
Optimal solution found.
Minimum total cost = -33698912
Max flow value      : 35624
Min cost value      : -33698912
Total time          : 0.139749 seconds

========================================

Source: 26237, Sink: 3619
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275375 seconds
Optimal solution found.
Minimum total cost = -33162428
Max flow value      : 24852
Min cost value      : -33162428
Total time          : 0.125574 seconds

========================================

Source: 23110, Sink: 2232
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27698 seconds
Optimal solution found.
Minimum total cost = -33673352
Max flow value      : 34215
Min cost value      : -33673352
Total time          : 0.168669 seconds

========================================

Source: 20939, Sink: 51479
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279007 seconds
Optimal solution found.
Minimum total cost = -33911546
Max flow value      : 36364
Min cost value      : -33911546
Total time          : 0.125687 seconds

========================================

Source: 61969, Sink: 19897
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275275 seconds
Optimal solution found.
Minimum total cost = -33334160
Max flow value      : 48806
Min cost value      : -33334160
Total time          : 0.151765 seconds

========================================

Source: 15877, Sink: 24890
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.2753 seconds
Optimal solution found.
Minimum total cost = -33966092
Max flow value      : 26640
Min cost value      : -33966092
Total time          : 0.11824 seconds

========================================

Source: 30822, Sink: 57062
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274383 seconds
Optimal solution found.
Minimum total cost = -33762224
Max flow value      : 12200
Min cost value      : -33762224
Total time          : 0.173474 seconds

========================================

Source: 55010, Sink: 62681
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.292359 seconds
Optimal solution found.
Minimum total cost = -33591380
Max flow value      : 30066
Min cost value      : -33591380
Total time          : 0.16526 seconds

========================================

Source: 26244, Sink: 3775
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275435 seconds
Optimal solution found.
Minimum total cost = -33771589
Max flow value      : 27456
Min cost value      : -33771589
Total time          : 0.245477 seconds

========================================

Source: 15479, Sink: 20594
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275307 seconds
Optimal solution found.
Minimum total cost = -33414445
Max flow value      : 26498
Min cost value      : -33414445
Total time          : 0.159378 seconds

========================================

Source: 22057, Sink: 3666
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275592 seconds
Optimal solution found.
Minimum total cost = -32983664
Max flow value      : 49842
Min cost value      : -32983664
Total time          : 0.208391 seconds

========================================

Source: 3985, Sink: 25517
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275932 seconds
Optimal solution found.
Minimum total cost = -31515721
Max flow value      : 51534
Min cost value      : -31515721
Total time          : 0.164919 seconds

========================================

Source: 43892, Sink: 28281
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276639 seconds
Optimal solution found.
Minimum total cost = -33309735
Max flow value      : 31583
Min cost value      : -33309735
Total time          : 0.13342 seconds

========================================

Source: 13649, Sink: 10688
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.232465 seconds
Optimal solution found.
Minimum total cost = -33146296
Max flow value      : 39010
Min cost value      : -33146296
Total time          : 0.1337 seconds

========================================

Source: 14052, Sink: 17475
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27606 seconds
Optimal solution found.
Minimum total cost = -33620060
Max flow value      : 9612
Min cost value      : -33620060
Total time          : 0.177163 seconds

========================================

Source: 49416, Sink: 22062
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280805 seconds
Optimal solution found.
Minimum total cost = -33644025
Max flow value      : 36895
Min cost value      : -33644025
Total time          : 0.15773 seconds

========================================

Source: 12656, Sink: 64935
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275627 seconds
Optimal solution found.
Minimum total cost = -33337832
Max flow value      : 41884
Min cost value      : -33337832
Total time          : 0.252369 seconds

========================================

Source: 16447, Sink: 799
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279416 seconds
Optimal solution found.
Minimum total cost = -33194719
Max flow value      : 56668
Min cost value      : -33194719
Total time          : 0.171449 seconds

========================================

Source: 40496, Sink: 21943
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280196 seconds
Optimal solution found.
Minimum total cost = -33775047
Max flow value      : 10421
Min cost value      : -33775047
Total time          : 0.154834 seconds

========================================

Source: 590, Sink: 26337
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275841 seconds
Optimal solution found.
Minimum total cost = -33495335
Max flow value      : 36865
Min cost value      : -33495335
Total time          : 0.155725 seconds

========================================

Source: 12715, Sink: 5067
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277576 seconds
Optimal solution found.
Minimum total cost = -33536565
Max flow value      : 25975
Min cost value      : -33536565
Total time          : 0.148865 seconds

========================================

Source: 28330, Sink: 35597
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276997 seconds
Optimal solution found.
Minimum total cost = -32665012
Max flow value      : 23656
Min cost value      : -32665012
Total time          : 0.174602 seconds

========================================

Source: 40484, Sink: 11961
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276988 seconds
Optimal solution found.
Minimum total cost = -34030152
Max flow value      : 6036
Min cost value      : -34030152
Total time          : 0.143603 seconds

========================================

Source: 2961, Sink: 57426
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.290059 seconds
Optimal solution found.
Minimum total cost = -33966092
Max flow value      : 3526
Min cost value      : -33966092
Total time          : 0.173628 seconds

========================================

Source: 28856, Sink: 56352
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276817 seconds
Optimal solution found.
Minimum total cost = -33863672
Max flow value      : 32980
Min cost value      : -33863672
Total time          : 0.162734 seconds

========================================

Source: 14579, Sink: 12096
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275197 seconds
Optimal solution found.
Minimum total cost = -33088646
Max flow value      : 36148
Min cost value      : -33088646
Total time          : 0.167826 seconds

========================================

Source: 51702, Sink: 5688
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274959 seconds
Optimal solution found.
Minimum total cost = -32968136
Max flow value      : 38424
Min cost value      : -32968136
Total time          : 0.274136 seconds

========================================

Source: 52762, Sink: 20835
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275737 seconds
Optimal solution found.
Minimum total cost = -32233192
Max flow value      : 68560
Min cost value      : -32233192
Total time          : 0.15994 seconds

========================================

Source: 37490, Sink: 17955
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277203 seconds
Optimal solution found.
Minimum total cost = -33757878
Max flow value      : 30720
Min cost value      : -33757878
Total time          : 0.252749 seconds

========================================

Source: 17740, Sink: 24275
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274646 seconds
Optimal solution found.
Minimum total cost = -33690530
Max flow value      : 31710
Min cost value      : -33690530
Total time          : 0.172742 seconds

========================================

Source: 48030, Sink: 1075
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275003 seconds
Optimal solution found.
Minimum total cost = -33221631
Max flow value      : 48946
Min cost value      : -33221631
Total time          : 0.144825 seconds

========================================

Source: 34273, Sink: 29796
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.2753 seconds
Optimal solution found.
Minimum total cost = -33010151
Max flow value      : 61942
Min cost value      : -33010151
Total time          : 0.191998 seconds

========================================

Source: 33026, Sink: 37065
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278191 seconds
Optimal solution found.
Minimum total cost = -33130435
Max flow value      : 38801
Min cost value      : -33130435
Total time          : 0.173345 seconds

========================================

Source: 33503, Sink: 4412
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275406 seconds
Optimal solution found.
Minimum total cost = -32631360
Max flow value      : 71197
Min cost value      : -32631360
Total time          : 0.160641 seconds

========================================

Source: 2870, Sink: 61504
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275926 seconds
Optimal solution found.
Minimum total cost = -33752032
Max flow value      : 21406
Min cost value      : -33752032
Total time          : 0.113241 seconds

========================================

Source: 65619, Sink: 60889
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276772 seconds
Optimal solution found.
Minimum total cost = -33455582
Max flow value      : 39432
Min cost value      : -33455582
Total time          : 0.137322 seconds

========================================

Source: 29245, Sink: 180
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275807 seconds
Optimal solution found.
Minimum total cost = -33877767
Max flow value      : 57115
Min cost value      : -33877767
Total time          : 0.175047 seconds

========================================

Source: 60533, Sink: 19193
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278689 seconds
Optimal solution found.
Minimum total cost = -33323527
Max flow value      : 57071
Min cost value      : -33323527
Total time          : 0.158837 seconds

========================================

Source: 28891, Sink: 63725
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279451 seconds
Optimal solution found.
Minimum total cost = -32425741
Max flow value      : 63407
Min cost value      : -32425741
Total time          : 0.17568 seconds

========================================

Source: 21884, Sink: 18206
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274773 seconds
Optimal solution found.
Minimum total cost = -34028440
Max flow value      : 4796
Min cost value      : -34028440
Total time          : 0.175064 seconds

========================================

Source: 18837, Sink: 32636
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27619 seconds
Optimal solution found.
Minimum total cost = -32861192
Max flow value      : 51764
Min cost value      : -32861192
Total time          : 0.170802 seconds

========================================

Source: 29843, Sink: 29448
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.281248 seconds
Optimal solution found.
Minimum total cost = -33021044
Max flow value      : 44286
Min cost value      : -33021044
Total time          : 0.234713 seconds

========================================

Source: 53121, Sink: 31238
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27661 seconds
Optimal solution found.
Minimum total cost = -33111433
Max flow value      : 33997
Min cost value      : -33111433
Total time          : 0.165061 seconds

========================================

Source: 23979, Sink: 52593
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277441 seconds
Optimal solution found.
Minimum total cost = -34089393
Max flow value      : 25390
Min cost value      : -34089393
Total time          : 0.246673 seconds

========================================

Source: 49853, Sink: 10831
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276433 seconds
Optimal solution found.
Minimum total cost = -34010948
Max flow value      : 3204
Min cost value      : -34010948
Total time          : 0.179711 seconds

========================================

Source: 13009, Sink: 30026
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277307 seconds
Optimal solution found.
Minimum total cost = -33966092
Max flow value      : 25103
Min cost value      : -33966092
Total time          : 0.119922 seconds

========================================

Source: 4397, Sink: 29570
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275272 seconds
Optimal solution found.
Minimum total cost = -33797207
Max flow value      : 30986
Min cost value      : -33797207
Total time          : 0.169274 seconds

========================================

Source: 17597, Sink: 55825
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277558 seconds
Optimal solution found.
Minimum total cost = -32869009
Max flow value      : 73096
Min cost value      : -32869009
Total time          : 0.188712 seconds

========================================

Source: 52415, Sink: 17376
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27523 seconds
Optimal solution found.
Minimum total cost = -34014560
Max flow value      : 3462
Min cost value      : -34014560
Total time          : 0.113878 seconds

========================================

Source: 61732, Sink: 32408
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.281424 seconds
Optimal solution found.
Minimum total cost = -33332761
Max flow value      : 19519
Min cost value      : -33332761
Total time          : 0.140242 seconds

========================================

Source: 24260, Sink: 40598
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.288833 seconds
Optimal solution found.
Minimum total cost = -33293181
Max flow value      : 17289
Min cost value      : -33293181
Total time          : 0.123685 seconds

========================================

Source: 55770, Sink: 31142
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.281144 seconds
Optimal solution found.
Minimum total cost = -32251971
Max flow value      : 56917
Min cost value      : -32251971
Total time          : 0.168201 seconds

========================================

Source: 11100, Sink: 5804
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277848 seconds
Optimal solution found.
Minimum total cost = -31655564
Max flow value      : 42402
Min cost value      : -31655564
Total time          : 0.132615 seconds

========================================

Source: 34097, Sink: 3039
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27907 seconds
Optimal solution found.
Minimum total cost = -33162486
Max flow value      : 30730
Min cost value      : -33162486
Total time          : 0.167674 seconds

========================================

Source: 16658, Sink: 65707
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27679 seconds
Optimal solution found.
Minimum total cost = -33978002
Max flow value      : 2382
Min cost value      : -33978002
Total time          : 0.175786 seconds

========================================

Source: 233, Sink: 52472
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279562 seconds
Optimal solution found.
Minimum total cost = -33957804
Max flow value      : 16576
Min cost value      : -33957804
Total time          : 0.183126 seconds

========================================

Source: 13001, Sink: 13750
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.236466 seconds
Optimal solution found.
Minimum total cost = -33484436
Max flow value      : 32428
Min cost value      : -33484436
Total time          : 0.128383 seconds

========================================

Source: 39858, Sink: 27255
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276396 seconds
Optimal solution found.
Minimum total cost = -33598718
Max flow value      : 17494
Min cost value      : -33598718
Total time          : 0.173658 seconds

========================================

Source: 31366, Sink: 32159
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27678 seconds
Optimal solution found.
Minimum total cost = -31288944
Max flow value      : 72686
Min cost value      : -31288944
Total time          : 0.157988 seconds

========================================

Source: 44258, Sink: 10366
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275802 seconds
Optimal solution found.
Minimum total cost = -32328629
Max flow value      : 72138
Min cost value      : -32328629
Total time          : 0.159464 seconds

========================================

Source: 18700, Sink: 6989
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279409 seconds
Optimal solution found.
Minimum total cost = -32459616
Max flow value      : 76749
Min cost value      : -32459616
Total time          : 0.180275 seconds

========================================

Source: 37947, Sink: 8772
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275357 seconds
Optimal solution found.
Minimum total cost = -33832988
Max flow value      : 16638
Min cost value      : -33832988
Total time          : 0.181085 seconds

========================================

Source: 11526, Sink: 13062
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274199 seconds
Optimal solution found.
Minimum total cost = -34011876
Max flow value      : 11446
Min cost value      : -34011876
Total time          : 0.18018 seconds

========================================

Source: 36928, Sink: 46077
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276786 seconds
Optimal solution found.
Minimum total cost = -33387620
Max flow value      : 13614
Min cost value      : -33387620
Total time          : 0.172111 seconds

========================================

Source: 50043, Sink: 26860
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276167 seconds
Optimal solution found.
Minimum total cost = -33950784
Max flow value      : 2932
Min cost value      : -33950784
Total time          : 0.151444 seconds

========================================

Source: 32349, Sink: 58743
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278144 seconds
Optimal solution found.
Minimum total cost = -32833952
Max flow value      : 37738
Min cost value      : -32833952
Total time          : 0.176779 seconds

========================================

Source: 41774, Sink: 63118
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27661 seconds
Optimal solution found.
Minimum total cost = -33913924
Max flow value      : 26084
Min cost value      : -33913924
Total time          : 0.189642 seconds

========================================

Source: 15877, Sink: 39868
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.300595 seconds
Optimal solution found.
Minimum total cost = -34051254
Max flow value      : 15484
Min cost value      : -34051254
Total time          : 0.166639 seconds

========================================

Source: 26798, Sink: 43755
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276097 seconds
Optimal solution found.
Minimum total cost = -33842983
Max flow value      : 5726
Min cost value      : -33842983
Total time          : 0.11222 seconds

========================================

Source: 3800, Sink: 5359
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.29772 seconds
Optimal solution found.
Minimum total cost = -32423429
Max flow value      : 64347
Min cost value      : -32423429
Total time          : 0.191958 seconds

========================================

Source: 47989, Sink: 56882
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275072 seconds
Optimal solution found.
Minimum total cost = -32688247
Max flow value      : 52964
Min cost value      : -32688247
Total time          : 0.164992 seconds

========================================

Source: 19680, Sink: 49622
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274977 seconds
Optimal solution found.
Minimum total cost = -33178517
Max flow value      : 41254
Min cost value      : -33178517
Total time          : 0.178607 seconds

========================================

Source: 62189, Sink: 49511
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277414 seconds
Optimal solution found.
Minimum total cost = -34035442
Max flow value      : 5548
Min cost value      : -34035442
Total time          : 0.172539 seconds

========================================

Source: 59218, Sink: 12486
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274754 seconds
Optimal solution found.
Minimum total cost = -33417943
Max flow value      : 19292
Min cost value      : -33417943
Total time          : 0.135401 seconds

========================================

Source: 25857, Sink: 21588
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.282995 seconds
Optimal solution found.
Minimum total cost = -32323371
Max flow value      : 36894
Min cost value      : -32323371
Total time          : 0.214218 seconds

========================================

Source: 46575, Sink: 2994
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274671 seconds
Optimal solution found.
Minimum total cost = -32115132
Max flow value      : 41476
Min cost value      : -32115132
Total time          : 0.180446 seconds

========================================

Source: 63048, Sink: 819
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277563 seconds
Optimal solution found.
Minimum total cost = -33905689
Max flow value      : 7984
Min cost value      : -33905689
Total time          : 0.124355 seconds

========================================

Source: 42053, Sink: 3808
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274193 seconds
Optimal solution found.
Minimum total cost = -33888469
Max flow value      : 2136
Min cost value      : -33888469
Total time          : 0.101685 seconds

========================================

Source: 35703, Sink: 12279
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276107 seconds
Optimal solution found.
Minimum total cost = -34015772
Max flow value      : 4968
Min cost value      : -34015772
Total time          : 0.107593 seconds

========================================

Source: 54096, Sink: 40368
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276438 seconds
Optimal solution found.
Minimum total cost = -31334252
Max flow value      : 93530
Min cost value      : -31334252
Total time          : 0.261494 seconds

========================================

Source: 65246, Sink: 44169
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.284567 seconds
Optimal solution found.
Minimum total cost = -33769530
Max flow value      : 19488
Min cost value      : -33769530
Total time          : 0.133687 seconds

========================================

Source: 60173, Sink: 9064
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.289872 seconds
Optimal solution found.
Minimum total cost = -33456433
Max flow value      : 24974
Min cost value      : -33456433
Total time          : 0.16123 seconds

========================================

Source: 48899, Sink: 53205
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.288206 seconds
Optimal solution found.
Minimum total cost = -33688628
Max flow value      : 49091
Min cost value      : -33688628
Total time          : 0.157154 seconds

========================================

Source: 58615, Sink: 9784
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274506 seconds
Optimal solution found.
Minimum total cost = -32703603
Max flow value      : 44391
Min cost value      : -32703603
Total time          : 0.169978 seconds

========================================

Source: 6144, Sink: 45304
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278266 seconds
Optimal solution found.
Minimum total cost = -32777596
Max flow value      : 37012
Min cost value      : -32777596
Total time          : 0.164398 seconds

========================================

Source: 30188, Sink: 10582
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277493 seconds
Optimal solution found.
Minimum total cost = -31302854
Max flow value      : 43788
Min cost value      : -31302854
Total time          : 0.131371 seconds

========================================

Source: 13254, Sink: 43542
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275865 seconds
Optimal solution found.
Minimum total cost = -33706716
Max flow value      : 39728
Min cost value      : -33706716
Total time          : 0.152422 seconds

========================================

Source: 25779, Sink: 23552
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276601 seconds
Optimal solution found.
Minimum total cost = -33760513
Max flow value      : 7744
Min cost value      : -33760513
Total time          : 0.11454 seconds

========================================

Source: 54022, Sink: 32232
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.2753 seconds
Optimal solution found.
Minimum total cost = -31728066
Max flow value      : 61001
Min cost value      : -31728066
Total time          : 0.170586 seconds

========================================

Source: 10518, Sink: 34553
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274749 seconds
Optimal solution found.
Minimum total cost = -31521889
Max flow value      : 37307
Min cost value      : -31521889
Total time          : 0.160988 seconds

========================================

Source: 64304, Sink: 14032
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276658 seconds
Optimal solution found.
Minimum total cost = -32417548
Max flow value      : 55492
Min cost value      : -32417548
Total time          : 0.176366 seconds

========================================

Source: 22895, Sink: 45452
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277495 seconds
Optimal solution found.
Minimum total cost = -32042365
Max flow value      : 34070
Min cost value      : -32042365
Total time          : 0.167701 seconds

========================================

Source: 18918, Sink: 3644
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276205 seconds
Optimal solution found.
Minimum total cost = -33319025
Max flow value      : 26567
Min cost value      : -33319025
Total time          : 0.281154 seconds

========================================

Source: 32031, Sink: 19295
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278508 seconds
Optimal solution found.
Minimum total cost = -33603697
Max flow value      : 13178
Min cost value      : -33603697
Total time          : 0.174143 seconds

========================================

Source: 62581, Sink: 62337
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277231 seconds
Optimal solution found.
Minimum total cost = -32188676
Max flow value      : 57394
Min cost value      : -32188676
Total time          : 0.177005 seconds

========================================

Source: 59342, Sink: 56099
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279942 seconds
Optimal solution found.
Minimum total cost = -32689390
Max flow value      : 67983
Min cost value      : -32689390
Total time          : 0.172882 seconds

========================================

Source: 12047, Sink: 35380
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278454 seconds
Optimal solution found.
Minimum total cost = -33978851
Max flow value      : 25518
Min cost value      : -33978851
Total time          : 0.135996 seconds

========================================

Source: 55317, Sink: 16550
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276899 seconds
Optimal solution found.
Minimum total cost = -33200477
Max flow value      : 36242
Min cost value      : -33200477
Total time          : 0.243371 seconds

========================================

Source: 4685, Sink: 5548
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276868 seconds
Optimal solution found.
Minimum total cost = -33346004
Max flow value      : 43050
Min cost value      : -33346004
Total time          : 0.151285 seconds

========================================

Source: 26793, Sink: 20135
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278939 seconds
Optimal solution found.
Minimum total cost = -33545486
Max flow value      : 26280
Min cost value      : -33545486
Total time          : 0.269479 seconds

========================================

Source: 5023, Sink: 60882
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278855 seconds
Optimal solution found.
Minimum total cost = -33638223
Max flow value      : 14586
Min cost value      : -33638223
Total time          : 0.17359 seconds

========================================

Source: 37947, Sink: 2548
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275529 seconds
Optimal solution found.
Minimum total cost = -33895604
Max flow value      : 6408
Min cost value      : -33895604
Total time          : 0.180839 seconds

========================================

Source: 14882, Sink: 60855
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276521 seconds
Optimal solution found.
Minimum total cost = -34030317
Max flow value      : 5138
Min cost value      : -34030317
Total time          : 0.112231 seconds

========================================

Source: 19856, Sink: 31865
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277982 seconds
Optimal solution found.
Minimum total cost = -33548789
Max flow value      : 33453
Min cost value      : -33548789
Total time          : 0.171155 seconds

========================================

Source: 63645, Sink: 40885
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276366 seconds
Optimal solution found.
Minimum total cost = -33996084
Max flow value      : 1304
Min cost value      : -33996084
Total time          : 0.101657 seconds

========================================

Source: 64158, Sink: 25149
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.283527 seconds
Optimal solution found.
Minimum total cost = -33809714
Max flow value      : 13346
Min cost value      : -33809714
Total time          : 0.175986 seconds

========================================

Source: 14599, Sink: 52444
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.284075 seconds
Optimal solution found.
Minimum total cost = -33892364
Max flow value      : 9216
Min cost value      : -33892364
Total time          : 0.105366 seconds

========================================

Source: 23134, Sink: 46045
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275971 seconds
Optimal solution found.
Minimum total cost = -33846165
Max flow value      : 6549
Min cost value      : -33846165
Total time          : 0.12258 seconds

========================================

Source: 5955, Sink: 27347
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279521 seconds
Optimal solution found.
Minimum total cost = -32194302
Max flow value      : 68793
Min cost value      : -32194302
Total time          : 0.156688 seconds

========================================

Source: 3452, Sink: 34044
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276507 seconds
Optimal solution found.
Minimum total cost = -32771657
Max flow value      : 57472
Min cost value      : -32771657
Total time          : 0.156095 seconds

========================================

Source: 49083, Sink: 30101
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276132 seconds
Optimal solution found.
Minimum total cost = -32740986
Max flow value      : 47108
Min cost value      : -32740986
Total time          : 0.156884 seconds

========================================

Source: 15183, Sink: 6930
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.2777 seconds
Optimal solution found.
Minimum total cost = -33911852
Max flow value      : 24610
Min cost value      : -33911852
Total time          : 0.191568 seconds

========================================

Source: 24444, Sink: 20113
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280262 seconds
Optimal solution found.
Minimum total cost = -33604152
Max flow value      : 29714
Min cost value      : -33604152
Total time          : 0.150991 seconds

========================================

Source: 24434, Sink: 16155
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27582 seconds
Optimal solution found.
Minimum total cost = -31726191
Max flow value      : 42032
Min cost value      : -31726191
Total time          : 0.147714 seconds

========================================

Source: 64774, Sink: 13976
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276369 seconds
Optimal solution found.
Minimum total cost = -33890452
Max flow value      : 13226
Min cost value      : -33890452
Total time          : 0.166775 seconds

========================================

Source: 33390, Sink: 52714
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275679 seconds
Optimal solution found.
Minimum total cost = -33647927
Max flow value      : 26488
Min cost value      : -33647927
Total time          : 0.12106 seconds

========================================

Source: 27840, Sink: 35212
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.283422 seconds
Optimal solution found.
Minimum total cost = -32485757
Max flow value      : 53520
Min cost value      : -32485757
Total time          : 0.223407 seconds

========================================

Source: 41082, Sink: 65919
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276066 seconds
Optimal solution found.
Minimum total cost = -33472757
Max flow value      : 21926
Min cost value      : -33472757
Total time          : 0.13686 seconds

========================================

Source: 30286, Sink: 44206
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277818 seconds
Optimal solution found.
Minimum total cost = -33069254
Max flow value      : 55524
Min cost value      : -33069254
Total time          : 0.157359 seconds

========================================

Source: 26753, Sink: 42888
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277 seconds
Optimal solution found.
Minimum total cost = -32254589
Max flow value      : 76852
Min cost value      : -32254589
Total time          : 0.176794 seconds

========================================

Source: 27647, Sink: 38977
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275181 seconds
Optimal solution found.
Minimum total cost = -33625607
Max flow value      : 10711
Min cost value      : -33625607
Total time          : 0.167776 seconds

========================================

Source: 26197, Sink: 33239
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279906 seconds
Optimal solution found.
Minimum total cost = -33239172
Max flow value      : 54406
Min cost value      : -33239172
Total time          : 0.15967 seconds

========================================

Source: 47106, Sink: 47443
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.231787 seconds
Optimal solution found.
Minimum total cost = -32759206
Max flow value      : 37706
Min cost value      : -32759206
Total time          : 0.135425 seconds

========================================

Source: 39800, Sink: 3390
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27719 seconds
Optimal solution found.
Minimum total cost = -33778212
Max flow value      : 17080
Min cost value      : -33778212
Total time          : 0.167915 seconds

========================================

Source: 20378, Sink: 4133
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277275 seconds
Optimal solution found.
Minimum total cost = -33732524
Max flow value      : 12976
Min cost value      : -33732524
Total time          : 0.178609 seconds

========================================

Source: 34319, Sink: 55770
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.232941 seconds
Optimal solution found.
Minimum total cost = -33983406
Max flow value      : 3148
Min cost value      : -33983406
Total time          : 0.0906553 seconds

========================================

Source: 17162, Sink: 26960
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276994 seconds
Optimal solution found.
Minimum total cost = -33973890
Max flow value      : 7798
Min cost value      : -33973890
Total time          : 0.115171 seconds

========================================

Source: 28610, Sink: 12190
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.235164 seconds
Optimal solution found.
Minimum total cost = -33820892
Max flow value      : 18150
Min cost value      : -33820892
Total time          : 0.0966585 seconds

========================================

Source: 60816, Sink: 34061
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.325532 seconds
Optimal solution found.
Minimum total cost = -33575012
Max flow value      : 19554
Min cost value      : -33575012
Total time          : 0.125063 seconds

========================================

Source: 35906, Sink: 13404
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280035 seconds
Optimal solution found.
Minimum total cost = -33469171
Max flow value      : 20198
Min cost value      : -33469171
Total time          : 0.129749 seconds

========================================

Source: 14912, Sink: 51254
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277508 seconds
Optimal solution found.
Minimum total cost = -33552980
Max flow value      : 19672
Min cost value      : -33552980
Total time          : 0.174986 seconds

========================================

Source: 14638, Sink: 36552
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275488 seconds
Optimal solution found.
Minimum total cost = -33227979
Max flow value      : 45624
Min cost value      : -33227979
Total time          : 0.162119 seconds

========================================

Source: 60742, Sink: 15474
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.285709 seconds
Optimal solution found.
Minimum total cost = -33505743
Max flow value      : 38395
Min cost value      : -33505743
Total time          : 0.163159 seconds

========================================

Source: 55470, Sink: 15433
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276953 seconds
Optimal solution found.
Minimum total cost = -33710148
Max flow value      : 42083
Min cost value      : -33710148
Total time          : 0.253694 seconds

========================================

Source: 34694, Sink: 65184
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.281183 seconds
Optimal solution found.
Minimum total cost = -33678690
Max flow value      : 8453
Min cost value      : -33678690
Total time          : 0.122085 seconds

========================================

Source: 7251, Sink: 4535
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.296667 seconds
Optimal solution found.
Minimum total cost = -34009852
Max flow value      : 2188
Min cost value      : -34009852
Total time          : 0.17496 seconds

========================================

Source: 8025, Sink: 53305
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.231961 seconds
Optimal solution found.
Minimum total cost = -34052852
Max flow value      : 21690
Min cost value      : -34052852
Total time          : 0.100927 seconds

========================================

Source: 61727, Sink: 58671
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275036 seconds
Optimal solution found.
Minimum total cost = -33569248
Max flow value      : 32548
Min cost value      : -33569248
Total time          : 0.169627 seconds

========================================

Source: 14186, Sink: 41493
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274303 seconds
Optimal solution found.
Minimum total cost = -32386703
Max flow value      : 38356
Min cost value      : -32386703
Total time          : 0.177905 seconds

========================================

Source: 42633, Sink: 19383
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.234445 seconds
Optimal solution found.
Minimum total cost = -32745746
Max flow value      : 39366
Min cost value      : -32745746
Total time          : 0.112537 seconds

========================================

Source: 53391, Sink: 59782
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278915 seconds
Optimal solution found.
Minimum total cost = -33231764
Max flow value      : 59050
Min cost value      : -33231764
Total time          : 0.153494 seconds

========================================

Source: 19487, Sink: 23038
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.28008 seconds
Optimal solution found.
Minimum total cost = -33206961
Max flow value      : 43484
Min cost value      : -33206961
Total time          : 0.148644 seconds

========================================

Source: 55833, Sink: 18291
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279646 seconds
Optimal solution found.
Minimum total cost = -32511317
Max flow value      : 29779
Min cost value      : -32511317
Total time          : 0.168527 seconds

========================================

Source: 8084, Sink: 37305
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276582 seconds
Optimal solution found.
Minimum total cost = -33230252
Max flow value      : 45763
Min cost value      : -33230252
Total time          : 0.169171 seconds

========================================

Source: 58549, Sink: 4012
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276464 seconds
Optimal solution found.
Minimum total cost = -33966092
Max flow value      : 42573
Min cost value      : -33966092
Total time          : 0.175226 seconds

========================================

Source: 41575, Sink: 52876
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.233703 seconds
Optimal solution found.
Minimum total cost = -33094292
Max flow value      : 55130
Min cost value      : -33094292
Total time          : 0.146045 seconds

========================================

Source: 39440, Sink: 27489
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.2772 seconds
Optimal solution found.
Minimum total cost = -33573412
Max flow value      : 39268
Min cost value      : -33573412
Total time          : 0.174568 seconds

========================================

Source: 64779, Sink: 51191
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.2761 seconds
Optimal solution found.
Minimum total cost = -33231614
Max flow value      : 39046
Min cost value      : -33231614
Total time          : 0.174088 seconds

========================================

Source: 65964, Sink: 27458
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.232754 seconds
Optimal solution found.
Minimum total cost = -32588467
Max flow value      : 26750
Min cost value      : -32588467
Total time          : 0.110888 seconds

========================================

Source: 19207, Sink: 40303
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278833 seconds
Optimal solution found.
Minimum total cost = -33959328
Max flow value      : 356
Min cost value      : -33959328
Total time          : 0.10106 seconds

========================================

Source: 11599, Sink: 24620
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274541 seconds
Optimal solution found.
Minimum total cost = -30108170
Max flow value      : 83500
Min cost value      : -30108170
Total time          : 0.170295 seconds

========================================

Source: 16915, Sink: 55913
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275323 seconds
Optimal solution found.
Minimum total cost = -33495277
Max flow value      : 28349
Min cost value      : -33495277
Total time          : 0.161721 seconds

========================================

Source: 16487, Sink: 2530
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274334 seconds
Optimal solution found.
Minimum total cost = -32135114
Max flow value      : 56832
Min cost value      : -32135114
Total time          : 0.15989 seconds

========================================

Source: 37221, Sink: 20438
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275967 seconds
Optimal solution found.
Minimum total cost = -34035485
Max flow value      : 34861
Min cost value      : -34035485
Total time          : 0.177412 seconds

========================================

Source: 55525, Sink: 17624
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279623 seconds
Optimal solution found.
Minimum total cost = -33703676
Max flow value      : 23856
Min cost value      : -33703676
Total time          : 0.115125 seconds

========================================

Source: 14480, Sink: 3674
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278934 seconds
Optimal solution found.
Minimum total cost = -33464890
Max flow value      : 27092
Min cost value      : -33464890
Total time          : 0.176535 seconds

========================================

Source: 51455, Sink: 12671
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276584 seconds
Optimal solution found.
Minimum total cost = -33591520
Max flow value      : 34052
Min cost value      : -33591520
Total time          : 0.177042 seconds

========================================

Source: 11354, Sink: 10089
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27959 seconds
Optimal solution found.
Minimum total cost = -33523908
Max flow value      : 28528
Min cost value      : -33523908
Total time          : 0.181472 seconds

========================================

Source: 13706, Sink: 57801
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276203 seconds
Optimal solution found.
Minimum total cost = -33308456
Max flow value      : 50009
Min cost value      : -33308456
Total time          : 0.170422 seconds

========================================

Source: 64909, Sink: 13544
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275847 seconds
Optimal solution found.
Minimum total cost = -32325203
Max flow value      : 69813
Min cost value      : -32325203
Total time          : 0.150094 seconds

========================================

Source: 13997, Sink: 26778
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.232191 seconds
Optimal solution found.
Minimum total cost = -32664725
Max flow value      : 48098
Min cost value      : -32664725
Total time          : 0.141011 seconds

========================================

Source: 43113, Sink: 4782
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276755 seconds
Optimal solution found.
Minimum total cost = -31635463
Max flow value      : 70730
Min cost value      : -31635463
Total time          : 0.148905 seconds

========================================

Source: 40791, Sink: 46587
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274569 seconds
Optimal solution found.
Minimum total cost = -33812620
Max flow value      : 9592
Min cost value      : -33812620
Total time          : 0.124749 seconds

========================================

Source: 39234, Sink: 14107
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275423 seconds
Optimal solution found.
Minimum total cost = -33290012
Max flow value      : 45762
Min cost value      : -33290012
Total time          : 0.17537 seconds

========================================

Source: 19042, Sink: 6892
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.2778 seconds
Optimal solution found.
Minimum total cost = -34019520
Max flow value      : 2812
Min cost value      : -34019520
Total time          : 0.0950449 seconds

========================================

Source: 63229, Sink: 52312
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274794 seconds
Optimal solution found.
Minimum total cost = -33247280
Max flow value      : 38118
Min cost value      : -33247280
Total time          : 0.262086 seconds

========================================

Source: 62931, Sink: 15363
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275965 seconds
Optimal solution found.
Minimum total cost = -33365628
Max flow value      : 42195
Min cost value      : -33365628
Total time          : 0.154346 seconds

========================================

Source: 22701, Sink: 29922
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276076 seconds
Optimal solution found.
Minimum total cost = -34025312
Max flow value      : 3948
Min cost value      : -34025312
Total time          : 0.16399 seconds

========================================

Source: 43465, Sink: 12639
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277244 seconds
Optimal solution found.
Minimum total cost = -33866762
Max flow value      : 9030
Min cost value      : -33866762
Total time          : 0.106249 seconds

========================================

Source: 20265, Sink: 52169
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27633 seconds
Optimal solution found.
Minimum total cost = -32985451
Max flow value      : 53000
Min cost value      : -32985451
Total time          : 0.159811 seconds

========================================

Source: 54172, Sink: 39358
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276194 seconds
Optimal solution found.
Minimum total cost = -33801288
Max flow value      : 38735
Min cost value      : -33801288
Total time          : 0.124235 seconds

========================================

Source: 666, Sink: 47114
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275586 seconds
Optimal solution found.
Minimum total cost = -33945932
Max flow value      : 1680
Min cost value      : -33945932
Total time          : 0.113047 seconds

========================================

Source: 50101, Sink: 64244
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277746 seconds
Optimal solution found.
Minimum total cost = -33604992
Max flow value      : 34826
Min cost value      : -33604992
Total time          : 0.208031 seconds

========================================

Source: 16644, Sink: 39271
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275455 seconds
Optimal solution found.
Minimum total cost = -32873774
Max flow value      : 67090
Min cost value      : -32873774
Total time          : 0.161757 seconds

========================================

Source: 54195, Sink: 54956
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.231456 seconds
Optimal solution found.
Minimum total cost = -33327635
Max flow value      : 38034
Min cost value      : -33327635
Total time          : 0.137744 seconds

========================================

Source: 6427, Sink: 19166
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.228881 seconds
Optimal solution found.
Minimum total cost = -33964046
Max flow value      : 186
Min cost value      : -33964046
Total time          : 0.192798 seconds

========================================

Source: 1669, Sink: 49684
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.289747 seconds
Optimal solution found.
Minimum total cost = -34025373
Max flow value      : 12332
Min cost value      : -34025373
Total time          : 0.154699 seconds

========================================

Source: 4620, Sink: 33766
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27755 seconds
Optimal solution found.
Minimum total cost = -33726042
Max flow value      : 29976
Min cost value      : -33726042
Total time          : 0.129531 seconds

========================================

Source: 14853, Sink: 57999
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276239 seconds
Optimal solution found.
Minimum total cost = -32817036
Max flow value      : 71937
Min cost value      : -32817036
Total time          : 0.161301 seconds

========================================

Source: 51153, Sink: 16719
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277889 seconds
Optimal solution found.
Minimum total cost = -32521114
Max flow value      : 47984
Min cost value      : -32521114
Total time          : 0.233504 seconds

========================================

Source: 26105, Sink: 52583
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279139 seconds
Optimal solution found.
Minimum total cost = -33852368
Max flow value      : 18954
Min cost value      : -33852368
Total time          : 0.177985 seconds

========================================

Source: 25071, Sink: 35113
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.285558 seconds
Optimal solution found.
Minimum total cost = -33831741
Max flow value      : 2258
Min cost value      : -33831741
Total time          : 0.115398 seconds

========================================

Source: 8045, Sink: 44806
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.2752 seconds
Optimal solution found.
Minimum total cost = -30670294
Max flow value      : 61109
Min cost value      : -30670294
Total time          : 0.263043 seconds

========================================

Source: 59991, Sink: 46383
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276496 seconds
Optimal solution found.
Minimum total cost = -33933405
Max flow value      : 19790
Min cost value      : -33933405
Total time          : 0.175329 seconds

========================================

Source: 17514, Sink: 54402
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27719 seconds
Optimal solution found.
Minimum total cost = -33177986
Max flow value      : 50934
Min cost value      : -33177986
Total time          : 0.16775 seconds

========================================

Source: 3997, Sink: 22397
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.293198 seconds
Optimal solution found.
Minimum total cost = -33966092
Max flow value      : 0
Min cost value      : -33966092
Total time          : 0.148772 seconds

========================================

Source: 8870, Sink: 26615
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275732 seconds
Optimal solution found.
Minimum total cost = -33791052
Max flow value      : 10940
Min cost value      : -33791052
Total time          : 0.182854 seconds

========================================

Source: 28734, Sink: 10891
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275923 seconds
Optimal solution found.
Minimum total cost = -33328332
Max flow value      : 34768
Min cost value      : -33328332
Total time          : 0.163137 seconds

========================================

Source: 19989, Sink: 32581
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27572 seconds
Optimal solution found.
Minimum total cost = -33728611
Max flow value      : 16378
Min cost value      : -33728611
Total time          : 0.166939 seconds

========================================

Source: 20693, Sink: 25564
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278342 seconds
Optimal solution found.
Minimum total cost = -32981383
Max flow value      : 40826
Min cost value      : -32981383
Total time          : 0.153064 seconds

========================================

Source: 52012, Sink: 21583
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.281946 seconds
Optimal solution found.
Minimum total cost = -33631922
Max flow value      : 18565
Min cost value      : -33631922
Total time          : 0.122226 seconds

========================================

Source: 701, Sink: 44228
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278114 seconds
Optimal solution found.
Minimum total cost = -33318094
Max flow value      : 52064
Min cost value      : -33318094
Total time          : 0.150564 seconds

========================================

Source: 25403, Sink: 44955
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.2801 seconds
Optimal solution found.
Minimum total cost = -32442989
Max flow value      : 59323
Min cost value      : -32442989
Total time          : 0.285195 seconds

========================================

Source: 25612, Sink: 60819
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.284211 seconds
Optimal solution found.
Minimum total cost = -33705509
Max flow value      : 21283
Min cost value      : -33705509
Total time          : 0.178323 seconds

========================================

Source: 23139, Sink: 40848
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280028 seconds
Optimal solution found.
Minimum total cost = -33826316
Max flow value      : 46592
Min cost value      : -33826316
Total time          : 0.167654 seconds

========================================

Source: 59173, Sink: 26253
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276729 seconds
Optimal solution found.
Minimum total cost = -33641260
Max flow value      : 27874
Min cost value      : -33641260
Total time          : 0.1528 seconds

========================================

Source: 50992, Sink: 47119
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277688 seconds
Optimal solution found.
Minimum total cost = -32906444
Max flow value      : 65785
Min cost value      : -32906444
Total time          : 0.165335 seconds

========================================

Source: 42608, Sink: 10658
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275781 seconds
Optimal solution found.
Minimum total cost = -33711966
Max flow value      : 9656
Min cost value      : -33711966
Total time          : 0.182218 seconds

========================================

Source: 20152, Sink: 31483
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276097 seconds
Optimal solution found.
Minimum total cost = -32020935
Max flow value      : 65873
Min cost value      : -32020935
Total time          : 0.183006 seconds

========================================

Source: 3243, Sink: 32354
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280713 seconds
Optimal solution found.
Minimum total cost = -33765516
Max flow value      : 21648
Min cost value      : -33765516
Total time          : 0.14989 seconds

========================================

Source: 7122, Sink: 43643
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275699 seconds
Optimal solution found.
Minimum total cost = -33428233
Max flow value      : 34708
Min cost value      : -33428233
Total time          : 0.13611 seconds

========================================

Source: 24007, Sink: 24774
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277021 seconds
Optimal solution found.
Minimum total cost = -32591780
Max flow value      : 34158
Min cost value      : -32591780
Total time          : 0.14599 seconds

========================================

Source: 16205, Sink: 61918
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.295352 seconds
Optimal solution found.
Minimum total cost = -34017802
Max flow value      : 20684
Min cost value      : -34017802
Total time          : 0.177332 seconds

========================================

Source: 24509, Sink: 18642
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278475 seconds
Optimal solution found.
Minimum total cost = -32695146
Max flow value      : 38873
Min cost value      : -32695146
Total time          : 0.226807 seconds

========================================

Source: 21588, Sink: 52372
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.283919 seconds
Optimal solution found.
Minimum total cost = -33966092
Max flow value      : 36894
Min cost value      : -33966092
Total time          : 0.14718 seconds

========================================

Source: 13634, Sink: 40885
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277658 seconds
Optimal solution found.
Minimum total cost = -33398087
Max flow value      : 35958
Min cost value      : -33398087
Total time          : 0.16128 seconds

========================================

Source: 10169, Sink: 20485
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275204 seconds
Optimal solution found.
Minimum total cost = -33298311
Max flow value      : 50248
Min cost value      : -33298311
Total time          : 0.144966 seconds

========================================

Source: 2076, Sink: 33172
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27648 seconds
Optimal solution found.
Minimum total cost = -33654534
Max flow value      : 35634
Min cost value      : -33654534
Total time          : 0.169975 seconds

========================================

Source: 22149, Sink: 18820
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277606 seconds
Optimal solution found.
Minimum total cost = -31583892
Max flow value      : 91960
Min cost value      : -31583892
Total time          : 0.159934 seconds

========================================

Source: 47350, Sink: 49068
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275619 seconds
Optimal solution found.
Minimum total cost = -33946548
Max flow value      : 4886
Min cost value      : -33946548
Total time          : 0.109229 seconds

========================================

Source: 15814, Sink: 40505
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275795 seconds
Optimal solution found.
Minimum total cost = -33423095
Max flow value      : 35847
Min cost value      : -33423095
Total time          : 0.154801 seconds

========================================

Source: 42503, Sink: 56962
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275368 seconds
Optimal solution found.
Minimum total cost = -33633213
Max flow value      : 28946
Min cost value      : -33633213
Total time          : 0.177589 seconds

========================================

Source: 37758, Sink: 9610
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278809 seconds
Optimal solution found.
Minimum total cost = -32907022
Max flow value      : 26500
Min cost value      : -32907022
Total time          : 0.124901 seconds

========================================

Source: 29225, Sink: 57788
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274986 seconds
Optimal solution found.
Minimum total cost = -33592100
Max flow value      : 13556
Min cost value      : -33592100
Total time          : 0.137566 seconds

========================================

Source: 57490, Sink: 22226
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276627 seconds
Optimal solution found.
Minimum total cost = -33637756
Max flow value      : 41828
Min cost value      : -33637756
Total time          : 0.1483 seconds

========================================

Source: 6773, Sink: 14067
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.290481 seconds
Optimal solution found.
Minimum total cost = -33787474
Max flow value      : 15532
Min cost value      : -33787474
Total time          : 0.119005 seconds

========================================

Source: 46743, Sink: 63785
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279442 seconds
Optimal solution found.
Minimum total cost = -32219026
Max flow value      : 76070
Min cost value      : -32219026
Total time          : 0.180948 seconds

========================================

Source: 5510, Sink: 6226
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277313 seconds
Optimal solution found.
Minimum total cost = -33881850
Max flow value      : 15076
Min cost value      : -33881850
Total time          : 0.172669 seconds

========================================

Source: 59028, Sink: 1048
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27832 seconds
Optimal solution found.
Minimum total cost = -33650441
Max flow value      : 37719
Min cost value      : -33650441
Total time          : 0.175876 seconds

========================================

Source: 33960, Sink: 43278
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277876 seconds
Optimal solution found.
Minimum total cost = -33724877
Max flow value      : 12370
Min cost value      : -33724877
Total time          : 0.177781 seconds

========================================

Source: 59158, Sink: 7403
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275378 seconds
Optimal solution found.
Minimum total cost = -33942486
Max flow value      : 2146
Min cost value      : -33942486
Total time          : 0.158566 seconds

========================================

Source: 64893, Sink: 36911
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275134 seconds
Optimal solution found.
Minimum total cost = -33017966
Max flow value      : 52744
Min cost value      : -33017966
Total time          : 0.148274 seconds

========================================

Source: 64993, Sink: 45550
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275895 seconds
Optimal solution found.
Minimum total cost = -34008392
Max flow value      : 1410
Min cost value      : -34008392
Total time          : 0.175127 seconds

========================================

Source: 39817, Sink: 10310
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279849 seconds
Optimal solution found.
Minimum total cost = -33906571
Max flow value      : 26900
Min cost value      : -33906571
Total time          : 0.171414 seconds

========================================

Source: 8925, Sink: 14933
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274967 seconds
Optimal solution found.
Minimum total cost = -34010957
Max flow value      : 17946
Min cost value      : -34010957
Total time          : 0.105881 seconds

========================================

Source: 30279, Sink: 58802
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279007 seconds
Optimal solution found.
Minimum total cost = -32270522
Max flow value      : 69506
Min cost value      : -32270522
Total time          : 0.212803 seconds

========================================

Source: 12047, Sink: 32334
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276814 seconds
Optimal solution found.
Minimum total cost = -34045829
Max flow value      : 25518
Min cost value      : -34045829
Total time          : 0.158535 seconds

========================================

Source: 27636, Sink: 52525
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278074 seconds
Optimal solution found.
Minimum total cost = -32595700
Max flow value      : 37448
Min cost value      : -32595700
Total time          : 0.284319 seconds

========================================

Source: 64148, Sink: 44233
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278158 seconds
Optimal solution found.
Minimum total cost = -33718145
Max flow value      : 23614
Min cost value      : -33718145
Total time          : 0.180957 seconds

========================================

Source: 13872, Sink: 32528
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.285337 seconds
Optimal solution found.
Minimum total cost = -33834305
Max flow value      : 21977
Min cost value      : -33834305
Total time          : 0.165049 seconds

========================================

Source: 35533, Sink: 36953
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279284 seconds
Optimal solution found.
Minimum total cost = -32994128
Max flow value      : 22344
Min cost value      : -32994128
Total time          : 0.129139 seconds

========================================

Source: 8457, Sink: 24577
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276427 seconds
Optimal solution found.
Minimum total cost = -32695104
Max flow value      : 37382
Min cost value      : -32695104
Total time          : 0.131063 seconds

========================================

Source: 35410, Sink: 27561
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276765 seconds
Optimal solution found.
Minimum total cost = -32488584
Max flow value      : 35063
Min cost value      : -32488584
Total time          : 0.148179 seconds

========================================

Source: 54896, Sink: 34865
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278182 seconds
Optimal solution found.
Minimum total cost = -33688652
Max flow value      : 36992
Min cost value      : -33688652
Total time          : 0.129887 seconds

========================================

Source: 21583, Sink: 9891
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275769 seconds
Optimal solution found.
Minimum total cost = -33932230
Max flow value      : 33862
Min cost value      : -33932230
Total time          : 0.16084 seconds

========================================

Source: 33709, Sink: 60533
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274856 seconds
Optimal solution found.
Minimum total cost = -33312887
Max flow value      : 22750
Min cost value      : -33312887
Total time          : 0.145176 seconds

========================================

Source: 57055, Sink: 37478
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27713 seconds
Optimal solution found.
Minimum total cost = -33613157
Max flow value      : 25923
Min cost value      : -33613157
Total time          : 0.165276 seconds

========================================

Source: 13014, Sink: 14010
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276678 seconds
Optimal solution found.
Minimum total cost = -33855824
Max flow value      : 13613
Min cost value      : -33855824
Total time          : 0.125695 seconds

========================================

Source: 48769, Sink: 14370
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277954 seconds
Optimal solution found.
Minimum total cost = -32713878
Max flow value      : 40394
Min cost value      : -32713878
Total time          : 0.176596 seconds

========================================

Source: 49597, Sink: 10553
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275144 seconds
Optimal solution found.
Minimum total cost = -33986843
Max flow value      : 13834
Min cost value      : -33986843
Total time          : 0.171799 seconds

========================================

Source: 22013, Sink: 17072
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274847 seconds
Optimal solution found.
Minimum total cost = -34041266
Max flow value      : 21068
Min cost value      : -34041266
Total time          : 0.168479 seconds

========================================

Source: 59534, Sink: 25026
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279738 seconds
Optimal solution found.
Minimum total cost = -32687512
Max flow value      : 48362
Min cost value      : -32687512
Total time          : 0.151594 seconds

========================================

Source: 12792, Sink: 7140
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274714 seconds
Optimal solution found.
Minimum total cost = -32567754
Max flow value      : 22483
Min cost value      : -32567754
Total time          : 0.160936 seconds

========================================

Source: 31412, Sink: 24029
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279105 seconds
Optimal solution found.
Minimum total cost = -33004242
Max flow value      : 57774
Min cost value      : -33004242
Total time          : 0.16005 seconds

========================================

Source: 61589, Sink: 60128
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280553 seconds
Optimal solution found.
Minimum total cost = -33189122
Max flow value      : 31751
Min cost value      : -33189122
Total time          : 0.137777 seconds

========================================

Source: 35906, Sink: 45527
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280642 seconds
Optimal solution found.
Minimum total cost = -33612627
Max flow value      : 20198
Min cost value      : -33612627
Total time          : 0.129838 seconds

========================================

Source: 56388, Sink: 29351
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279429 seconds
Optimal solution found.
Minimum total cost = -33520298
Max flow value      : 29025
Min cost value      : -33520298
Total time          : 0.125606 seconds

========================================

Source: 7510, Sink: 6011
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.232919 seconds
Optimal solution found.
Minimum total cost = -33900966
Max flow value      : 10094
Min cost value      : -33900966
Total time          : 0.148425 seconds

========================================

Source: 23925, Sink: 9674
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279111 seconds
Optimal solution found.
Minimum total cost = -32772214
Max flow value      : 68420
Min cost value      : -32772214
Total time          : 0.153911 seconds

========================================

Source: 59254, Sink: 35075
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274776 seconds
Optimal solution found.
Minimum total cost = -33149541
Max flow value      : 53552
Min cost value      : -33149541
Total time          : 0.155258 seconds

========================================

Source: 12276, Sink: 42712
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276229 seconds
Optimal solution found.
Minimum total cost = -33045646
Max flow value      : 23920
Min cost value      : -33045646
Total time          : 0.18248 seconds

========================================

Source: 733, Sink: 48627
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276897 seconds
Optimal solution found.
Minimum total cost = -31391574
Max flow value      : 48571
Min cost value      : -31391574
Total time          : 0.163892 seconds

========================================

Source: 16610, Sink: 40163
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274808 seconds
Optimal solution found.
Minimum total cost = -33939274
Max flow value      : 2438
Min cost value      : -33939274
Total time          : 0.163563 seconds

========================================

Source: 24943, Sink: 31291
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274625 seconds
Optimal solution found.
Minimum total cost = -33555941
Max flow value      : 27818
Min cost value      : -33555941
Total time          : 0.167834 seconds

========================================

Source: 60498, Sink: 10569
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.286725 seconds
Optimal solution found.
Minimum total cost = -33822137
Max flow value      : 13710
Min cost value      : -33822137
Total time          : 0.173602 seconds

========================================

Source: 51975, Sink: 50296
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276973 seconds
Optimal solution found.
Minimum total cost = -33910815
Max flow value      : 39161
Min cost value      : -33910815
Total time          : 0.166593 seconds

========================================

Source: 51509, Sink: 49207
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276919 seconds
Optimal solution found.
Minimum total cost = -33775616
Max flow value      : 7326
Min cost value      : -33775616
Total time          : 0.0983657 seconds

========================================

Source: 43899, Sink: 14963
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.290118 seconds
Optimal solution found.
Minimum total cost = -33694526
Max flow value      : 36592
Min cost value      : -33694526
Total time          : 0.162656 seconds

========================================

Source: 37493, Sink: 2091
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.282306 seconds
Optimal solution found.
Minimum total cost = -33643784
Max flow value      : 31755
Min cost value      : -33643784
Total time          : 0.177915 seconds

========================================

Source: 55147, Sink: 48223
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276796 seconds
Optimal solution found.
Minimum total cost = -31417215
Max flow value      : 48538
Min cost value      : -31417215
Total time          : 0.156351 seconds

========================================

Source: 44265, Sink: 20975
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274198 seconds
Optimal solution found.
Minimum total cost = -32847704
Max flow value      : 37028
Min cost value      : -32847704
Total time          : 0.148678 seconds

========================================

Source: 43139, Sink: 9411
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277998 seconds
Optimal solution found.
Minimum total cost = -33141366
Max flow value      : 52916
Min cost value      : -33141366
Total time          : 0.149633 seconds

========================================

Source: 13842, Sink: 18181
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27779 seconds
Optimal solution found.
Minimum total cost = -33977640
Max flow value      : 23096
Min cost value      : -33977640
Total time          : 0.114691 seconds

========================================

Source: 31344, Sink: 38116
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276855 seconds
Optimal solution found.
Minimum total cost = -33705713
Max flow value      : 24798
Min cost value      : -33705713
Total time          : 0.114757 seconds

========================================

Source: 56817, Sink: 50006
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27581 seconds
Optimal solution found.
Minimum total cost = -32933133
Max flow value      : 51858
Min cost value      : -32933133
Total time          : 0.157575 seconds

========================================

Source: 21209, Sink: 34231
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275299 seconds
Optimal solution found.
Minimum total cost = -32612942
Max flow value      : 32056
Min cost value      : -32612942
Total time          : 0.13692 seconds

========================================

Source: 34097, Sink: 8718
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277073 seconds
Optimal solution found.
Minimum total cost = -33966092
Max flow value      : 24966
Min cost value      : -33966092
Total time          : 0.151187 seconds

========================================

Source: 53683, Sink: 21449
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276379 seconds
Optimal solution found.
Minimum total cost = -31923713
Max flow value      : 43548
Min cost value      : -31923713
Total time          : 0.158775 seconds

========================================

Source: 44875, Sink: 26693
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276084 seconds
Optimal solution found.
Minimum total cost = -33781082
Max flow value      : 33061
Min cost value      : -33781082
Total time          : 0.120418 seconds

========================================

Source: 21983, Sink: 54454
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275993 seconds
Optimal solution found.
Minimum total cost = -32221928
Max flow value      : 36476
Min cost value      : -32221928
Total time          : 0.268846 seconds

========================================

Source: 53731, Sink: 37758
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276328 seconds
Optimal solution found.
Minimum total cost = -33594036
Max flow value      : 45952
Min cost value      : -33594036
Total time          : 0.267192 seconds

========================================

Source: 20500, Sink: 12564
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276404 seconds
Optimal solution found.
Minimum total cost = -33993505
Max flow value      : 24426
Min cost value      : -33993505
Total time          : 0.123827 seconds

========================================

Source: 29757, Sink: 34702
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.283607 seconds
Optimal solution found.
Minimum total cost = -33794672
Max flow value      : 15366
Min cost value      : -33794672
Total time          : 0.133284 seconds

========================================

Source: 1503, Sink: 59384
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275487 seconds
Optimal solution found.
Minimum total cost = -33791160
Max flow value      : 23576
Min cost value      : -33791160
Total time          : 0.133886 seconds

========================================

Source: 263, Sink: 26615
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279391 seconds
Optimal solution found.
Minimum total cost = -34031732
Max flow value      : 10940
Min cost value      : -34031732
Total time          : 0.1689 seconds

========================================

Source: 56751, Sink: 57740
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276064 seconds
Optimal solution found.
Minimum total cost = -33874506
Max flow value      : 11121
Min cost value      : -33874506
Total time          : 0.160179 seconds

========================================

Source: 59120, Sink: 59173
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.281103 seconds
Optimal solution found.
Minimum total cost = -32328161
Max flow value      : 45040
Min cost value      : -32328161
Total time          : 0.15929 seconds

========================================

Source: 14370, Sink: 34464
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.270891 seconds
Optimal solution found.
Minimum total cost = -32138180
Max flow value      : 61876
Min cost value      : -32138180
Total time          : 0.140608 seconds

========================================

Source: 13847, Sink: 53755
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278962 seconds
Optimal solution found.
Minimum total cost = -33385656
Max flow value      : 31730
Min cost value      : -33385656
Total time          : 0.205641 seconds

========================================

Source: 46481, Sink: 37802
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277006 seconds
Optimal solution found.
Minimum total cost = -33992720
Max flow value      : 26628
Min cost value      : -33992720
Total time          : 0.173006 seconds

========================================

Source: 50330, Sink: 39725
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277734 seconds
Optimal solution found.
Minimum total cost = -33242702
Max flow value      : 47025
Min cost value      : -33242702
Total time          : 0.170848 seconds

========================================

Source: 32393, Sink: 56228
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.281091 seconds
Optimal solution found.
Minimum total cost = -32934824
Max flow value      : 41564
Min cost value      : -32934824
Total time          : 0.178258 seconds

========================================

Source: 16275, Sink: 22283
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278703 seconds
Optimal solution found.
Minimum total cost = -33909618
Max flow value      : 5134
Min cost value      : -33909618
Total time          : 0.107324 seconds

========================================

Source: 65796, Sink: 51445
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.288918 seconds
Optimal solution found.
Minimum total cost = -33295340
Max flow value      : 25808
Min cost value      : -33295340
Total time          : 0.119255 seconds

========================================

Source: 19469, Sink: 23472
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276638 seconds
Optimal solution found.
Minimum total cost = -31990027
Max flow value      : 74781
Min cost value      : -31990027
Total time          : 0.242367 seconds

========================================

Source: 8830, Sink: 31802
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.230756 seconds
Optimal solution found.
Minimum total cost = -31996355
Max flow value      : 96068
Min cost value      : -31996355
Total time          : 0.176916 seconds

========================================

Source: 62411, Sink: 33040
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275443 seconds
Optimal solution found.
Minimum total cost = -33979736
Max flow value      : 3032
Min cost value      : -33979736
Total time          : 0.165079 seconds

========================================

Source: 54441, Sink: 24870
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27438 seconds
Optimal solution found.
Minimum total cost = -32693406
Max flow value      : 53652
Min cost value      : -32693406
Total time          : 0.261861 seconds

========================================

Source: 51572, Sink: 24828
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275373 seconds
Optimal solution found.
Minimum total cost = -33851677
Max flow value      : 4670
Min cost value      : -33851677
Total time          : 0.176425 seconds

========================================

Source: 36276, Sink: 49872
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.232143 seconds
Optimal solution found.
Minimum total cost = -33839163
Max flow value      : 5522
Min cost value      : -33839163
Total time          : 0.0877807 seconds

========================================

Source: 44704, Sink: 29669
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27654 seconds
Optimal solution found.
Minimum total cost = -33151269
Max flow value      : 30681
Min cost value      : -33151269
Total time          : 0.164604 seconds

========================================

Source: 13852, Sink: 45069
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279166 seconds
Optimal solution found.
Minimum total cost = -33257972
Max flow value      : 45150
Min cost value      : -33257972
Total time          : 0.176555 seconds

========================================

Source: 20719, Sink: 849
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.300318 seconds
Optimal solution found.
Minimum total cost = -33949130
Max flow value      : 24842
Min cost value      : -33949130
Total time          : 0.180806 seconds

========================================

Source: 31799, Sink: 49361
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276442 seconds
Optimal solution found.
Minimum total cost = -33832654
Max flow value      : 7510
Min cost value      : -33832654
Total time          : 0.193356 seconds

========================================

Source: 7258, Sink: 13062
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279189 seconds
Optimal solution found.
Minimum total cost = -34000430
Max flow value      : 11446
Min cost value      : -34000430
Total time          : 0.172182 seconds

========================================

Source: 1742, Sink: 42347
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.283985 seconds
Optimal solution found.
Minimum total cost = -33596061
Max flow value      : 44530
Min cost value      : -33596061
Total time          : 0.25015 seconds

========================================

Source: 38215, Sink: 35530
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.319957 seconds
Optimal solution found.
Minimum total cost = -33547212
Max flow value      : 10472
Min cost value      : -33547212
Total time          : 0.258148 seconds

========================================

Source: 45669, Sink: 65773
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276755 seconds
Optimal solution found.
Minimum total cost = -33281454
Max flow value      : 37431
Min cost value      : -33281454
Total time          : 0.167181 seconds

========================================

Source: 6690, Sink: 29635
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.276513 seconds
Optimal solution found.
Minimum total cost = -34043389
Max flow value      : 14054
Min cost value      : -34043389
Total time          : 0.169327 seconds

========================================

Source: 5483, Sink: 51467
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279531 seconds
Optimal solution found.
Minimum total cost = -33798958
Max flow value      : 35096
Min cost value      : -33798958
Total time          : 0.119153 seconds

========================================

Source: 18218, Sink: 25959
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.232822 seconds
Optimal solution found.
Minimum total cost = -32078588
Max flow value      : 39100
Min cost value      : -32078588
Total time          : 0.147882 seconds

========================================

Source: 20245, Sink: 64192
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275847 seconds
Optimal solution found.
Minimum total cost = -32713912
Max flow value      : 50117
Min cost value      : -32713912
Total time          : 0.174804 seconds

========================================

Source: 63943, Sink: 21322
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275058 seconds
Optimal solution found.
Minimum total cost = -33961255
Max flow value      : 1382
Min cost value      : -33961255
Total time          : 0.100258 seconds

========================================

Source: 23384, Sink: 63061
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.278144 seconds
Optimal solution found.
Minimum total cost = -33566601
Max flow value      : 23978
Min cost value      : -33566601
Total time          : 0.162662 seconds

========================================

Source: 15310, Sink: 32757
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275467 seconds
Optimal solution found.
Minimum total cost = -33042449
Max flow value      : 29322
Min cost value      : -33042449
Total time          : 0.127905 seconds

========================================

Source: 65496, Sink: 55103
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.280176 seconds
Optimal solution found.
Minimum total cost = -33937912
Max flow value      : 14090
Min cost value      : -33937912
Total time          : 0.172335 seconds

========================================

Source: 58435, Sink: 47465
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.277283 seconds
Optimal solution found.
Minimum total cost = -33278706
Max flow value      : 18239
Min cost value      : -33278706
Total time          : 0.122148 seconds

========================================

Source: 46796, Sink: 13346
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274307 seconds
Optimal solution found.
Minimum total cost = -33966092
Max flow value      : 2464
Min cost value      : -33966092
Total time          : 0.175224 seconds

========================================

Source: 41024, Sink: 58207
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.274728 seconds
Optimal solution found.
Minimum total cost = -31026842
Max flow value      : 44772
Min cost value      : -31026842
Total time          : 0.175984 seconds

========================================

Source: 12959, Sink: 49811
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.283509 seconds
Optimal solution found.
Minimum total cost = -33310931
Max flow value      : 42338
Min cost value      : -33310931
Total time          : 0.158227 seconds

========================================

Source: 20463, Sink: 4437
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.279567 seconds
Optimal solution found.
Minimum total cost = -33130555
Max flow value      : 29336
Min cost value      : -33130555
Total time          : 0.152345 seconds

========================================

Source: 30966, Sink: 59657
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.275326 seconds
Optimal solution found.
Minimum total cost = -32101769
Max flow value      : 57969
Min cost value      : -32101769
Total time          : 0.170576 seconds

========================================

Source: 64490, Sink: 35142
Reading graph from file: ./test_data_small/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs in 0.27775 seconds
Optimal solution found.
Minimum total cost = -33703391
Max flow value      : 30906
Min cost value      : -33703391
Total time          : 0.133215 seconds

========================================

