Starting allocation process for ****************************************** with amount 60000000000
Converting 60000000000 tokens to 23683.33241912817 ETH equivalent

===== INITIAL PATH WEIGHTS AND RANKING =====
Rank 1: Path 3 - Weight: -18.27547318136225, Capacity: 0
Rank 2: Path 4 - Weight: -12.117794493752584, Capacity: 0
Rank 3: Path 1 - Weight: -12.112031220646552, Capacity: 0
Rank 4: Path 5 - Weight: -12.1102903597573, Capacity: 0
Rank 5: Path 8 - Weight: -12.10278622576202, Capacity: 0
============================================


===== ALLOCATION ROUND 1 =====
Path 3 has zero capacity, marking as exhausted

----- RECALCULATING PATH WEIGHTS AND CAPACITIES -----
Edge 1 of path 1 (******************************************->******************************************) is completely exhausted
Edge 2 of path 1 (******************************************->******************************************) is completely exhausted
Path 1 is exhausted (one or more edges have no available pools)
Edge 1 of path 2 (******************************************->******************************************) is completely exhausted
Edge 2 of path 2 (******************************************->0x0000000000c5dc95539589fbd24be07c6c14eca4) is completely exhausted
Edge 3 of path 2 (0x0000000000c5dc95539589fbd24be07c6c14eca4->******************************************) is completely exhausted
Path 2 is exhausted (one or more edges have no available pools)
Edge 1 of path 4 (******************************************->0xa0b86991c6218b36c1d19d4a2e9eb0ce3606eb48) is completely exhausted
Edge 2 of path 4 (0xa0b86991c6218b36c1d19d4a2e9eb0ce3606eb48->******************************************) is completely exhausted
Edge 3 of path 4 (******************************************->******************************************) is completely exhausted
Path 4 is exhausted (one or more edges have no available pools)
Edge 1 of path 5 (******************************************->0xdac17f958d2ee523a2206206994597c13d831ec7) is completely exhausted
Edge 2 of path 5 (0xdac17f958d2ee523a2206206994597c13d831ec7->******************************************) is completely exhausted
Edge 3 of path 5 (******************************************->******************************************) is completely exhausted
Path 5 is exhausted (one or more edges have no available pools)
Edge 1 of path 6 (******************************************->0xcbb7c0000ab88b473b1f5afd9ef808440eed33bf) is completely exhausted
Edge 2 of path 6 (0xcbb7c0000ab88b473b1f5afd9ef808440eed33bf->******************************************) is completely exhausted
Edge 3 of path 6 (******************************************->******************************************) is completely exhausted
Path 6 is exhausted (one or more edges have no available pools)
Edge 1 of path 7 (******************************************->0x3472a5a71965499acd81997a54bba8d852c6e53d) is completely exhausted
Edge 2 of path 7 (0x3472a5a71965499acd81997a54bba8d852c6e53d->******************************************) is completely exhausted
Edge 3 of path 7 (******************************************->******************************************) is completely exhausted
Path 7 is exhausted (one or more edges have no available pools)
Edge 1 of path 8 (******************************************->0x6b175474e89094c44da98b954eedeac495271d0f) is completely exhausted
Edge 2 of path 8 (0x6b175474e89094c44da98b954eedeac495271d0f->******************************************) is completely exhausted
Edge 3 of path 8 (******************************************->******************************************) is completely exhausted
Path 8 is exhausted (one or more edges have no available pools)
Edge 1 of path 9 (******************************************->0x1f9840a85d5af5bf1d1762f925bdaddc4201f984) is completely exhausted
Edge 2 of path 9 (0x1f9840a85d5af5bf1d1762f925bdaddc4201f984->******************************************) is completely exhausted
Edge 3 of path 9 (******************************************->******************************************) is completely exhausted
Path 9 is exhausted (one or more edges have no available pools)
Edge 1 of path 10 (******************************************->0x582d872a1b094fc48f5de31d3b73f2d9be47def1) is completely exhausted
Edge 2 of path 10 (0x582d872a1b094fc48f5de31d3b73f2d9be47def1->******************************************) is completely exhausted
Edge 3 of path 10 (******************************************->******************************************) is completely exhausted
Path 10 is exhausted (one or more edges have no available pools)
Edge 1 of path 11 (******************************************->0x55296f69f40ea6d20e478533c15a6b08b654e758) is completely exhausted
Edge 2 of path 11 (0x55296f69f40ea6d20e478533c15a6b08b654e758->******************************************) is completely exhausted
Edge 3 of path 11 (******************************************->******************************************) is completely exhausted
Path 11 is exhausted (one or more edges have no available pools)

----- UPDATED PATH RANKING -----
Reranking round 1: 0 paths changed
Update time: 0.000000s, Sort time: 0.000000s
--------------------------------
No more paths with capacity available

===== ALLOCATION COMPLETED =====
Allocated to 0 paths: []
Total allocation: 0.0 (0.00%)
Remaining amount (original units): 60000000000.0

Path Input Amounts:

Pool Proportions (direct calculation):

Detailed Pool Allocations (by path and edge):

Allocation log saved to: results/log/allocation_log_0x22_60000000000.txt
