import pandas as pd
import json
import os
import time
from decimal import Decimal, getcontext
import re
from collections import defaultdict
import argparse
import copy

# Set ultra-high precision for financial calculations
getcontext().prec = 256

class RerankingStats:
    def __init__(self):
        self.rounds = 0
        self.path_changes_per_round = []
        self.update_times_per_round = []
        self.sort_times_per_round = []
        self.total_update_time = 0
        self.total_sort_time = 0
    
    def report(self, log_func):
        """Report statistics to the provided log function"""
        log_func("\n" + "="*80)
        log_func("RERANKING STATISTICS")
        log_func("="*80)
        log_func(f"Total reranking rounds: {self.rounds}")
        
        if self.rounds > 0:
            log_func(f"\nPath changes per round:")
            for i, changes in enumerate(self.path_changes_per_round):
                log_func(f"  Round {i+1}: {changes} paths changed")
            
            log_func(f"\nTime spent updating weights/capacities:")
            for i, t in enumerate(self.update_times_per_round):
                log_func(f"  Round {i+1}: {t:.6f} seconds")
            log_func(f"  Total: {self.total_update_time:.6f} seconds")
            
            log_func(f"\nTime spent sorting paths:")
            for i, t in enumerate(self.sort_times_per_round):
                log_func(f"  Round {i+1}: {t:.6f} seconds")
            log_func(f"  Total: {self.total_sort_time:.6f} seconds")
        
        log_func("="*80)

def read_path_enumeration_results(file_path):
    """Extract paths from non-disjoint path file."""
    with open(file_path, 'r') as f:
        content = f.read()
    
    paths = []
    path_blocks = re.findall(r"Path \d+:\s*([\d\s]+)", content)
    
    for path in path_blocks:
        paths.append(path.strip())
    
    return {'paths': paths}

def process_and_save_paths(
    path_file: str,
    node_map_file: str,
    output_dir: str = "results/path_analysis_df",
    name: str = None,
    is_disjoint: bool = False,
    num_paths: int = None
) -> pd.DataFrame:
    """Process paths from a path file, analyze them, and save the results."""
    os.makedirs('results/log', exist_ok=True)
    process_log_path = f"results/log/process_paths_{os.path.basename(path_file)}.txt"
    process_log = open(process_log_path, "w", encoding="utf-8")
    
    def log_process(message):
        process_log.write(f"{message}\n")
    
    if name is None:
        name = os.path.basename(path_file).split('.')[0]
    
    # Read node map
    with open(node_map_file, 'r') as f:
        node_map = {}
        for line in f:
            parts = line.strip().split()
            if len(parts) >= 2:
                index, token = parts[0], parts[1]
                node_map[int(index)] = token
    
    # Extract paths
    paths_data = read_path_enumeration_results(path_file)
    paths = paths_data['paths']
    
    if num_paths is not None:
        paths = paths[:num_paths]
    
    # Prepare data for DataFrame
    data = []
    
    for i, path_str in enumerate(paths):
        token_path = [node_map[int(node_id)] for node_id in path_str.split()]
        
        clean_path = []
        for token in token_path:
            base_token = token.split('_')[0] if '_' in token else token
            if base_token not in clean_path:
                clean_path.append(base_token)
        
        data.append({
            'path_id': i + 1,
            'path': ' -> '.join(clean_path),
            'token_sequence': clean_path
        })
    
    df = pd.DataFrame(data)
    os.makedirs(output_dir, exist_ok=True)
    output_file = f'{output_dir}/path_analysis_{name}.csv'
    df.to_csv(output_file, index=False)
    
    log_process(f"Path analysis saved to {output_file}")
    log_process(f"Total paths analyzed: {len(df)}")
    process_log.close()
    return df

def load_node_mappings(node_name_file, node_mapping_file):
    """Load both node name and node mapping information into dictionaries."""
    os.makedirs('results/log', exist_ok=True)
    
    # Load symbol to address mapping
    node_name = {}
    with open(node_name_file, 'r') as file:
        for line in file:
            line = line.strip()
            if line:
                parts = line.split()
                symbol = parts[0]
                address = parts[1]
                node_name[symbol] = address
    
    # Load address to node ID mapping
    node_index = {}
    with open(node_mapping_file, 'r') as file:
        for line in file:
            line = line.strip()
            if line:
                parts = line.split()
                node_id = parts[0]
                address = parts[1]
                node_index[address] = node_id
    
    # Create node ID to symbol mapping
    index_name = {}
    for name, addr in node_name.items():
        try:
            index_name[int(node_index[addr])] = name
        except (KeyError, ValueError):
            pass
    
    return node_name, node_index, index_name

def load_token_prices(price_file_path):
    """Load token prices from the quotes file"""
    try:
        with open(price_file_path, 'r', encoding='utf-8') as file:
            price_data = json.load(file)
        
        token_prices = {}
        for token in price_data:
            address = token['address'].lower()
            try:
                price = float(token['quotes']['avg_quote'])
            except (KeyError, ValueError, TypeError):
                price = 0
            token_prices[address] = price
        
        return token_prices
    except Exception as e:
        print(f"Error loading token prices: {e}")
        return {}

def load_pool_capacities(capacity_file_path):
    """Load pool capacities from a JSON file."""
    try:
        with open(capacity_file_path, 'r', ending='utf-8') as f:
            capacities = json.load(f)
        return capacities
    except Exception as e:
        return {}

def load_pool_weights(weights_file_path):
    """Load pool weights from a JSON file."""
    try:
        with open(weights_file_path, 'r') as f:
            weights = json.load(f)
        return weights
    except Exception as e:
        return {}

def load_pool_data(weights_file_path=None, capacities_file_path=None):
    """Load pool weights and capacities from JSON files."""
    pool_weights = {}
    pool_capacities = {}
    
    if weights_file_path and os.path.exists(weights_file_path):
        try:
            with open(weights_file_path, 'r', encoding='utf-8') as f:
                pool_weights = json.load(f)
        except Exception as e:
            pass
    
    if capacities_file_path and os.path.exists(capacities_file_path):
        try:
            with open(capacities_file_path, 'r', encoding='utf-8') as f:
                pool_capacities = json.load(f)
        except Exception as e:
            pass
    
    return pool_weights, pool_capacities

def calculate_path_weight_and_capacity(tokens, pools, pool_weights, pool_capacities):
    """Calculate the weight and capacity of a path."""
    total_weight = 0
    min_capacity = float('inf')
    selected_edges = []
    
    is_pools_list_of_lists = False
    if pools and isinstance(pools[0], list):
        is_pools_list_of_lists = True
    
    for i in range(len(tokens) - 1):
        token_in = tokens[i].lower()
        token_out = tokens[i+1].lower()
        
        candidate_pools = []
        if is_pools_list_of_lists:
            if i < len(pools):
                candidate_pools = pools[i]
        else:
            if i < len(pools):
                candidate_pools = [pools[i].lower()]
        
        if not candidate_pools:
            total_weight = float('inf')
            min_capacity = 0
            break
        
        best_weight, best_capacity, best_pool, best_tick, best_pool_type = find_best_pool_for_edge(
            token_in, token_out, candidate_pools, pool_weights, pool_capacities
        )
        
        if best_weight != float('inf'):
            total_weight += best_weight
        
        if best_capacity < min_capacity:
            min_capacity = best_capacity
        
        edge_info = {
            'pool': best_pool if best_pool else (candidate_pools[0] if candidate_pools else None),
            'tick': best_tick,
            'weight': best_weight,
            'capacity': best_capacity,
            'pool_type': best_pool_type
        }
        selected_edges.append(edge_info)
    
    if min_capacity == float('inf'):
        min_capacity = 0
    
    return total_weight, min_capacity, selected_edges

def find_best_pool_for_edge(token_in, token_out, candidate_pools, pool_weights, pool_capacities):
    """Find the best pool and tick for a given edge (token pair)."""
    best_weight = float('inf')
    best_capacity = 0
    best_pool = None
    best_tick = None
    best_pool_type = None
    
    token_pair_key = f"{token_in.lower()}_{token_out.lower()}"
    
    if token_pair_key not in pool_weights:
        return best_weight, best_capacity, best_pool, best_tick, best_pool_type
    
    all_tick_options = []
    
    for pool_address in candidate_pools:
        pool_address = pool_address.lower()
        
        if pool_address not in pool_weights[token_pair_key]:
            continue
        
        weight_data_list = pool_weights[token_pair_key][pool_address]
        
        for tick_data in weight_data_list:
            tick = tick_data.get('tick')
            weight = tick_data.get('weight', float('inf'))
            pool_type = tick_data.get('pool_type', 'v3')
            
            capacity = 0
            if token_pair_key in pool_capacities and pool_address in pool_capacities[token_pair_key]:
                for cap_data in pool_capacities[token_pair_key][pool_address]:
                    if cap_data.get('tick') == tick:
                        capacity = cap_data.get('capacity', 0)
                        break
            
            all_tick_options.append({
                'pool': pool_address,
                'tick': tick,
                'weight': weight,
                'capacity': capacity,
                'pool_type': pool_type
            })
    
    all_tick_options.sort(key=lambda x: x['weight'])
    
    for option in all_tick_options:
        if option['capacity'] > 0:
            best_weight = option['weight']
            best_capacity = option['capacity']
            best_pool = option['pool']
            best_tick = option['tick']
            best_pool_type = option['pool_type']
            break
    
    if best_pool is None and all_tick_options:
        option = all_tick_options[0]
        best_weight = option['weight']
        best_capacity = 0
        best_pool = option['pool']
        best_tick = option['tick']
        best_pool_type = option['pool_type']
    
    return best_weight, best_capacity, best_pool, best_tick, best_pool_type

def get_token_pair_key(token_a, token_b):
    """Create a consistent key for a token pair."""
    token_a = token_a.lower().strip()
    token_b = token_b.lower().strip()
    
    if hasattr(get_token_pair_key, 'token_lookup'):
        token_a = get_token_pair_key.token_lookup.get(token_a, token_a)
        token_b = get_token_pair_key.token_lookup.get(token_b, token_b)
        
    return f"{token_a}_{token_b}"

def initialize_token_lookup(token_mapping):
    """Initialize a token symbol to address lookup table."""
    get_token_pair_key.token_lookup = token_mapping 

def calculate_proportions_bps(
    from_token: str, 
    paths: list, 
    path_tokens: list,
    price_dict: dict, 
    balance_dict: dict,
    pool_weights: dict,
    token_decimals_dict: dict,
    original_amount: int,
    lambda_threshold: int = 0
):
    """Calculate the proportions for each path in basis points (BPS) using dynamic path re-ranking."""
    allocation_log_path = f"results/log/allocation_log_{from_token[0:4]}_{original_amount}.txt"
    allocation_log = open(allocation_log_path, "w", encoding="utf-8")
    
    def log_msg(message):
        allocation_log.write(message + "\n")
    
    rerank_stats = RerankingStats()
    
    log_msg(f"Starting allocation process for {from_token} with amount {original_amount}")
    
    if from_token not in price_dict:
        log_msg(f"Warning: Could not find price for input token or no valid paths")
        log_msg(f"  price dict: {price_dict}")
        allocation_log.close()
        return [], original_amount, {}, {}

    decimals = token_decimals_dict.get(from_token, 6)
    input_amount = original_amount / (10 ** decimals) * price_dict[from_token]
    log_msg(f"Converting {original_amount} tokens to {input_amount} ETH equivalent")
    
    proportions_bps = [0] * len(paths)
    total_remaining = input_amount
    
    working_weights = copy.deepcopy(pool_weights)
    working_capacities = copy.deepcopy(balance_dict)
    
    edge_exhausted = {}
    
    path_weights = []
    path_capacities = []
    path_edges = []
    
    # Calculate initial path weights and capacities
    for i, pools in enumerate(paths):
        tokens = path_tokens[i]
        path_weight, path_capacity, selected_edges = calculate_path_weight_and_capacity(
            tokens, pools, working_weights, working_capacities
        )
        path_weights.append(path_weight)
        path_capacities.append(path_capacity)
        path_edges.append(selected_edges)
    
    log_msg("\n===== INITIAL PATH WEIGHTS AND RANKING =====")
    path_data = [(i, path_weights[i], path_capacities[i]) for i in range(len(paths))]
    path_data.sort(key=lambda x: x[1])
    for rank, (idx, weight, capacity) in enumerate(path_data[:5]):
        log_msg(f"Rank {rank+1}: Path {idx+1} - Weight: {weight}, Capacity: {capacity}")
    log_msg("============================================\n")
    
    allocated_paths = []
    exhausted_paths = set()
    pool_allocations = {}
    pool_proportion_bps = {}
    path_pool_allocations = {}
    
    allocation_round = 0
    
    while total_remaining > 0 and len(exhausted_paths) < len(paths):
        if allocation_round > 0:
            log_msg("\n----- RECALCULATING PATH WEIGHTS AND CAPACITIES -----")
            rerank_stats.rounds += 1
            path_changes_count = 0
            
            old_path_data = {i: (path_weights[i], path_capacities[i]) if i < len(path_weights) else (float('inf'), 0) 
                             for i in range(len(paths)) if i not in exhausted_paths}
            
            path_weights = []
            path_capacities = []
            path_edges = []
            
            update_start_time = time.time()
            
            for i, pools in enumerate(paths):
                if i in exhausted_paths:
                    path_weights.append(float('inf'))
                    path_capacities.append(0)
                    path_edges.append([])
                    continue
                
                path_exhausted = False
                for edge_idx in range(len(path_tokens[i]) - 1):
                    if edge_exhausted.get((i, edge_idx), False):
                        path_exhausted = True
                        break
                
                if path_exhausted:
                    exhausted_paths.add(i)
                    path_weights.append(float('inf'))
                    path_capacities.append(0)
                    path_edges.append([])
                    log_msg(f"Path {i+1} is exhausted (one or more edges have no available pools)")
                    continue
                    
                tokens = path_tokens[i]
                
                all_pools_for_path = []
                for edge_idx in range(len(tokens) - 1):
                    token_in = tokens[edge_idx].lower()
                    token_out = tokens[edge_idx + 1].lower()
                    token_pair_key = f"{token_in}_{token_out}"
                    
                    available_pools = []
                    if token_pair_key in working_weights:
                        for pool_addr in working_weights[token_pair_key]:
                            has_capacity = False
                            if pool_addr in working_capacities.get(token_pair_key, {}):
                                for capacity_entry in working_capacities[token_pair_key][pool_addr]:
                                    if capacity_entry.get('capacity', 0) > 0:
                                        has_capacity = True
                                        break
                            
                            if has_capacity:
                                available_pools.append(pool_addr)
                    
                    if not available_pools:
                        edge_exhausted[(i, edge_idx)] = True
                        path_exhausted = True
                        log_msg(f"Edge {edge_idx+1} of path {i+1} ({token_in}->{token_out}) is completely exhausted")
                    else:
                        all_pools_for_path.append(available_pools)
                
                if path_exhausted:
                    exhausted_paths.add(i)
                    path_weights.append(float('inf'))
                    path_capacities.append(0)
                    path_edges.append([])
                    log_msg(f"Path {i+1} is exhausted (one or more edges have no available pools)")
                    continue
                
                path_weight, path_capacity, selected_edges = calculate_path_weight_and_capacity(
                    tokens, all_pools_for_path, working_weights, working_capacities
                )
                
                if i in old_path_data:
                    old_weight, old_capacity = old_path_data[i]
                    weight_change = path_weight - old_weight
                    capacity_change = path_capacity - old_capacity
                    
                    weight_changed = abs(weight_change) > 1e-10
                    capacity_changed = abs(capacity_change) > 1e-10
                    
                    old_edges = path_edges[i] if i < len(path_edges) else []
                    pool_tick_changes = []
                    
                    for j, edge in enumerate(selected_edges):
                        if j < len(old_edges):
                            old_edge = old_edges[j]
                            if edge['pool'] != old_edge['pool'] or edge['tick'] != old_edge['tick']:
                                pool_tick_changes.append((j, old_edge['pool'], old_edge['tick'], edge['pool'], edge['tick']))
                    
                    if weight_changed or capacity_changed or pool_tick_changes:
                        path_changes_count += 1
                    
                    log_msg(f"Path {i+1} updates:")
                    log_msg(f"  Weight: {old_weight} -> {path_weight} (Change: {weight_change:+.6f})")
                    log_msg(f"  Capacity: {old_capacity} -> {path_capacity} (Change: {capacity_change:+.6f})")
                    
                    if pool_tick_changes:
                        log_msg(f"  POOL/TICK CHANGES DETECTED:")
                        for edge_idx, old_pool, old_tick, new_pool, new_tick in pool_tick_changes:
                            log_msg(f"    Edge {edge_idx+1}: Pool {old_pool}@Tick {old_tick} -> Pool {new_pool}@Tick {new_tick}")
                
                path_weights.append(path_weight)
                path_capacities.append(path_capacity)
                path_edges.append(selected_edges)
                
                if path_capacity <= 0:
                    exhausted_paths.add(i)
                    log_msg(f"  Path {i+1} now exhausted (zero capacity)")
            
            update_end_time = time.time()
            update_time = update_end_time - update_start_time
            rerank_stats.update_times_per_round.append(update_time)
            rerank_stats.total_update_time += update_time
            
            sort_start_time = time.time()
            
            path_data = [(i, path_weights[i], path_capacities[i]) for i in range(len(paths)) if i not in exhausted_paths]
            path_data.sort(key=lambda x: x[1])
            
            sort_end_time = time.time()
            sort_time = sort_end_time - sort_start_time
            rerank_stats.sort_times_per_round.append(sort_time)
            rerank_stats.total_sort_time += sort_time
            
            rerank_stats.path_changes_per_round.append(path_changes_count)
            
            log_msg("\n----- UPDATED PATH RANKING -----")
            log_msg(f"Reranking round {rerank_stats.rounds}: {path_changes_count} paths changed")
            log_msg(f"Update time: {update_time:.6f}s, Sort time: {sort_time:.6f}s")
            for rank, (idx, weight, capacity) in enumerate(path_data[:5]):
                log_msg(f"Rank {rank+1}: Path {idx+1} - Weight: {weight}, Capacity: {capacity}")
            log_msg("--------------------------------")
        
        if not path_data:
            log_msg("No more paths with capacity available")
            break
            
        allocation_round += 1
        log_msg(f"\n===== ALLOCATION ROUND {allocation_round} =====")
        
        idx, weight, capacity = path_data[0]
        
        if capacity <= 0:
            log_msg(f"Path {idx+1} has zero capacity, marking as exhausted")
            exhausted_paths.add(idx)
            continue
        
        amount_to_allocate = min(capacity, total_remaining)
        
        if amount_to_allocate <= 0:
            log_msg(f"Path {idx+1} has no capacity to allocate")
            exhausted_paths.add(idx)
            continue
        
        proportion_bps = int((amount_to_allocate * 10000) // input_amount)
        proportions_bps[idx] += proportion_bps
        
        if idx not in allocated_paths:
            allocated_paths.append(idx)
        
        total_remaining -= amount_to_allocate
        
        log_msg(f"Selected Path {idx+1}")
        log_msg(f"Path: {' -> '.join(path_tokens[idx])}")
        log_msg(f"Weight: {weight}, Capacity: {capacity}")
        log_msg(f"Allocated amount: {amount_to_allocate}")
        log_msg(f"Proportion: {proportion_bps/100:.2f}% ({proportion_bps} bps)")
        log_msg(f"Remaining: {total_remaining}")
        
        selected_edges = path_edges[idx]
        
        log_msg(f"\nUpdating capacities for path {idx+1}:")
        for edge_idx, edge_data in enumerate(selected_edges):
            token_in = path_tokens[idx][edge_idx].lower()
            token_out = path_tokens[idx][edge_idx + 1].lower()
            pool_address = edge_data['pool']
            tick = edge_data['tick']
            pool_type = edge_data.get('pool_type', 'v3')
            
            log_msg(f"  Edge {edge_idx+1}: {token_in} -> {token_out}, Pool: {pool_address}")
            log_msg(f"  Tick: {tick}, Pool Type: {pool_type}, Weight: {edge_data['weight']}, Capacity before: {edge_data['capacity']}")
            
            pool_key = (idx, edge_idx, pool_address, tick)
            if pool_key not in pool_allocations:
                pool_allocations[pool_key] = 0
            pool_allocations[pool_key] += amount_to_allocate
            
            if idx not in path_pool_allocations:
                path_pool_allocations[idx] = {}
            if edge_idx not in path_pool_allocations[idx]:
                path_pool_allocations[idx][edge_idx] = []
            
            existing_from_to = None
            pool_identifier = pool_address.lower()
            
            if pool_identifier in pool_proportion_bps:
                found = False
                for key in pool_proportion_bps:
                    if isinstance(key, str) and key.startswith(f"{pool_identifier}_"):
                        parts = key.split('_', 3)
                        if len(parts) >= 3:
                            existing_from = parts[1]
                            existing_to = parts[2]
                            if existing_from != token_in or existing_to != token_out:
                                existing_from_to = (existing_from, existing_to)
                                found = True
                                break
                
                if found and existing_from_to:
                    pool_identifier = f"{pool_address}_{token_in}_{token_out}"
                    
                    if pool_address in pool_proportion_bps:
                        existing_key = f"{pool_address}_{existing_from_to[0]}_{existing_from_to[1]}"
                        pool_proportion_bps[existing_key] = pool_proportion_bps[pool_address]
                        del pool_proportion_bps[pool_address]
            
            pool_bps = int((amount_to_allocate * 10000) // input_amount)
            
            if pool_identifier not in pool_proportion_bps:
                pool_proportion_bps[pool_identifier] = 0
            pool_proportion_bps[pool_identifier] += pool_bps
            
            path_pool_allocations[idx][edge_idx].append((
                pool_address, 
                tick, 
                amount_to_allocate,
                edge_data['capacity'],
                pool_type,
                pool_bps
            ))
            
            token_pair_key = f"{token_in}_{token_out}"
            if token_pair_key in working_capacities and pool_address in working_capacities[token_pair_key]:
                tick_updated = False
                for capacity_entry in working_capacities[token_pair_key][pool_address]:
                    if capacity_entry.get('tick') == tick:
                        old_capacity = capacity_entry.get('capacity', 0)
                        new_capacity = max(0, old_capacity - amount_to_allocate)
                        capacity_entry['capacity'] = new_capacity
                        log_msg(f"  Updated capacity: {old_capacity} -> {new_capacity}")
                        
                        if new_capacity <= 0:
                            log_msg(f"  TICK EXHAUSTED: Pool {pool_address}@Tick {tick} now has zero capacity")
                        
                        tick_updated = True
                        break
                
                if not tick_updated:
                    log_msg(f"  WARNING: Could not find tick {tick} for pool {pool_address}")
                
                all_ticks_exhausted = True
                for capacity_entry in working_capacities[token_pair_key][pool_address]:
                    if capacity_entry.get('capacity', 0) > 0:
                        all_ticks_exhausted = False
                        break
                
                if all_ticks_exhausted:
                    log_msg(f"  Pool {pool_address} has all ticks exhausted for {token_in}->{token_out}")
                
                all_pools_exhausted = True
                for pool_addr, cap_entries in working_capacities[token_pair_key].items():
                    for cap_entry in cap_entries:
                        if cap_entry.get('capacity', 0) > 0:
                            all_pools_exhausted = False
                            log_msg(f"  Still available: Pool {pool_addr} for {token_in}->{token_out}")
                            break
                    if not all_pools_exhausted:
                        break
                
                if all_pools_exhausted:
                    edge_exhausted[(idx, edge_idx)] = True
                    log_msg(f"  Edge {edge_idx+1} ({token_in}->{token_out}) is completely exhausted (all pools)")
        
        log_msg(f"===== END OF ROUND {allocation_round} =====")
        
        if total_remaining < lambda_threshold:
            log_msg(f"Remaining amount {total_remaining} is below threshold {lambda_threshold}")
            break
    
    log_msg("\n===== ALLOCATION COMPLETED =====")
    log_msg(f"Allocated to {len(allocated_paths)} paths: {[p+1 for p in allocated_paths]}")
    log_msg(f"Total allocation: {input_amount - total_remaining} ({(input_amount - total_remaining) / input_amount * 100:.2f}%)")
    
    remaining_amount_original = total_remaining / price_dict[from_token] * (10 ** decimals)
    log_msg(f"Remaining amount (original units): {remaining_amount_original}")
    
    path_input_amounts = {}
    for i, proportion_bps in enumerate(proportions_bps):
        path_input_amounts[i] = (original_amount * proportion_bps) // 10000
    
    log_msg("\nPath Input Amounts:")
    for path_idx, amount in path_input_amounts.items():
        if amount > 0:
            log_msg(f"Path {path_idx+1}: {amount} ({proportions_bps[path_idx]/100:.2f}%)")
    
    log_msg("\nPool Proportions (direct calculation):")
    for pool_addr, bps in sorted(pool_proportion_bps.items(), key=lambda x: -x[1])[:20]:
        if '_' in pool_addr and pool_addr.count('_') >= 2:
            parts = pool_addr.split('_', 3)
            original_pool = parts[0]
            token_in = parts[1]
            token_out = parts[2]
            log_msg(f"Pool {original_pool} ({token_in}->{token_out}): {bps/100:.2f}% ({bps} bps)")
        else:
            log_msg(f"Pool {pool_addr}: {bps/100:.2f}% ({bps} bps)")
    
    for path_idx in path_pool_allocations:
        for edge_idx in path_pool_allocations[path_idx]:
            pool_groups = {}
            for pool_addr, tick, amount, capacity, p_type, pool_bps in path_pool_allocations[path_idx][edge_idx]:
                if pool_addr not in pool_groups:
                    pool_groups[pool_addr] = {
                        "tick": tick,
                        "pool_bps": 0,
                        "pool_type": p_type
                    }
                pool_groups[pool_addr]["pool_bps"] += pool_bps
            
            consolidated_entries = []
            for pool_addr, data in pool_groups.items():
                consolidated_entries.append((
                    pool_addr,
                    data["tick"],
                    data["pool_type"],
                    data["pool_bps"] / 10000.0
                ))
            
            path_pool_allocations[path_idx][edge_idx] = consolidated_entries
    
    log_msg("\nDetailed Pool Allocations (by path and edge):")
    for path_idx in sorted(path_pool_allocations.keys()):
        path_input = path_input_amounts.get(path_idx, 0)
        if path_input <= 0:
            continue
            
        log_msg(f"\nPath {path_idx+1} (Input: {path_input}):")
        for edge_idx, pools_data in sorted(path_pool_allocations[path_idx].items()):
            token_in = path_tokens[path_idx][edge_idx].lower()
            token_out = path_tokens[path_idx][edge_idx+1].lower()
            log_msg(f"  Edge {edge_idx+1} ({token_in} -> {token_out}):")
            
            for pool_addr, tick, pool_type, proportion in pools_data:
                actual_amount = int(path_input * proportion)
                log_msg(f"    Pool: {pool_addr}, Tick: {tick}, Type: {pool_type}")
                log_msg(f"    Allocated: {actual_amount} tokens ({proportion*100:.2f}% of path input)")
    
    log_msg(f"\nAllocation log saved to: {allocation_log_path}")
    allocation_log.close()
    
    calculate_proportions_bps.last_rerank_stats = rerank_stats
    
    return proportions_bps, remaining_amount_original, path_pool_allocations, pool_proportion_bps 

def generate_path_allocation_json(
    path_file: str,
    input_amount: int,
    name: str,
    balance_dict: dict,
    price_dict: dict,
    token_decimals_dict: dict,
    weights_file: str = None,
    capacities_file: str = None,
    lambda_threshold: float = 0
) -> dict:
    """
    Generate path allocation percentages in JSON format without simulation.
    
    Args:
        path_file: Path to the CSV file with path analysis
        input_amount: Input amount for allocation
        name: Name for output files
        balance_dict: Dictionary of pool balances
        price_dict: Dictionary of token prices
        token_decimals_dict: Dictionary of token decimals
        weights_file: Path to the weights JSON file (optional)
        capacities_file: Path to the capacities JSON file (optional)
        lambda_threshold: Threshold for allocation (as a fraction of input)
    
    Returns:
        Dictionary with allocation information in JSON format
    """
    # Create output directories
    os.makedirs('results', exist_ok=True)
    os.makedirs('results/path_allocation_json', exist_ok=True)
    os.makedirs('results/log', exist_ok=True)
    
    # Create allocation log file
    alloc_log_path = f"results/path_allocation_json/path_allocation_{name}_{input_amount}.txt"
    log_file = open(alloc_log_path, "w", encoding="utf-8")
    
    def log_alloc(message):
        log_file.write(message + "\n")
    
    log_alloc(f"Starting path allocation generation for {name}")
    log_alloc(f"Input amount: {input_amount}")
    
    # Load path analysis data
    log_alloc(f"Loading path analysis from {path_file}...")
    df = pd.read_csv(path_file)
    
    if 'path' not in df.columns:
        log_alloc(f"Error: 'path' column missing from {path_file}")
        log_file.close()
        return {"error": "path column missing"}
    
    input_token = df['path'].values[0].split(' -> ')[0]
    
    # Load pool weights and capacities if provided
    pool_weights = {}
    pool_capacities = {}
    
    if weights_file and os.path.exists(weights_file):
        log_alloc(f"Loading weights from {weights_file}...")
        try:
            pool_weights = load_pool_weights(weights_file)
            log_alloc(f"Loaded weights for {len(pool_weights)} token pairs")
        except Exception as e:
            log_alloc(f"Error loading weights: {str(e)}")
    else:
        log_alloc("No weights file provided or file not found, using default balance_dict")
    
    if capacities_file and os.path.exists(capacities_file):
        log_alloc(f"Loading capacities from {capacities_file}...")
        try:
            pool_capacities = load_pool_capacities(capacities_file)
            log_alloc(f"Loaded capacities for {len(pool_capacities)} token pairs")
        except Exception as e:
            log_alloc(f"Error loading capacities: {str(e)}")
    else:
        log_alloc("No capacities file provided or file not found, using default balance_dict")
    
    # Extract token paths from the dataset
    paths_tokens = []
    for i, row in df.iterrows():
        path_tokens = row['path'].split(' -> ')
        paths_tokens.append(path_tokens)
        
    # Generate pool addresses for paths
    log_alloc("Generating pool addresses for paths...")
    token_pair_to_pools = {}
    
    # Build the lookup from weight/capacity data
    if pool_weights:
        for pair_key, pools_data in pool_weights.items():
            token_a, token_b = pair_key.split('_')
            token_pair = (token_a.lower(), token_b.lower())
            
            if token_pair not in token_pair_to_pools:
                token_pair_to_pools[token_pair] = []
                
            for pool_addr in pools_data.keys():
                if pool_addr not in token_pair_to_pools[token_pair]:
                    token_pair_to_pools[token_pair].append(pool_addr)
    
    # Assign pool addresses to each path
    paths_pools = []
    for path_tokens in paths_tokens:
        pools = []
        for i in range(len(path_tokens) - 1):
            token_a = path_tokens[i].lower()
            token_b = path_tokens[i + 1].lower()
            
            pair_key = (token_a, token_b)
            found_pool = False
            
            if pair_key in token_pair_to_pools and token_pair_to_pools[pair_key]:
                best_pool = None
                best_weight = float('inf')
                
                for pool_addr in token_pair_to_pools[pair_key]:
                    pair_key_str = f"{token_a}_{token_b}"
                    reversed_pair_key = f"{token_b}_{token_a}"
                    
                    if (pair_key_str in pool_weights and pool_addr in pool_weights[pair_key_str]) or \
                       (reversed_pair_key in pool_weights and pool_addr in pool_weights[reversed_pair_key]):
                        found_pool = True
                        
                        pool_weight = float('inf')
                        if pair_key_str in pool_weights and pool_addr in pool_weights[pair_key_str]:
                            for tick_data in pool_weights[pair_key_str][pool_addr]:
                                weight = tick_data.get('weight', float('inf'))
                                if weight < pool_weight:
                                    pool_weight = weight
                        elif reversed_pair_key in pool_weights and pool_addr in pool_weights[reversed_pair_key]:
                            for tick_data in pool_weights[reversed_pair_key][pool_addr]:
                                weight = tick_data.get('weight', -float('inf'))
                                if -weight < pool_weight:
                                    pool_weight = -weight
                        
                        if pool_weight < best_weight:
                            best_weight = pool_weight
                            best_pool = pool_addr
                
                if found_pool and best_pool:
                    pools.append(best_pool)
                else:
                    pools.append(token_pair_to_pools[pair_key][0])
                    found_pool = True
            
            if not found_pool:
                log_alloc(f"Warning: No pool found for {token_a} -> {token_b}")
                placeholder = f"0x{token_a[2:10]}{token_b[2:10]}"
                pools.append(placeholder)
        
        paths_pools.append(pools)
    
    # Update the DataFrame with the generated pool addresses
    df['involved_pool_addresses'] = paths_pools
    
    # Determine pool types
    def determine_pool_types(pools):
        pool_types = []
        for pool_address in pools:
            pool_address = pool_address.lower()
            pool_type = 'v3'
            
            for pair_key in pool_weights:
                if pool_address in pool_weights[pair_key]:
                    for tick_data in pool_weights[pair_key][pool_address]:
                        if 'pool_type' in tick_data:
                            pool_type = tick_data['pool_type']
                            break
                    if pool_type != 'v3':
                        break
            
            pool_types.append(pool_type)
        return pool_types
    
    df['pools_type_list'] = df['involved_pool_addresses'].apply(determine_pool_types)
    
    log_alloc(f"Generated pool addresses for {len(paths_pools)} paths")
    
    # Measure execution time of calculate_proportions_bps
    log_alloc("\nStarting path allocation algorithm...")
    start_time = time.time()
    
    # Calculate lambda threshold in absolute terms
    absolute_lambda_threshold = int(lambda_threshold * input_amount)
    log_alloc(f"Using lambda threshold: {lambda_threshold} ({absolute_lambda_threshold} tokens)")
    
    try:
        # Call the path allocation function
        proportions_bps, remaining_amount, path_pool_allocations, pool_proportion_bps = calculate_proportions_bps(
            input_token, 
            df['involved_pool_addresses'].tolist(),
            paths_tokens,
            price_dict, 
            pool_capacities if pool_capacities else balance_dict,
            pool_weights if pool_weights else {},
            token_decimals_dict,
            input_amount,
            absolute_lambda_threshold
        )
    except Exception as e:
        log_alloc(f"Error during path allocation: {str(e)}")
        import traceback
        log_alloc(traceback.format_exc())
        log_file.close()
        return {"error": str(e)}
    
    end_time = time.time()
    execution_time = end_time - start_time
    log_alloc(f"Path allocation completed in {execution_time:.4f} seconds")
    log_alloc(f"Number of paths processed: {len(paths_tokens)}")
    
    # Add proportions to DataFrame
    df['proportion_bps'] = proportions_bps
    
    # Calculate input amount for each path
    df['path_input_amount'] = df['proportion_bps'].apply(
        lambda x: (input_amount * x) // 10000
    )
    
    # Prepare result structure
    result = {
        "fromAmount": str(input_amount),
        "from": "",
        "to": "",
        "route": {
            "fills": [],
        },
        'remaining_amount': remaining_amount,
        'allocation_metadata': {
            'total_paths': len(paths_tokens),
            'allocated_paths': len([p for p in proportions_bps if p > 0]),
            'execution_time_seconds': execution_time,
            'lambda_threshold': lambda_threshold
        }
    }
    
    # Set source and destination tokens
    if not df.empty and 'path' in df.columns:
        first_path = df.iloc[0]['path']
        if ' -> ' in first_path:
            tokens = first_path.split(' -> ')
            result["from"] = tokens[0].lower()
            result["to"] = tokens[-1].lower()
    
    # Use the directly calculated pool proportions to create fills
    for pool_key, bps in pool_proportion_bps.items():
        if bps <= 0:
            continue
        
        # Check if this is a multi-key entry
        if '_' in pool_key and pool_key.count('_') >= 2:
            parts = pool_key.split('_', 3)
            pool_address = parts[0]
            token_in = parts[1]
            token_out = parts[2]
            
            # Determine pool type
            pool_type = "v3"
            token_pair_key = f"{token_in}_{token_out}"
            
            if token_pair_key in pool_weights and pool_address in pool_weights[token_pair_key]:
                for tick_data in pool_weights[token_pair_key][pool_address]:
                    if 'pool_type' in tick_data:
                        pool_type = tick_data['pool_type']
                        break
            
            fill = {
                "from": token_in,
                "to": token_out,
                "pool": pool_address,
                "source": "Uniswap_V" + pool_type[-1],
                "poolTotalProportionBps": str(bps)
            }
            
            result["route"]["fills"].append(fill)
        else:
            # Regular pool_address entry
            pool_address = pool_key
            
            # Find token pair and other details for this pool
            found = False
            for path_idx, edge_data in path_pool_allocations.items():
                for edge_idx, pools_data in edge_data.items():
                    for pool_addr, tick, pool_type, proportion in pools_data:
                        if pool_addr.lower() == pool_address.lower():
                            token_in = paths_tokens[path_idx][edge_idx].lower()
                            token_out = paths_tokens[path_idx][edge_idx + 1].lower()
                            
                            fill = {
                                "from": token_in,
                                "to": token_out,
                                "pool": pool_address,
                                "source": "Uniswap_V" + pool_type[-1],
                                "poolTotalProportionBps": str(bps)
                            }
                            
                            result["route"]["fills"].append(fill)
                            found = True
                            break
                    if found:
                        break
                if found:
                    break
    
    # Add reranking statistics to the result
    if hasattr(calculate_proportions_bps, 'last_rerank_stats'):
        stats = calculate_proportions_bps.last_rerank_stats
        result['reranking_stats'] = {
            'total_rounds': stats.rounds,
            'total_update_time': stats.total_update_time,
            'total_sort_time': stats.total_sort_time
        }
    
    # Save result to JSON file
    result_file = f"results/path_allocation_json/allocation_{name}_{input_amount}.json"
    with open(result_file, "w") as f:
        json.dump(result, f, indent=2)
    
    log_alloc(f"Path allocation result saved to {result_file}")
    log_alloc(f"Total fills generated: {len(result['route']['fills'])}")
    
    # Close the log file
    log_alloc(f"Allocation log saved to: {alloc_log_path}")
    log_file.close()
    
    return result

def main():
    parser = argparse.ArgumentParser(description='Generate path allocation percentages')
    parser.add_argument('--pairs', type=str, default="usdt-frax,usdt-ohm,usdt-cbbtc,reth-wsteth,spx-peas,wbtc-spx,wbtc-usdt", 
                        help='Comma-separated list of source-target pairs')
    parser.add_argument('--max_length', type=int, default=3, help='Maximum path length')
    parser.add_argument('--split-ticks', action='store_true', 
                       help='Enable V3 tick splitting for large price ranges')
    parser.add_argument('--allocation_threshold', type=float, default=0, 
                        help='Allocation threshold (as a fraction of input amount)')
    parser.add_argument('--block', type=int, default=21974203,
                        help='block number')
    args = parser.parse_args()
    
    # Configuration
    block_number = args.block
    graph_name = 'combined'
    k = 10000
    max_length = args.max_length
    lambda_threshold = args.allocation_threshold
    
    node_name_file = f"../data/node_name.txt"
    node_mapping_file = f"graphs/{block_number}_{graph_name}_node_map.txt"
    node_name, node_index, index_name = load_node_mappings(node_name_file, node_mapping_file)
    
    # Parse pairs from command-line argument
    pairs = args.pairs.split(',')
    
    for pair in pairs:
        source_symbol, target_symbol = pair.split('-')
    
        source = node_index[node_name[source_symbol]]
        target = node_index[node_name[target_symbol]]

        names = [f'all_paths_{source}_{target}_{max_length}']

        if source_symbol == 'usdt':
            input_amounts = [200000000000]#, 1000000000000, 5000000000000]
        elif source_symbol == 'wbtc':
            input_amounts = [2000000000, 10000000000, 60000000000]
        elif source_symbol == 'reth':
            input_amounts = [20000000000000000000, 100000000000000000000, 500000000000000000000]
        elif source_symbol == 'spx':
            input_amounts = [40000000000000, 200000000000000, 1000000000000000]
        else:
            input_amounts = [1000000000000000000]

        print(f"\n{'=' * 80}")
        print(f"Processing pair: {source_symbol}-{target_symbol}")
        print(f"Max path length: {max_length}")
        print(f"Allocation threshold: {lambda_threshold}")
        print(f"Input amounts: {input_amounts}")
        print(f"{'=' * 80}\n")

        # Path to capacities and weights files
        if args.split_ticks:
            capacities_file = f'graphs/{block_number}_processed_pool_capacities.json'
            weights_file = f'graphs/{block_number}_processed_pool_weights.json'
        else:
            capacities_file = f'graphs/{block_number}_pool_capacities.json'
            weights_file = f'graphs/{block_number}_pool_weights.json'
        
        # Load token prices and decimals
        price_dict = load_token_prices(f'../data/prices/block-{block_number}/tokens-quotes.json')
        token_decimals_df = pd.read_csv(f'../data/token-decimals.csv')
        token_decimals_dict = {}
        for index, row in token_decimals_df.iterrows():
            token_decimals_dict[row['token_address']] = row['decimals']

        # Initialize token lookup
        token_lookup = {}
        for name, addr in node_name.items():
            token_lookup[name.lower()] = addr.lower()
        
        for _, addr in node_name.items():
            token_lookup[addr.lower()] = addr.lower()
        
        initialize_token_lookup(token_lookup)

        # Process paths first if needed
        for name in names:
            path_file = f'results/{name}.txt'
            node_map_file = f'graphs/{block_number}_{graph_name}_node_map.txt'
            output_dir = 'results/path_analysis_df'
            
            csv_path_file = f'{output_dir}/path_analysis_{name}.csv'
            if not os.path.exists(csv_path_file):
                process_and_save_paths(
                    path_file=path_file,
                    node_map_file=node_map_file,
                    output_dir=output_dir,
                    name=name,
                    is_disjoint=False,
                    num_paths=k
                )

        # Generate path allocations
        for name in names:   
            # Load pool balances as fallback
            balance_dict = {}
            try:
                balance_file = f'../data/prices/block-{block_number}/pool-balances.json'
                if os.path.exists(balance_file):
                    with open(balance_file, 'r', encoding='utf-8') as f:
                        balance_dict = json.load(f)
            except Exception as e:
                pass

            path_file = f'results/path_analysis_df/path_analysis_{name}.csv'
            
            for input_amount in input_amounts:
                # Generate path allocation JSON
                result = generate_path_allocation_json(
                    path_file=path_file,
                    input_amount=input_amount,
                    name=name,
                    balance_dict=balance_dict,
                    price_dict=price_dict,
                    token_decimals_dict=token_decimals_dict,
                    weights_file=weights_file,
                    capacities_file=capacities_file,
                    lambda_threshold=lambda_threshold
                )
                print(f"Generated allocation for {name} with input amount {input_amount}")

if __name__ == "__main__":
    main() 