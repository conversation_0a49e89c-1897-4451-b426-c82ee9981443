#include <iostream>
#include <vector>
#include <cfloat>
#include "graph.h"

int main() {
    RerankGraph graph;

    std::vector<EdgeGroup> edge_groups(5);
    edge_groups[0].push(Edge{1, 100, 0});
    edge_groups[0].push(Edge{2, 200, 1});
    edge_groups[0].push(Edge{3, 300, 2});

    edge_groups[1].push(Edge{4, 400, 3});
    edge_groups[1].push(Edge{5, 500, 4});
    edge_groups[1].push(Edge{6, 600, 5});

    edge_groups[2].push(Edge{7, 700, 6});
    edge_groups[2].push(Edge{8, 800, 7});
    edge_groups[2].push(Edge{9, 900, 8});

    edge_groups[3].push(Edge{10, 1000, 9});
    edge_groups[3].push(Edge{11, 1100, 10});
    edge_groups[3].push(Edge{12, 1200, 11});

    edge_groups[4].push(Edge{13, 1300, 12});
    edge_groups[4].push(Edge{14, 1400, 13});
    edge_groups[4].push(Edge{15, 1500, 14});

    graph.edge_groups = edge_groups;
    graph.paths.push_back(Path{true, 0, DBL_MAX, {0, 1, 2}});
    graph.paths.push_back(Path{true, 0, DBL_MAX, {3, 4, 2}});

    graph.update_all_paths();

    for(auto& path: graph.paths) {
        std::cout << path.weight_sum << " " << path.min_capacity << std::endl;
    }

    std::cout << "minimal path index: " << graph.minimal_path_index << std::endl;
    std::cout << "minimal path weight: " << graph.paths[graph.minimal_path_index].weight_sum << std::endl;
    std::cout << "--------------------------------" << std::endl;

    for(int i = 0; i < 10; i++) {
        graph.use_minimal_path(100);
        graph.update_minimal_path_index();
        std::cout << "after use minimal path" << std::endl;
        for(int i = 0; i < graph.paths.size(); i++) {
            auto& path = graph.paths[i];
            if(path.is_valid) {
                std::cout << path.weight_sum << " " << path.min_capacity << std::endl;
            }
            else{
                std::cout << "path" << i << " is used up" << std::endl;
            }
        }

        std::cout << "minimal path index: " << graph.minimal_path_index << std::endl;
        std::cout << "minimal path weight: " << graph.paths[graph.minimal_path_index].weight_sum << std::endl;
        std::cout << "--------------------------------" << std::endl;
    }



    return 0;
}