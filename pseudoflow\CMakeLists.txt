cmake_minimum_required(VERSION 3.10)

# Project Name
project(SolverProject)

# Set C++ Standard
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Optimization flag
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -O3")

# Include directories (adjust this if LEMON is in a different location)
include_directories(${CMAKE_SOURCE_DIR} ~/lemon/include)

# Link directories
link_directories(/usr/local/lib ~/lemon/lib)

# Find LEMON (If installed via package manager)
find_library(LEMON_LIB lemon HINTS ~/lemon/lib /opt/homebrew/lib /usr/local/lib)

# Source files
set(SOURCES main.cpp network_simplex.cpp pseudoflow.cpp)

# Create the executable
add_executable(solver ${SOURCES})

# Link the LEMON library
target_link_libraries(solver ${LEMON_LIB})
