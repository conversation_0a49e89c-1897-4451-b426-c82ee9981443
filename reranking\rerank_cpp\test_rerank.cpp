#include <iostream>
#include <fstream>
#include <vector>
#include <queue>
#include <string>
#include <sstream>
#include <limits>
#include <filesystem>
#include <chrono>
#include "graph.h"

// Directory containing edges_group_*.txt and paths.txt
#define EDGE_GROUP_DIR "../test_data" // Change as needed

// Helper to load a single edge group file
EdgeGroup load_edge_group(const std::string& filename) {
    EdgeGroup group;
    std::ifstream fin(filename);
    std::string line;
    while (std::getline(fin, line)) {
        if (line.empty()) continue;
        std::istringstream iss(line);
        double weight, capacity;
        int id;
        iss >> weight >> capacity >> id;
        group.push(Edge{weight, capacity, id});
    }
    return group;
}

// Helper to load all edge group files (edges_group_0.txt, edges_group_1.txt, ...)
std::vector<EdgeGroup> load_all_edge_groups(const std::string& dir) {
    std::vector<EdgeGroup> edge_groups;
    int idx = 0;
    while (true) {
        std::ostringstream oss;
        oss << dir << "/edges_group_" << idx << ".txt";
        std::ifstream fin(oss.str());
        if (!fin.good()) break;
        fin.close();
        edge_groups.push_back(load_edge_group(oss.str()));
        ++idx;
    }
    return edge_groups;
}

// Helper to load paths.txt
std::vector<Path> load_paths(const std::string& filename) {
    std::vector<Path> paths;
    std::ifstream fin(filename);
    std::string line;
    while (std::getline(fin, line)) {
        if (line.empty()) continue;
        std::istringstream iss(line);
        std::vector<int> edge_id_list;
        int idx;
        while (iss >> idx) {
            edge_id_list.push_back(idx);
        }
        Path path;
        path.is_valid = true;
        path.weight_sum = 0;
        path.min_capacity = std::numeric_limits<double>::max();
        path.edge_id_list = edge_id_list;
        paths.push_back(path);
    }
    return paths;
}

int main(int argc, char* argv[]) {
    // Load edge groups and paths
    if(argc != 3) {
        std::cerr << "Usage: " << argv[0] << " <edge_group_dir> <total_amount>" << std::endl;
        return 1;
    }
    std::string edge_group_dir = argv[1];
    double total_amount = std::stod(argv[2]);
    std::vector<EdgeGroup> edge_groups = load_all_edge_groups(EDGE_GROUP_DIR);
    std::vector<Path> paths = load_paths(std::string(EDGE_GROUP_DIR) + "/paths.txt");

    RerankGraph graph;
    graph.edge_groups = edge_groups;
    graph.paths = paths;
    graph.update_all_paths();

    std::cout << "Initial path weights and capacities:" << std::endl;
    for (size_t i = 0; i < graph.paths.size(); ++i) {
        const auto& path = graph.paths[i];
        std::cout << "Path " << i << ": weight_sum=" << path.weight_sum << ", min_capacity=" << path.min_capacity << std::endl;
    }
    std::cout << "Minimal path index: " << graph.minimal_path_index << std::endl;
    std::cout << "Minimal path weight: " << graph.paths[graph.minimal_path_index].weight_sum << std::endl;
    std::cout << "--------------------------------" << std::endl;

    // Use minimal path repeatedly until all paths are exhausted
    // double total_amount =  789.4444139709391;
    // double total_amount =  3947.2220698546957; //789.4444139709391;
    // double total_amount =  23683.33241912817; //3947.2220698546957; //789.4444139709391;
    // int round = 0;
    int original_scan = 0;
    int path_length = graph.paths[0].edge_id_list.size();
    auto start_time = std::chrono::high_resolution_clock::now();
    while(total_amount > 1e-6) {
        // if(round > 100) {
            // break;
        // }
        // round++;    
        graph.use_minimal_path(total_amount);
        graph.update_minimal_path_index();
        graph.actual_scan += graph.paths.size();
        // each edge find its usage by sanning the whole graph
        original_scan += path_length*(graph.paths.size()*path_length);
        if(!graph.paths[graph.minimal_path_index].is_valid) {
            break;
        }
        std::cout << "total amount: " << total_amount << std::endl << std::endl;
    }
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    std::cout << "Time taken: " << duration.count() << " milliseconds" << std::endl;
    std::cout << "original scan: " << original_scan << std::endl;
    std::cout << "no lazy compute scan: " << graph.no_lazy_compute_scan << std::endl;
    std::cout << "actual scan: " << graph.actual_scan << std::endl;
    return 0;
    // 1ms 
    // 4ms
    // 17ms
} 