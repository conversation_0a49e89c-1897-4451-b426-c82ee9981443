{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import json\n", "import math\n", "import networkx as nx\n", "import pandas as pd\n", "import os\n", "import traceback\n", "from collections import defaultdict\n", "from web3 import Web3\n", "from decimal import Decimal"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def load_token_prices(price_file_path):\n", "    \"\"\"\n", "    Load token prices from the quotes file\n", "    Returns a dictionary mapping token address to its WETH price\n", "    \"\"\"\n", "    try:\n", "        with open(price_file_path, 'r') as file:\n", "            price_data = json.load(file)\n", "        \n", "        # Create price mapping\n", "        token_prices = {}\n", "        for token in price_data:\n", "            address = token['address'].lower()\n", "            # Use avg_quote as the price, default to 0 if not available\n", "            try:\n", "                price = float(token['quotes']['avg_quote'])\n", "            except (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>E<PERSON>r):\n", "                price = 0\n", "            token_prices[address] = price\n", "            \n", "        return token_prices\n", "    except Exception as e:\n", "        print(f\"Error loading token prices: {e}\")\n", "        return {}"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def load_data_from_json(file_path, filtered_pools=None):\n", "    \"\"\"\n", "    Load data from a JSON file\n", "    \n", "    Args:\n", "        file_path: Path to the JSON file\n", "        filtered_pools: Optional set of pool addresses to filter by\n", "    \"\"\"\n", "    with open(file_path, 'r') as file:\n", "        data = json.load(file)\n", "    \n", "    # If filtered_pools is provided, only return pools in the filter list\n", "    if filtered_pools:\n", "        filtered_data = []\n", "        for pool in data:\n", "            try:\n", "                pool_data = json.loads(pool) if isinstance(pool, str) else pool\n", "                pool_address = pool_data['poolState']['poolState']['poolAddress'].lower()\n", "                if pool_address in filtered_pools:\n", "                    filtered_data.append(pool)\n", "            except (<PERSON><PERSON><PERSON><PERSON>, j<PERSON>.J<PERSON>NDecodeError):\n", "                continue\n", "        return filtered_data\n", "    \n", "    return data\n", "\n", "\n", "block_number = 21974203\n", "v3_file_path = '../data/prices/block-{}/updated-v3pools.json'.format(block_number)\n", "v3_data = load_data_from_json(v3_file_path)\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON><PERSON>ress</th>\n", "      <th>tickCount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0xd0f292fa6fe694892bcf52b55ca0f3e277be17c0</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0xd0f6ca5683c2b21069dcd64537acb23120de886d</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0xd0fc8ba7e267f2bc56044a7715a489d851dc6d78</td>\n", "      <td>124</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0xd10a303db5f0fb535f4c52e6acf13b19e1afbc7d</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0xd18349dcd0fd3137a92d9c79a8b2025c1929a53d</td>\n", "      <td>8</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                  pool<PERSON>ddress  tickCount\n", "0  0xd0f292fa6fe694892bcf52b55ca0f3e277be17c0          8\n", "1  0xd0f6ca5683c2b21069dcd64537acb23120de886d          9\n", "2  0xd0fc8ba7e267f2bc56044a7715a489d851dc6d78        124\n", "3  0xd10a303db5f0fb535f4c52e6acf13b19e1afbc7d          2\n", "4  0xd18349dcd0fd3137a92d9c79a8b2025c1929a53d          8"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# Count the number of ticks in each pool\n", "tick_counts = []\n", "for pool_data in v3_data:\n", "    try:\n", "        # Handle both string and dict formats\n", "        pool = json.loads(pool_data) if isinstance(pool_data, str) else pool_data\n", "        \n", "        # Access the tickBitMap which contains the ticks\n", "        tick_bit_map = pool['poolState']['poolState']['tickBitMap']\n", "        num_ticks = len(tick_bit_map)\n", "        \n", "        # Get pool address for identification\n", "        pool_address = pool['poolState']['poolState']['poolAddress']\n", "        \n", "        tick_counts.append({\n", "            'poolAddress': pool_address,\n", "            'tickCount': num_ticks\n", "        })\n", "    except (<PERSON><PERSON><PERSON><PERSON>, j<PERSON>.JSONDecodeError) as e:\n", "        print(f\"Error processing pool: {e}\")\n", "\n", "\n", "tick_df = pd.DataFrame(tick_counts)\n", "tick_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### test capacity"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [], "source": ["# left right not 0 but individual\n", "import json\n", "import math\n", "\n", "def calculate_single_pool_capacity(pool_data, token_prices, k=1):\n", "    \"\"\"\n", "    Calculate the capacity of a single pool and its nearby active ticks.\n", "    Improved version with fixed calculations for token balances and tick handling.\n", "\n", "    Args:\n", "        pool_data: Dictionary containing pool data (or JSON string)\n", "        token_prices: Dictionary mapping token addresses to their WETH prices\n", "        k: Number of active ticks to consider in each direction from current tick\n", "\n", "    Returns:\n", "        Dictionary containing:\n", "        - pool_address: The address of the pool\n", "        - current_tick: Current tick information and capacity\n", "        - left_ticks: List of k active ticks to the left with their capacities\n", "        - right_ticks: List of k active ticks to the right with their capacities\n", "        Returns None if pool is invalid or there's an error\n", "    \"\"\"\n", "    try:\n", "        # Parse pool data if it's a JSON string\n", "        if isinstance(pool_data, str):\n", "            pool_data = json.loads(pool_data)\n", "\n", "        # Validate pool type\n", "        if pool_data.get('poolType') != 'uniswap_v3_like_pool':\n", "            return None\n", "            \n", "        pool_static_info = pool_data['poolState']['poolStaticInfo']\n", "        if pool_static_info.get('dexExchange') != 'uniswap':\n", "            return None\n", "            \n", "        pool_state = pool_data['poolState']['poolState']\n", "        pool_address = pool_state['poolAddress']\n", "        \n", "        # Extract token information\n", "        token0_address = pool_static_info['token0'].lower()\n", "        token1_address = pool_static_info['token1'].lower()\n", "        token0_decimals = int(pool_static_info['token0Decimals'])\n", "        token1_decimals = int(pool_static_info['token1Decimals'])\n", "\n", "\n", "        # Get current tick and tick spacing\n", "        current_tick = int(pool_state['currentTick'])\n", "        tick_spacing = int(pool_static_info.get('tickSpacing', 1))\n", "        \n", "        # Get initialized ticks with liquidity\n", "        initialized_ticks = []\n", "        for tick_entry in pool_state.get('tickBitMap', []):\n", "            # if tick_entry[0] == '51240':\n", "            # print(tick_entry[0])\n", "            if len(tick_entry) == 2:\n", "                tick_idx = int(tick_entry[0])\n", "                tick_data = tick_entry[1]\n", "                if tick_data.get('initialized', False) and int(tick_data.get('liquidityGross', 0)) > 0:\n", "                    initialized_ticks.append((tick_idx, tick_data))\n", "        \n", "        # Sort ticks and create lookup map\n", "        initialized_ticks.sort(key=lambda x: x[0])\n", "        # print('initialized_ticks', initialized_ticks)\n", "        tick_bitmap = {tick_idx: data for tick_idx, data in initialized_ticks}\n", "        \n", "        def tick_to_price(tick):\n", "            \"\"\"直接从 tick 计算价格 (token1/token0)\"\"\"\n", "            return 1.0001 ** tick\n", "    \n", "        def tick_to_adjusted_price(tick, token0_decimals, token1_decimals):\n", "            \"\"\"返回考虑小数位的实际价格\"\"\"\n", "            raw_price = 1.0001 ** tick\n", "            decimal_adjustment = 10 ** (token0_decimals - token1_decimals) # - token1_decimals)\n", "            return raw_price * decimal_adjustment\n", "        \n", "        # Initialize result\n", "        result = {\n", "            'pool_address': pool_address,\n", "            'current_tick': None,\n", "            'left_ticks': [],\n", "            'right_ticks': []\n", "        }\n", "        \n", "        # Find the active tick range containing current_tick\n", "        # print(current_tick)\n", "        active_tick_lower = None\n", "        active_tick_upper = None\n", "        \n", "        # Calculate the active tick lower bound by integer division\n", "        current_tick_lower = current_tick // tick_spacing * tick_spacing\n", "        \n", "        # Check if this tick is initialized\n", "        if current_tick_lower in tick_bitmap:\n", "            active_tick_lower = current_tick_lower\n", "            active_tick_upper = current_tick_lower + tick_spacing\n", "        else:\n", "            # If not found, search through initialized ticks\n", "            for tick_idx, tick_data in initialized_ticks:\n", "                if tick_idx <= current_tick < tick_idx + tick_spacing:\n", "                    active_tick_lower = tick_idx\n", "                    active_tick_upper = tick_idx + tick_spacing\n", "                    break\n", "        \n", "        if active_tick_lower is not None:\n", "            tick_liquidity = int(tick_bitmap[active_tick_lower]['liquidityGross'])\n", "    \n", "            # 直接获取价格而非平方根\n", "            price_lower = tick_to_price(active_tick_lower)\n", "            price_upper = tick_to_price(active_tick_upper)\n", "            current_price = tick_to_price(current_tick)\n", "\n", "            current_adjust_price = tick_to_adjusted_price(current_tick, token0_decimals, token1_decimals)\n", "            price_lower_adjust = tick_to_adjusted_price(active_tick_lower, token0_decimals, token1_decimals)\n", "            price_upper_adjust = tick_to_adjusted_price(active_tick_upper, token0_decimals, token1_decimals)\n", "\n", "            # 计算区间内的代币量（使用 Uniswap V3 白皮书中的精确公式）\n", "            # 假设 L 是流动性，Δx 和 Δy 是代币数量，P 是价格\n", "            # Δx = L * (1/√P_a - 1/√P_b)\n", "            # Δy = L * (√P_b - √P_a)\n", "            \n", "            # 当前价格到区间上边界的 token0 量\n", "            token0_balance = tick_liquidity * (1/math.sqrt(current_price) - 1/math.sqrt(price_upper)) / (10**token0_decimals)\n", "            # print('current token0: ', token0_balance)\n", "            \n", "            # 当前价格到区间下边界的 token1 量\n", "            token1_balance = tick_liquidity * (math.sqrt(current_price) - math.sqrt(price_lower)) / (10**token1_decimals)\n", "            # print(f'current token1: {math.sqrt(current_price)} - {math.sqrt(price_lower)}')\n", "            \n", "            # Calculate capacity in ETH\n", "            if token0_address in token_prices and token1_address in token_prices:\n", "                capacity = (\n", "                    token0_balance * token_prices[token0_address] + \n", "                    token1_balance * token_prices[token1_address]\n", "                )\n", "                \n", "\n", "                result['current_tick'] = {\n", "                    'tick': active_tick_lower,\n", "                    'tick_upper': active_tick_upper,\n", "                    'capacity': capacity,\n", "                    'price': [price_lower_adjust, price_upper_adjust],\n", "                    'token0_balance': token0_balance,\n", "                    'token1_balance': token1_balance,\n", "                    'liquidity': tick_liquidity\n", "                }\n", "        \n", "         # --- Nearby Active Ticks Calculation ---\n", "        if active_tick_upper is None and active_tick_lower is None:\n", "            active_tick_lower = current_tick\n", "            active_tick_upper = current_tick\n", "       \n", "        # Left ticks (price < current)\n", "        print('current_tick', current_tick)\n", "        left_ticks = [t for t in initialized_ticks if t[0] < active_tick_lower]\n", "        left_ticks = sorted(left_ticks, key=lambda x: x[0], reverse=True)[:k]\n", "        \n", "        # Right ticks (price > current)\n", "        right_ticks = [t for t in initialized_ticks if t[0] > active_tick_lower]\n", "        right_ticks = sorted(right_ticks, key=lambda x: x[0])[:k]\n", "        \n", "        # Calculate capacity for left ticks (consider both token0 and token1)\n", "        for tick_idx, tick_data in left_ticks:\n", "            tick_liquidity = int(tick_data['liquidityGross'])\n", "            \n", "            # 获取价格\n", "            price_lower = tick_to_price(tick_idx)\n", "            price_upper = tick_to_price(tick_idx + tick_spacing)\n", "\n", "            price_lower_adjust = tick_to_adjusted_price(tick_idx, token0_decimals, token1_decimals)\n", "            price_upper_adjust = tick_to_adjusted_price(tick_idx+tick_spacing, token0_decimals, token1_decimals)\n", "\n", "            # 左侧tick: 当前价格高于此区间，区间内全是token1\n", "            token0_balance = 0 # tick_liquidity * (1/math.sqrt(price_lower) - 1/math.sqrt(price_upper)) / (10**token0_decimals) # 0\n", "            \n", "            # 计算区间内的 token1 量（使用精确公式）\n", "            token1_balance = tick_liquidity * (math.sqrt(price_upper) - math.sqrt(price_lower)) / (10**token1_decimals)\n", "\n", "            if token0_address in token_prices and token1_address in token_prices:\n", "                capacity = token1_balance * token_prices[token1_address]\n", "                \n", "                result['left_ticks'].append({\n", "                    'tick': tick_idx,\n", "                    'tick_upper': tick_idx + tick_spacing,\n", "                    'capacity': capacity,\n", "                    'price': [price_lower_adjust, price_upper_adjust],\n", "                    'token0_balance': token0_balance,\n", "                    'token1_balance': token1_balance,\n", "                    'liquidity': tick_liquidity\n", "                })\n", "        \n", "        # Calculate capacity for right ticks (consider both token0 and token1)\n", "        for tick_idx, tick_data in right_ticks:\n", "            tick_liquidity = int(tick_data['liquidityGross'])\n", "    \n", "            # 获取价格\n", "            price_lower = tick_to_price(tick_idx)\n", "            price_upper = tick_to_price(tick_idx + tick_spacing)\n", "\n", "            price_lower_adjust = tick_to_adjusted_price(tick_idx, token0_decimals, token1_decimals)\n", "            price_upper_adjust = tick_to_adjusted_price(tick_idx+tick_spacing, token0_decimals, token1_decimals)\n", "\n", "            \n", "            # 右侧tick: 当前价格低于此区间，区间内全是token0\n", "            token1_balance = 0 # tick_liquidity * (math.sqrt(price_upper) - math.sqrt(price_lower)) / (10**token1_decimals) # 0\n", "            \n", "            # 计算区间内的 token0 量（使用精确公式）\n", "            token0_balance = tick_liquidity * (1/math.sqrt(price_lower) - 1/math.sqrt(price_upper)) / (10**token0_decimals)\n", "\n", "            if token0_address in token_prices and token1_address in token_prices:\n", "                # 计算价值\n", "                capacity = token0_balance * token_prices[token0_address]\n", "                result['right_ticks'].append({\n", "                    'tick': tick_idx,\n", "                    'tick_upper': tick_idx + tick_spacing,\n", "                    'capacity': capacity,\n", "                    'price': [price_lower_adjust, price_upper_adjust],\n", "                    'token0_balance': token0_balance,\n", "                    'token1_balance': token1_balance,\n", "                    'liquidity': tick_liquidity\n", "                })\n", "        \n", "        return result\n", "        \n", "    except Exception as e:\n", "        print(f\"Error processing pool {pool_address if 'pool_address' in locals() else 'unknown'}: {str(e)}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [], "source": ["def test_specific_pool_capacity(pool_address, k=2):\n", "    try:\n", "        # Load data using same paths as main code\n", "        block_number = 21974203\n", "        v3_file_path = f'../data/prices/block-{block_number}/filtered-v3pools.json'\n", "        price_file_path = f'../data/prices/block-{block_number}/tokens-quotes.json'\n", "        \n", "        # Load token prices\n", "        token_prices = load_token_prices(price_file_path)\n", "        \n", "        # Load V3 pools\n", "        with open(v3_file_path, 'r') as file:\n", "            v3_pools = json.load(file)\n", "        \n", "        # Find specific pool\n", "        pool_data = None\n", "        for pool in v3_pools:\n", "            try:\n", "                pool_dict = json.loads(pool) if isinstance(pool, str) else pool\n", "                if pool_dict['poolState']['poolState']['poolAddress'].lower() == pool_address.lower():\n", "                    pool_data = pool_dict\n", "                    break\n", "            except:\n", "                continue\n", "        \n", "        if pool_data: # Number of ticks to consider in each direction\n", "            result = calculate_single_pool_capacity(pool_data, token_prices, k)\n", "\n", "            # current tick\n", "            if result['current_tick']:\n", "                print(\"current_tick\")\n", "                print(result['current_tick'])\n", "            else:\n", "                print(\"current_tick is None\")\n", "            print()\n", "            \n", "            # left ticks\n", "            if result['left_ticks']:\n", "                for left_tick in result['left_ticks']:\n", "                    print(\"left_tick\")\n", "                    print(left_tick)\n", "            else:\n", "                print(\"left_tick is None\")  \n", "            \n", "            print()\n", "\n", "            # right ticks\n", "            if result['right_ticks']:\n", "                for right_tick in result['right_ticks']:\n", "                    print(\"right_tick\")\n", "                    print(right_tick) \n", "            else:\n", "                print(\"right_tick is None\")\n", "           \n", "        else:\n", "            print(f\"Pool {pool_address} not found in data\")\n", "            \n", "    except Exception as e:\n", "        print(f\"Error in test: {e}\")\n", "        traceback.print_exc()\n"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["current_tick 179470\n", "current_tick is None\n", "\n", "left_tick\n", "{'tick': 174800, 'tick_upper': 175000, 'capacity': 0.2604484288682621, 'price': [0.03900210036904792, 0.03978995527303792], 'token0_balance': 0, 'token1_balance': 0.2613620386278842, 'liquidity': 4164348354395916}\n", "left_tick\n", "{'tick': 173800, 'tick_upper': 174000, 'capacity': 0.0003756674476759824, 'price': [0.03529073623738647, 0.03600362039867405], 'token0_balance': 0, 'token1_balance': 0.00037698522658545974, 'liquidity': 6314551542180}\n", "\n", "right_tick\n", "{'tick': 180000, 'tick_upper': 180200, 'capacity': 0.0004776674770741013, 'price': [0.06560090568504753, 0.06692606496521715], 'token0_balance': 0.0077570452805814155, 'token1_balance': 0, 'liquidity': 6314551542180}\n", "right_tick\n", "{'tick': 183200, 'tick_upper': 183400, 'capacity': 0.0918760286403668, 'price': [0.09033938323912129, 0.09216426767957205], 'token0_balance': 1.4920138978872957, 'token1_balance': 0, 'liquidity': 1425288225699567}\n"]}], "source": [" # Example usage for specific pool\n", "test_specific_pool_capacity(\"0xf1b63cd9d80f922514c04b0fd0a30373316dd75b\", 2)  # Example pool address"]}], "metadata": {"kernelspec": {"display_name": "python39", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.20"}}, "nbformat": 4, "nbformat_minor": 2}