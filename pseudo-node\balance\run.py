import subprocess
import os

def node_name_mapping(node_name_file):
    node_name_mapping = {}
    with open(node_name_file, 'r') as file:
        for line in file:
            line = line.strip()
            if line:
                parts = line.split()
                symbol = parts[0]
                address = parts[1]
                node_name_mapping[symbol] = address
    return node_name_mapping

def read_in_node_mapping(node_mapping_file):
    node_mapping = {}
    with open(node_mapping_file, 'r') as file:
        for line in file:
            line = line.strip()
            if line:
                parts = line.split()
                node_id = parts[0]
                address = parts[1]
                node_mapping[address] = node_id
    return node_mapping

    
def run_dfs_optimize(
    executable_path,
    graph_file,
    source,
    target,
    max_length,
    k,
    equivalent_nodes_file="none"
):
    """
    Run the compiled C++ DFS optimization program with the given parameters.
    
    Args:
        executable_path: Path to the compiled C++ executable
        graph_file: Path to the graph file
        source: Source vertex
        target: Target vertex
        max_length: Maximum path length
        k: Number of disjoint paths
        equivalent_nodes_file: Path to equivalent nodes file or "none"
    
    Returns:
        The stdout and stderr output from the program
    """
    # Ensure the executable exists
    if not os.path.exists(executable_path):
        raise FileNotFoundError(f"Executable not found at {executable_path}")
    
    # Ensure the graph file exists
    if not os.path.exists(graph_file):
        raise FileNotFoundError(f"Graph file not found at {graph_file}")
    
    # Check if equivalent_nodes_file exists if it's not "none"
    if equivalent_nodes_file != "none" and not os.path.exists(equivalent_nodes_file):
        raise FileNotFoundError(f"Equivalent nodes file not found at {equivalent_nodes_file}")
    
    # Create the results directory if it doesn't exist
    os.makedirs("results", exist_ok=True)
    
    # Prepare the input string with all the required inputs
    input_string = f"{graph_file}\n{source}\n{target}\n{max_length}\n{k}\n{equivalent_nodes_file}\n"
    
    # Run the executable with the prepared input
    process = subprocess.Popen(
        [executable_path],
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    # Send the input and get the output
    stdout, stderr = process.communicate(input_string)
    
    # Check if the process completed successfully
    if process.returncode != 0:
        print(f"Error running the executable. Return code: {process.returncode}")
        print(f"Error output: {stderr}")
    
    return stdout, stderr

def main():
    # Configuration parameters
    executable_path = "./dfs_no_disjoint"  # Path to your compiled executable

    block_number = 21974203
    name = 'pairs_capacity_usdt_ohm'

    node_index = read_in_node_mapping(f"../graph_construction/graphs/{block_number}_{name}_node_map.txt")
    node_name = node_name_mapping(f"../data/node_name.txt")
    # print(node_name)
    # print(node_index)
    source_symbol = "usdt"
    target_symbol = "ohm"

    source = node_index[node_name[source_symbol]]
    target = node_index[node_name[target_symbol]]
    # Example usage
    print("Running DFS optimization...")
    
    # You can define multiple test cases
    test_cases = [
        {
            "graph_file": f"../graph_construction/graphs/{block_number}_{name}_token_graph.txt",
            "source": source,
            "target": target,
            "max_length": 3,
            "k": 100,
            "equivalent_nodes_file": f"../graph_construction/graphs/relationship/{block_number}_{name}_relationships.txt"
        },
    ]
    
    # Run each test case
    for i, test_case in enumerate(test_cases):
        print(f"\nRunning test case {i+1}:")
        print(f"Graph: {test_case['graph_file']}")
        print(f"Source: {test_case['source']}, Target: {test_case['target']}")
        print(f"Max Length: {test_case['max_length']}, k: {test_case['k']}")
        
        stdout, stderr = run_dfs_optimize(
            executable_path,
            test_case["graph_file"],
            test_case["source"],
            test_case["target"],
            test_case["max_length"],
            test_case["k"],
            test_case["equivalent_nodes_file"]
        )
        
        # Print the output
        print("\nProgram output:")
        print(stdout)
        
        if stderr:
            print("\nErrors/warnings:")
            print(stderr)
        
        # Get the output file name

if __name__ == "__main__":
    main()