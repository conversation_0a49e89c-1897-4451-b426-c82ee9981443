import subprocess
import os
import argparse

def node_name_mapping(node_name_file):
    node_name_mapping = {}
    with open(node_name_file, 'r') as file:
        for line in file:
            line = line.strip()
            if line:
                parts = line.split()
                symbol = parts[0]
                address = parts[1]
                node_name_mapping[symbol] = address
    return node_name_mapping

def read_in_node_mapping(node_mapping_file):
    node_mapping = {}
    with open(node_mapping_file, 'r') as file:
        for line in file:
            line = line.strip()
            if line:
                parts = line.split()
                node_id = parts[0]
                address = parts[1]
                node_mapping[address] = node_id
    return node_mapping

    
def run_dfs_optimize(
    executable_path,
    graph_file,
    source,
    target,
    max_length,
    equivalent_nodes_file="none"
):
    """
    Run the compiled C++ DFS optimization program with the given parameters.
    
    Args:
        executable_path: Path to the compiled C++ executable
        graph_file: Path to the graph file
        source: Source vertex
        target: Target vertex
        max_length: Maximum path length
        k: Number of disjoint paths
        equivalent_nodes_file: Path to equivalent nodes file or "none"
    
    Returns:
        The stdout and stderr output from the program
    """
    # Ensure the executable exists
    if not os.path.exists(executable_path):
        raise FileNotFoundError(f"Executable not found at {executable_path}")
    
    # Ensure the graph file exists
    if not os.path.exists(graph_file):
        raise FileNotFoundError(f"Graph file not found at {graph_file}")
    
    # Check if equivalent_nodes_file exists if it's not "none"
    if equivalent_nodes_file != "none" and not os.path.exists(equivalent_nodes_file):
        raise FileNotFoundError(f"Equivalent nodes file not found at {equivalent_nodes_file}")
    
    # Create the results directory if it doesn't exist
    os.makedirs("results", exist_ok=True)
    
    # Prepare the input string with all the required inputs
    input_string = f"{graph_file}\n{source}\n{target}\n{max_length}\n"
    
    # Run the executable with the prepared input
    process = subprocess.Popen(
        [executable_path],
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    # Send the input and get the output
    stdout, stderr = process.communicate(input_string)
    
    # Check if the process completed successfully
    if process.returncode != 0:
        print(f"Error running the executable. Return code: {process.returncode}")
        print(f"Error output: {stderr}")
    
    return stdout, stderr

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run DFS path optimization')
    parser.add_argument('--pairs', type=str, default="wbtc-spx", 
                        help='Comma-separated list of source-target pairs (e.g., "usdt-frax,usdt-ohm")')
    parser.add_argument('--max_length', type=int, default=3, help='Maximum path length')
    args = parser.parse_args()
    
    # Configuration parameters
    executable_path = "enum/dfs"  # Path to your compiled executable
    block_number = 21974203
    name = 'combined'
    max_length = args.max_length

    # Load token mappings
    node_index = read_in_node_mapping(f"graphs/{block_number}_{name}_node_map.txt")
    node_name = node_name_mapping(f"../data/node_name.txt")
    
    # Parse pairs
    pairs = [pair.strip().lower() for pair in args.pairs.split(',')]
    
    # Process each source-target pair
    for pair in pairs:
        # Split the pair into source and target
        try:
            source_symbol, target_symbol = pair.split('-')
            source_symbol = source_symbol.lower()
            target_symbol = target_symbol.lower()
        except ValueError:
            print(f"Error: Invalid pair format: {pair}. Use format 'source-target'")
            continue
        
        # Validate token symbols
        if source_symbol not in node_name:
            print(f"Error: Source token '{source_symbol}' not found in node mappings")
            continue
        if target_symbol not in node_name:
            print(f"Error: Target token '{target_symbol}' not found in node mappings")
            continue
        
        print(f"\n{'=' * 80}")
        print(f"Processing pair: {source_symbol}-{target_symbol}")
        print(f"Max path length: {max_length}")
        print(f"{'=' * 80}\n")
        
        source = node_index[node_name[source_symbol]]
        target = node_index[node_name[target_symbol]]
        
        # Example usage
        print("Running DFS optimization...")
        
        # Define test case
        test_case = {
            "graph_file": f"graphs/{block_number}_{name}_token_graph.txt",
            "source": source,
            "target": target,
            "max_length": max_length,
        }
        
        print(f"Graph: {test_case['graph_file']}")
        print(f"Source: {test_case['source']}, Target: {test_case['target']}")
        print(f"Max Length: {test_case['max_length']}")
        
        stdout, stderr = run_dfs_optimize(
            executable_path,
            test_case["graph_file"],
            test_case["source"],
            test_case["target"],
            test_case["max_length"],
        )
        
        # Print the output
        print("\nProgram output:")
        print(stdout)
        
        if stderr:
            print("\nErrors/warnings:")
            print(stderr)

if __name__ == "__main__":
    main()