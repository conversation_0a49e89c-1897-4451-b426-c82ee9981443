Running tests for ./small_test/graph_2.txt with pairs from ./small_test/test_pairs.txt
Test: Source: 3 Sink: 69
Reading graph from file: ./small_test/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs
Number of nodes     : 66432
Number of arcs      : 164316
Time to max flow    : 0.005111

Solution checks as feasible.

Solution checks as optimal.
Max Flow            : 1984
---------------------------------------
Test: Source: 3 Sink: 410
Reading graph from file: ./small_test/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs
Number of nodes     : 66432
Number of arcs      : 164316
Time to max flow    : 0.005153

Solution checks as feasible.

Solution checks as optimal.
Max Flow            : 59817
---------------------------------------
Test: Source: 3 Sink: 117
Reading graph from file: ./small_test/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs
Number of nodes     : 66432
Number of arcs      : 164316
Time to max flow    : 0.00512933

Solution checks as feasible.

Solution checks as optimal.
Max Flow            : 61494
---------------------------------------
Test: Source: 3 Sink: 635
Reading graph from file: ./small_test/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs
Number of nodes     : 66432
Number of arcs      : 164316
Time to max flow    : 0.00517863

Solution checks as feasible.

Solution checks as optimal.
Max Flow            : 39882
---------------------------------------
Test: Source: 3 Sink: 125
Reading graph from file: ./small_test/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs
Number of nodes     : 66432
Number of arcs      : 164316
Time to max flow    : 0.00522738

Solution checks as feasible.

Solution checks as optimal.
Max Flow            : 61425
---------------------------------------
Test: Source: 3 Sink: 0
Reading graph from file: ./small_test/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs
Number of nodes     : 66432
Number of arcs      : 164316
Time to max flow    : 0.00522162

Solution checks as feasible.

Solution checks as optimal.
Max Flow            : 50214
---------------------------------------
Test: Source: 3 Sink: 1
Reading graph from file: ./small_test/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs
Number of nodes     : 66432
Number of arcs      : 164316
Time to max flow    : 0.00518071

Solution checks as feasible.

Solution checks as optimal.
Max Flow            : 32990
---------------------------------------
Test: Source: 3 Sink: 4
Reading graph from file: ./small_test/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs
Number of nodes     : 66432
Number of arcs      : 164316
Time to max flow    : 0.00527425

Solution checks as feasible.

Solution checks as optimal.
Max Flow            : 32990
---------------------------------------
Test: Source: 3 Sink: 2
Reading graph from file: ./small_test/graph_2.txt
Graph loaded: 66432 nodes, 164316 arcs
Number of nodes     : 66432
Number of arcs      : 164316
Time to max flow    : 0.00547254

Solution checks as feasible.

Solution checks as optimal.
Max Flow            : 32990
---------------------------------------
