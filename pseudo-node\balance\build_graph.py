import json
import math
import networkx as nx
import pandas as pd
import os
import traceback
from collections import defaultdict
import heapq
from collections import defaultdict, deque

def load_data_from_json(file_path, filtered_pools=None):
    """
    Load data from a JSON file
    
    Args:
        file_path: Path to the JSON file
        filtered_pools: Optional set of pool addresses to filter by
    """
    with open(file_path, 'r') as file:
        data = json.load(file)
    
    # If filtered_pools is provided, only return pools in the filter list
    if filtered_pools:
        filtered_data = []
        for pool in data:
            try:
                pool_data = json.loads(pool) if isinstance(pool, str) else pool
                pool_address = pool_data['poolState']['poolState']['poolAddress'].lower()
                if pool_address in filtered_pools:
                    filtered_data.append(pool)
            except (KeyError, json.JSONDecodeError):
                continue
        return filtered_data
    
    return data

def compute_exchange_rate(reserveA, reserveB, trade_amount):
    if trade_amount >= reserveA:
        return None # Invalid trade, prevents division by zero

    new_reserveA = reserveA + trade_amount  # Adding traded amount to reserveA
    new_reserveB = (reserveA * reserveB) / new_reserveA  # Solve for new reserveB using AMM formula
    new_rate = new_reserveB / new_reserveA  # Apply fee and compute rate
    return new_rate#, new_reserveA, new_reserveB

def load_token_prices(price_file_path):
    """
    Load token prices from the quotes file
    Returns a dictionary mapping token address to its WETH price
    """
    try:
        with open(price_file_path, 'r') as file:
            price_data = json.load(file)
        
        # Create price mapping
        token_prices = {}
        for token in price_data:
            address = token['address'].lower()
            # Use avg_quote as the price, default to 0 if not available
            try:
                price = float(token['quotes']['avg_quote'])
            except (KeyError, ValueError, TypeError):
                price = 0
            token_prices[address] = price
            
        return token_prices
    except Exception as e:
        print(f"Error loading token prices: {e}")
        return {}

# -- for modeling V2 sudo ticks
import math
from decimal import Decimal, getcontext, ROUND_DOWN

# Ensure Decimal precision is high enough for financial calculations
# Example: getcontext().prec = 78 (Uniswap's requirement for sqrtPriceMath)
# For general use, 50-60 should be ample if not doing direct sqrtPriceX96 manipulations.
# If you are setting this globally, do it once at the start of your script.
# For this function, we'll assume it's set appropriately elsewhere.
# getcontext().prec = 60 # Example precision

DECIMAL_ZERO = Decimal(0)
DECIMAL_ONE = Decimal(1)
# EPSILON should be small enough to avoid issues but not so small it causes underflow with current precision
DECIMAL_EPSILON = Decimal('1e-30') # Adjusted for potentially higher precision context

def calculate_exchange_rates_v2(
    pools_data: list,
    token_prices_eth: dict,
    percentage_steps: list = [0.01, 0.02, 0.05, 0.1, 0.15, 0.2],
    num_current_percentage_layers: int = 1
):
    """
    Calculates virtual tick information for V2 pools based on price percentage intervals.
    This function is designed to output data that can be consumed by a modified
    `create_token_graph` function to model V2 pools with multiple depth layers.

    Args:
        pools_data: A list of V2 pool data dictionaries (as per the provided JSON structure).
        token_prices_eth: A dictionary mapping token addresses (lowercase) to their ETH prices (as Decimal or float).
        percentage_steps: A list of floats representing percentage deviations from the current price
                          to define virtual tick boundaries (e.g., [0.01, 0.05] for 1% and 5%).
        num_current_percentage_layers: How many layers of `percentage_steps` around P_current
                                       are considered part of the "current" price range.

    Returns:
        A tuple containing:
        - results (list): A list of dictionaries, where each dictionary represents a
                          directional segment of a V2 virtual tick with its properties.
        - processed_pools_count (int): The number of pools successfully processed.
    """
    results = []
    processed_pools_count = 0

    # Convert percentage steps to Decimal for precision
    decimal_percentage_steps = sorted([Decimal(str(p)) for p in percentage_steps])

    for pool_entry in pools_data:
        try:
            if pool_entry.get("poolType") != "uniswap_v2_like_pool":
                continue

            pool_static_info = pool_entry["poolState"]["poolStaticInfo"]
            pool_state = pool_entry["poolState"]["poolState"]
            pool_address = pool_static_info["poolAddress"].lower()

            token0_address = pool_static_info["token0"].lower()
            token1_address = pool_static_info["token1"].lower()
            token0_decimals = int(pool_static_info["token0Decimals"])
            token1_decimals = int(pool_static_info["token1Decimals"])

            # Use Decimal for all reserve and price calculations
            r0_initial_raw = Decimal(pool_state["tokenBalance0"])
            r1_initial_raw = Decimal(pool_state["tokenBalance1"])

            # Adjust for decimals
            r0_initial = r0_initial_raw / (DECIMAL_ONE * 10 ** token0_decimals)
            r1_initial = r1_initial_raw / (DECIMAL_ONE * 10 ** token1_decimals)

            if r0_initial <= DECIMAL_EPSILON or r1_initial <= DECIMAL_EPSILON:
                # print(f"Warning: Pool {pool_address} has negligible or zero initial reserves.")
                continue

            k = r0_initial * r1_initial # Constant product k = x*y
            # Fee rate from ppm to decimal (e.g., 3000 ppm -> 0.003)
            fee_rate = Decimal(pool_static_info["swapFee"]) / Decimal(1000000)

            # Current price P = r1/r0 (amount of token1 per 1 unit of token0)
            p_current = r1_initial / r0_initial
            if p_current <= DECIMAL_EPSILON:
                # print(f"Warning: Pool {pool_address} has negligible or zero current price.")
                continue
            
            # --- Define price boundaries based on percentage steps ---
            # Left side boundaries (prices lower than p_current)
            # P_current, P_L1, P_L2, ... where P_Li = P_current * (1 - step_i)
            # These will be sorted in descending order: P_current, P_L_closest, P_L_further, ...
            left_boundaries = [p_current]
            for pct_step in decimal_percentage_steps:
                left_boundaries.append(p_current * (DECIMAL_ONE - pct_step))
            # Filter out non-positive prices, remove duplicates, sort descending
            left_boundaries = sorted(list(set(p for p in left_boundaries if p > DECIMAL_EPSILON)), reverse=True)

            # Right side boundaries (prices higher than p_current)
            # P_current, P_R1, P_R2, ... where P_Ri = P_current * (1 + step_i)
            # These will be sorted in ascending order: P_current, P_R_closest, P_R_further, ...
            right_boundaries = [p_current]
            for pct_step in decimal_percentage_steps:
                right_boundaries.append(p_current * (DECIMAL_ONE + pct_step))
            right_boundaries = sorted(list(set(p for p in right_boundaries if p > DECIMAL_EPSILON)))


            # --- Generate virtual tick segments for token0 -> token1 (price decreases) ---
            # Iterates through intervals like [P_L1, P_current], [P_L2, P_L1], ...
            # P_start is the higher price, P_end is the lower price in the interval.
            for i in range(len(left_boundaries) - 1):
                p_start_segment = left_boundaries[i]   # Upper price boundary of the segment
                p_end_segment = left_boundaries[i+1] # Lower price boundary of the segment

                if p_end_segment >= p_start_segment: continue # Price must decrease

                # Theoretical reserves at p_start_segment
                r0_at_p_start = (k / p_start_segment).sqrt()
                r1_at_p_start = (k * p_start_segment).sqrt()
                # Theoretical reserves at p_end_segment
                r0_at_p_end = (k / p_end_segment).sqrt()
                # r1_at_p_end = (k * p_end_segment).sqrt() # Can also be calculated as k / r0_at_p_end

                # Net token0 input required to move price from p_start_segment to p_end_segment
                delta_r0_net_input = r0_at_p_end - r0_at_p_start
                if delta_r0_net_input <= DECIMAL_EPSILON: continue

                # Gross token0 input (capacity for this segment)
                capacity_token0_gross = delta_r0_net_input / (DECIMAL_ONE - fee_rate)
                # Net token1 output from this trade
                delta_r1_net_output = r1_at_p_start - (k / r0_at_p_end) # or r1_at_p_start - r1_at_p_end

                if capacity_token0_gross <= DECIMAL_EPSILON or delta_r1_net_output <= DECIMAL_EPSILON:
                    continue
                
                avg_effective_rate_t0_to_t1 = delta_r1_net_output / capacity_token0_gross
                
                price_token0_in_eth = Decimal(str(token_prices_eth.get(token0_address, '0')))
                liquidity_value_eth_segment = capacity_token0_gross * price_token0_in_eth
                
                # Determine pair_type ("current" or "left")
                is_current_segment = False
                # The first num_current_percentage_layers segments away from p_current are "current"
                # For left side, these are the segments where p_start is p_current,
                # or p_start is P_L1 and p_end is P_L2 (if num_current_percentage_layers > 1), etc.
                # This logic checks if the segment's upper bound (p_start) is within the "current" price bands.
                # A segment is [P_Li+1, P_Li]. If P_Li is one of the first num_current_percentage_layers boundaries from P_current.
                # The index `i` here corresponds to the i-th segment away from p_current.
                if i < num_current_percentage_layers:
                    is_current_segment = True
                
                pair_type = "current" if is_current_segment else "left"
                
                # For "current" type, liquidity_value_eth will be a list [cap_t0_t1, cap_t1_t0]
                # Initialize the t0->t1 part here. The t1->t0 part will be filled later.
                current_segment_liquidity_eth = [float(liquidity_value_eth_segment), DECIMAL_ZERO] if pair_type == "current" else float(liquidity_value_eth_segment)

                results.append({
                    "pool_id": pool_address,
                    "original_token0": token0_address,
                    "original_token1": token1_address,
                    # Pseudo node names for this segment's ends
                    "token0_symbol": f"{token0_address}_v2_{pool_address[-6:]}_{p_end_segment:.12f}_{p_start_segment:.12f}_t0",
                    "token1_symbol": f"{token1_address}_v2_{pool_address[-6:]}_{p_end_segment:.12f}_{p_start_segment:.12f}_t1",
                    "rate_token0_to_token1": float(avg_effective_rate_t0_to_t1),
                    "rate_token1_to_token0": None,
                    "swap_fee": float(fee_rate),
                    "liquidity_value_eth": current_segment_liquidity_eth,
                    "capacity_token_in_gross": float(capacity_token0_gross),
                    "output_token_out_net": float(delta_r1_net_output),
                    "price_range_p1_div_p0": [float(p_start_segment), float(p_end_segment)], # [upper_price, lower_price]
                    "pair_type": pair_type,
                    "direction": "token0_to_token1"
                })

            # --- Generate virtual tick segments for token1 -> token0 (price increases) ---
            # Iterates through intervals like [P_current, P_R1], [P_R1, P_R2], ...
            # P_start is the lower price, P_end is the higher price in the interval.
            for i in range(len(right_boundaries) - 1):
                p_start_segment = right_boundaries[i]  # Lower price boundary of the segment
                p_end_segment = right_boundaries[i+1]  # Upper price boundary of the segment

                if p_end_segment <= p_start_segment: continue # Price must increase

                r1_at_p_start = (k * p_start_segment).sqrt()
                # r0_at_p_start = (k / p_start_segment).sqrt()
                r1_at_p_end = (k * p_end_segment).sqrt()
                # r0_at_p_end = (k / p_end_segment).sqrt()

                delta_r1_net_input = r1_at_p_end - r1_at_p_start # Net token1 input
                if delta_r1_net_input <= DECIMAL_EPSILON: continue
                
                capacity_token1_gross = delta_r1_net_input / (DECIMAL_ONE - fee_rate)
                delta_r0_net_output = (k / p_start_segment).sqrt() - (k / p_end_segment).sqrt() # or r0_at_p_start - r0_at_p_end

                if capacity_token1_gross <= DECIMAL_EPSILON or delta_r0_net_output <= DECIMAL_EPSILON:
                    continue

                avg_effective_rate_t1_to_t0 = delta_r0_net_output / capacity_token1_gross
                
                price_token1_in_eth = Decimal(str(token_prices_eth.get(token1_address, '0')))
                liquidity_value_eth_segment = capacity_token1_gross * price_token1_in_eth

                is_current_segment = False
                if i < num_current_percentage_layers:
                    is_current_segment = True
                pair_type = "current" if is_current_segment else "right"

                # If this is a "current" segment for t1->t0, find its t0->t1 counterpart and update liquidity_value_eth
                if pair_type == "current":
                    found_counterpart = False
                    # The counterpart t0->t1 segment would have p_start_segment (which is p_current for the first right segment)
                    # as its p_start, and its p_end would be p_current * (1 - corresponding_pct_step).
                    # This matching is a bit tricky due to how segments are defined.
                    # A simpler way: the "current" segments are those closest to P_current.
                    # The t0->t1 current segment is [P_current * (1-step_j), P_current]
                    # The t1->t0 current segment is [P_current, P_current * (1+step_j)]
                    # They share P_current as a boundary.
                    for res_idx, res_entry in enumerate(results):
                        if (res_entry["pool_id"] == pool_address and
                            res_entry["pair_type"] == "current" and
                            res_entry["direction"] == "token0_to_token1" and
                            # Check if the P_current boundary matches
                            abs(res_entry["price_range_p1_div_p0"][0] - p_start_segment) < DECIMAL_EPSILON * DECIMAL_ONE * 100 and # p_start_segment is P_current
                            # And if the "width" of the percentage step is the same
                            abs( (p_current - res_entry["price_range_p1_div_p0"][1])/p_current - \
                                 (p_end_segment - p_current)/p_current ) < DECIMAL_EPSILON * DECIMAL_ONE * 10):
                            if isinstance(res_entry["liquidity_value_eth"], list):
                                results[res_idx]["liquidity_value_eth"][1] = float(liquidity_value_eth_segment)
                                # Also update the pseudo node names to be consistent for the "current" pair
                                # The "current" pair should ideally share the same pseudo node names for token0 and token1
                                # For simplicity, we might just use the ones from the t0->t1 entry.
                                # Or, create a combined "current" entry later.
                                # For now, we mark this one as "merged" so create_token_graph can handle it.
                                pair_type = "current_t1t0_merged" # Special type to indicate it's the second half
                                found_counterpart = True
                            break
                    if not found_counterpart and pair_type == "current":
                        # This is a t1->t0 current segment without a t0->t1 counterpart yet,
                        # or the counterpart was filtered. This shouldn't happen if logic is symmetrical.
                        # We'll create a new entry, but it will be lopsided.
                        current_segment_liquidity_eth = [DECIMAL_ZERO, float(liquidity_value_eth_segment)]
                    elif pair_type != "current":
                         current_segment_liquidity_eth = float(liquidity_value_eth_segment)
                    # If found_counterpart, this specific entry's liquidity_value_eth is not the primary one.
                    # The primary one is the list in the t0->t1 entry.
                else: # "right"
                    current_segment_liquidity_eth = float(liquidity_value_eth_segment)


                # Only add if it's not a merged counterpart (which is already handled by updating the t0->t1 entry)
                if pair_type != "current_t1t0_merged":
                    results.append({
                        "pool_id": pool_address,
                        "original_token0": token0_address,
                        "original_token1": token1_address,
                        "token0_symbol": f"{token0_address}_v2_{pool_address[-6:]}_{p_start_segment:.12f}_{p_end_segment:.12f}_t0",
                        "token1_symbol": f"{token1_address}_v2_{pool_address[-6:]}_{p_start_segment:.12f}_{p_end_segment:.12f}_t1",
                        "rate_token0_to_token1": None,
                        "rate_token1_to_token0": float(avg_effective_rate_t1_to_t0),
                        "swap_fee": float(fee_rate),
                        "liquidity_value_eth": current_segment_liquidity_eth,
                        "capacity_token_in_gross": float(capacity_token1_gross),
                        "output_token_out_net": float(delta_r0_net_output),
                        "price_range_p1_div_p0": [float(p_start_segment), float(p_end_segment)], # [lower_price, upper_price]
                        "pair_type": pair_type,
                        "direction": "token1_to_token0"
                    })
            processed_pools_count += 1
        except Exception as e:
            # print(f"Error processing V2 pool {pool_address}: {e}")
            # import traceback
            # traceback.print_exc()
            continue
            
    # Final cleanup for "current" entries that might not have found a counterpart
    # (e.g., if one direction had zero capacity and was filtered)
    for res_entry in results:
        if res_entry["pair_type"] == "current" and res_entry["direction"] == "token0_to_token1":
            if isinstance(res_entry["liquidity_value_eth"], list) and res_entry["liquidity_value_eth"][1] == DECIMAL_ZERO:
                # This means the t1->t0 part was not found or had zero capacity.
                # create_token_graph should be able to handle if list[1] is 0.0
                pass # Keep as is, [cap_A, 0.0]

    return results, processed_pools_count

def calculate_exchange_rates_v3(pools, k=1, token_prices=None, liquidity_threshold=0):
    """
    Compute the exchange rate using Uniswap V3's sqrtPriceX96 and tick data.
    
    This function processes Uniswap V3 pools to extract exchange rates at:
    1. Current price point (if initialized)
    2. k ticks to the left of current price (k pairs)
    3. k ticks to the right of current price (k pairs)

    In total, each pool will have up to 2*k+1 pseudo pairs:
    - 1 pair at the current price (if initialized)
    - k pairs at lower prices (left ticks)
    - k pairs at higher prices (right ticks)
    
    Each pseudo pair consists of two tokens with pool and tick information in their identifiers.
    
    Args:
        pools: List of pool data from Uniswap V3
        k: Number of ticks to consider in each direction from current price
        token_prices: Dictionary mapping token addresses to their WETH prices
        
    Returns:
        results: List of exchange rates for all pools and their ticks
        skipped_pools: Number of pools skipped due to errors
        pair_pools: Dictionary mapping token pairs to their pool addresses
    """
    results = []
    skipped_pools = 0
    
    # Track pools by token pair - this helps identify when multiple pools exist for the same token pair
    pair_pools = {}  # Maps token pair to list of pools
    
    # ===== FIRST PASS: Group pools by token pair =====
    # This helps us determine which pools share the same token pair
    for pool in pools:
        try:
            pool_data = json.loads(pool) if isinstance(pool, str) else pool
            
            if pool_data['poolType'] == 'uniswap_v3_like_pool':
                pool_static_info = pool_data['poolState']['poolStaticInfo']
                if pool_static_info['dexExchange'] == 'uniswap':
                    pool_state = pool_data['poolState']['poolState']
                    pool_address = pool_state['poolAddress']
                    
                    # Extract token addresses
                    token0 = pool_static_info['token0'].lower()
                    token1 = pool_static_info['token1'].lower()
                    
                    # Create a unique pair identifier (sort tokens to ensure consistency)
                    # This ensures that token0_token1 and token1_token0 are treated as the same pair
                    pair_key = "_".join(sorted([token0, token1]))
                    
                    if pair_key not in pair_pools:
                        pair_pools[pair_key] = []
                    
                    # Store the pool address as the identifier
                    pair_pools[pair_key].append(pool_address)
        except Exception:
            continue
    
    # ===== SECOND PASS: Process each pool and create exchange rates =====
    for pool in pools:
        try:
            pool_data = json.loads(pool) if isinstance(pool, str) else pool
            
            if pool_data['poolType'] == 'uniswap_v3_like_pool':
                pool_static_info = pool_data['poolState']['poolStaticInfo']
                if pool_static_info['dexExchange'] == 'uniswap':
                    pool_state = pool_data['poolState']['poolState']
                    pool_address = pool_state['poolAddress']
                    
                    # Extract token addresses
                    token0 = pool_static_info['token0'].lower()
                    token1 = pool_static_info['token1'].lower()

                    token0_address = pool_static_info['token0'].lower()
                    token1_address = pool_static_info['token1'].lower()

                    token0_decimals = int(pool_static_info['token0Decimals'])
                    token1_decimals = int(pool_static_info['token1Decimals'])
                    
                    # Create a unique pair identifier
                    pair_key = "_".join(sorted([token0, token1]))
                    
                    # Pool-specific suffix for pool-specific nodes (not base tokens)
                    # Use last 6 characters of pool address to keep names shorter
                    pool_suffix = "_{0}".format(pool_address[-6:])
                    
                    # ===== EXTRACT CURRENT PRICE AND POOL PARAMETERS =====
                    
                    # Get current tick and tick spacing
                    current_tick = int(pool_state.get('currentTick', 0))
                    tick_spacing = int(pool_static_info.get('tickSpacing', 1))
                    
                    # Get initialized ticks with liquidity
                    initialized_ticks = []
                    
                    for tick_entry in pool_state.get('tickBitMap', []):
                        if len(tick_entry) == 2:
                            tick_idx = int(tick_entry[0])
                            tick_data = tick_entry[1]
                            
                            # Only consider initialized ticks with liquidity
                            if tick_data.get('initialized', False) and int(tick_data.get('liquidityGross', 0)) > 0:
                                initialized_ticks.append((tick_idx, tick_data))
                    
                    # Sort ticks for easier processing
                    initialized_ticks.sort(key=lambda x: x[0])
                    
                    # Create a dictionary for quick lookup
                    tick_bitmap = {tick_idx: data for tick_idx, data in initialized_ticks}
                    
                    # Function to compute sqrt price from tick
                    def tick_to_price(tick):
                        """直接从 tick 计算价格 (token1/token0)"""
                        return 1.0001 ** tick
                    
                    def tick_to_adjusted_price(tick, token0_decimals, token1_decimals):
                        """返回考虑小数位的实际价格"""
                        raw_price = 1.0001 ** tick
                        decimal_adjustment = 10 ** (token0_decimals - token1_decimals)
                        return raw_price * decimal_adjustment
                            
                    # Find the active tick range containing current_tick
                    active_tick_lower = None
                    active_tick_upper = None
                    for i in range(len(initialized_ticks) - 1):
                        if initialized_ticks[i][0] <= current_tick < initialized_ticks[i+1][0]:
                            active_tick_lower = initialized_ticks[i][0]
                            active_tick_upper = initialized_ticks[i+1][0]
                            break
                    
                    # If we couldn't find the active tick range, use current tick as reference
                    if active_tick_lower is None and active_tick_upper is None:
                        active_tick_lower = current_tick
                        active_tick_upper = current_tick
                    
                    # Get swap fee (stored as parts per million)
                    swap_fee = int(pool_static_info.get('swapFee', 0)) / 1e6  # Convert to percentage
                    
                    # ===== PROCESS CURRENT TICK =====
                    if active_tick_lower is not None:
                        current_price = tick_to_price(current_tick)
                        
                        # Calculate current liquidity by accumulating liquidityNet values
                        current_liquidity = 0
                        for tick_idx, tick_data in initialized_ticks:
                            if tick_idx <= current_tick:
                                current_liquidity += int(tick_data.get('liquidityNet', 0))
                            else:
                                break
                        
                        price_lower = tick_to_price(active_tick_lower)
                        price_upper = tick_to_price(active_tick_upper)
                        
                        # Calculate token amounts in the current tick range
                        token0_balance = current_liquidity * (1/math.sqrt(current_price) - 1/math.sqrt(price_upper)) / (10**token0_decimals)
                        token1_balance = current_liquidity * (math.sqrt(current_price) - math.sqrt(price_lower)) / (10**token1_decimals)
                        
                        token0_value_eth = token0_balance * token_prices[token0_address]
                        token1_value_eth = token1_balance * token_prices[token1_address]
                        
                        # Calculate capacity in ETH
                        if token0_address in token_prices and token1_address in token_prices:
                            tick_value_eth = (
                                token0_value_eth + token1_value_eth
                            )
                            
                            if tick_value_eth < liquidity_threshold:
                                continue
                            
                            # Create unique identifiers for current tick nodes
                            tick_token0 = f"{token0}{pool_suffix}_tick{active_tick_lower}"
                            tick_token1 = f"{token1}{pool_suffix}_tick{active_tick_lower}"
                            
                            tick_price_lower = tick_to_adjusted_price(active_tick_lower, token0_decimals, token1_decimals)
                            tick_price_upper = tick_to_adjusted_price(active_tick_upper, token0_decimals, token1_decimals)
                            
                            current_tick_result = {
                                'pool_id': pool_address,
                                'token0_symbol': tick_token0,
                                'token1_symbol': tick_token1,
                                'rate_token0_to_token1': tick_price_lower,
                                'rate_token1_to_token0': 1 / tick_price_upper,
                                'swap_fee': swap_fee,
                                'original_token0': token0,
                                'original_token1': token1,
                                'pair_key': pair_key,
                                'pool_address': pool_address,
                                'total_pools_for_pair': len(pair_pools[pair_key]),
                                'tick': active_tick_lower,
                                'liquidity': current_liquidity,
                                'liquidity_value_eth': [token0_value_eth, token1_value_eth],
                                'is_tick_node': True,
                                'pair_type': 'current'
                            }
                            results.append(current_tick_result)
                    
                    # ===== PROCESS LEFT TICKS (price < current) =====
                    left_intervals = []
                    for i in reversed(range(len(initialized_ticks) - 1)):
                        if initialized_ticks[i+1][0] < current_tick:
                            left_intervals.append((initialized_ticks[i][0], initialized_ticks[i+1][0]))
                        if len(left_intervals) >= k:
                            break
                    
                    for lower, upper in left_intervals:
                        # Calculate liquidity for this interval by accumulating liquidityNet values
                        interval_liquidity = 0
                        for tick_idx, tick_data in initialized_ticks:
                            if tick_idx <= lower:
                                interval_liquidity += int(tick_data.get('liquidityNet', 0))
                            elif tick_idx > upper:
                                break
                        
                        price_lower = tick_to_price(lower)
                        price_upper = tick_to_price(upper)
                        
                        # For left ticks (below current price), token0 is 0
                        token0_balance = 0
                        
                        # Calculate token1 amount in this tick range
                        token1_balance = interval_liquidity * (math.sqrt(price_upper) - math.sqrt(price_lower)) / (10**token1_decimals)
                        
                        if token0_address in token_prices and token1_address in token_prices:
                            tick_value_eth = token1_balance * token_prices[token1_address]
                            
                            if tick_value_eth < liquidity_threshold:
                                continue
                            
                            # Create unique identifiers for left tick nodes
                            tick_token0 = f"{token0}{pool_suffix}_tick{lower}"
                            tick_token1 = f"{token1}{pool_suffix}_tick{lower}"
                            
                            tick_price = tick_to_adjusted_price(lower, token0_decimals, token1_decimals)
                            
                            left_tick_result = {
                                'pool_id': pool_address,
                                'token0_symbol': tick_token0,
                                'token1_symbol': tick_token1,
                                'rate_token0_to_token1': tick_price,
                                'rate_token1_to_token0': 1 / tick_price,
                                'swap_fee': swap_fee,
                                'original_token0': token0,
                                'original_token1': token1,
                                'pair_key': pair_key,
                                'pool_address': pool_address,
                                'total_pools_for_pair': len(pair_pools[pair_key]),
                                'tick': lower,
                                'liquidity': interval_liquidity,
                                'liquidity_value_eth': tick_value_eth,
                                'is_tick_node': True,
                                'pair_type': 'left'
                            }
                            results.append(left_tick_result)
                    
                    # ===== PROCESS RIGHT TICKS (price > current) =====
                    right_intervals = []
                    for i in range(len(initialized_ticks) - 1):
                        if initialized_ticks[i][0] > current_tick:
                            right_intervals.append((initialized_ticks[i][0], initialized_ticks[i+1][0]))
                        if len(right_intervals) >= k:
                            break
                    
                    for lower, upper in right_intervals:
                        # Calculate liquidity for this interval by accumulating liquidityNet values
                        interval_liquidity = 0
                        for tick_idx, tick_data in initialized_ticks:
                            if tick_idx <= lower:
                                interval_liquidity += int(tick_data.get('liquidityNet', 0))
                            elif tick_idx > upper:
                                break
                        
                        price_lower = tick_to_price(lower)
                        price_upper = tick_to_price(upper)
                        
                        # For right ticks (above current price), token1 is 0
                        token1_balance = 0
                        
                        # Calculate token0 amount in this tick range
                        token0_balance = interval_liquidity * (1/math.sqrt(price_lower) - 1/math.sqrt(price_upper)) / (10**token0_decimals)
                        
                        if token0_address in token_prices and token1_address in token_prices:
                            tick_value_eth = token0_balance * token_prices[token0_address]
                            
                            if tick_value_eth < liquidity_threshold:
                                continue
                            
                            # Create unique identifiers for right tick nodes
                            tick_token0 = f"{token0}{pool_suffix}_tick{lower}"
                            tick_token1 = f"{token1}{pool_suffix}_tick{lower}"
                            
                            tick_price = tick_to_adjusted_price(upper, token0_decimals, token1_decimals)
                            
                            right_tick_result = {
                                'pool_id': pool_address,
                                'token0_symbol': tick_token0,
                                'token1_symbol': tick_token1,
                                'rate_token0_to_token1': tick_price,
                                'rate_token1_to_token0': 1 / tick_price,
                                'swap_fee': swap_fee,
                                'original_token0': token0,
                                'original_token1': token1,
                                'pair_key': pair_key,
                                'pool_address': pool_address,
                                'total_pools_for_pair': len(pair_pools[pair_key]),
                                'tick': lower,
                                'liquidity': interval_liquidity,
                                'liquidity_value_eth': tick_value_eth,
                                'is_tick_node': True,
                                'pair_type': 'right'
                            }
                            results.append(right_tick_result)
        except Exception as e:
            skipped_pools += 1
            continue
    
    return results, skipped_pools

def create_token_graph(
    v3_exchange_rates: list,
    v2_virtual_tick_segments: list,
    token_prices_eth: dict = None, # Currently not used for V2 capacity calculation here
):
    """
    Create a token exchange graph with weighted edges and directional constraints.
    V3 pools are modeled with ticks.
    V2 pools are modeled with refined virtual tick segments based on price ranges.

    Args:
        v3_exchange_rates: List of exchange rate dictionaries from calculate_exchange_rates_v3.
        v2_virtual_tick_segments: List of dictionaries from calculate_exchange_rates_v2_refined,
                                   each representing a directional segment of a V2 virtual tick.
        token_prices_eth: Optional dictionary mapping token addresses to their ETH prices.
                          (Note: For V2, liquidity_value_eth is already pre-calculated
                           in v2_virtual_tick_segments by calculate_exchange_rates_v2_refined)

    Returns:
        G: NetworkX DiGraph representing the token exchange network.
    """
    G = nx.DiGraph()
    log_base = math.e

    # --- V3 POOL PROCESSING (UNCHANGED) ---
    v3_base_tokens = {}
    v3_token_pseudo_nodes = defaultdict(set)
    v3_node_pair_types = {}

    # ===== FIRST PASS (V3): Identify base tokens and their pseudo nodes from V3 pools =====
    for rate in v3_exchange_rates:
        token0, token1 = rate['original_token0'], rate['original_token1']
        token0_node_v3, token1_node_v3 = rate['token0_symbol'], rate['token1_symbol']
        pair_type_v3 = rate.get('pair_type', 'current')
        if '_' in token0_node_v3: # Check if it's a pseudo node
            v3_token_pseudo_nodes[token0].add(token0_node_v3)
            v3_node_pair_types[token0_node_v3] = pair_type_v3
        if '_' in token1_node_v3: # Check if it's a pseudo node
            v3_token_pseudo_nodes[token1].add(token1_node_v3)
            v3_node_pair_types[token1_node_v3] = pair_type_v3
        if token0 not in v3_base_tokens: v3_base_tokens[token0] = token0
        if token1 not in v3_base_tokens: v3_base_tokens[token1] = token1

    # ===== SECOND PASS (V3): Add V3 nodes to the graph =====
    for token_address in v3_base_tokens.keys():
        if not G.has_node(token_address):
            G.add_node(token_address, is_base=True, token=token_address, protocol='base')
    for token_address, pseudo_nodes_set in v3_token_pseudo_nodes.items():
        for node_v3 in pseudo_nodes_set:
            if not G.has_node(node_v3):
                tick_v3 = None
                if '_tick' in node_v3:
                    try: tick_v3 = int(node_v3.split('_tick')[1].split('_')[0])
                    except (IndexError, ValueError): pass
                pair_type_v3 = v3_node_pair_types.get(node_v3, 'current')
                G.add_node(node_v3, is_pseudo=True, token=token_address, tick=tick_v3, pair_type=pair_type_v3, protocol='v3')

    # ===== THIRD PASS (V3): Connect V3 base tokens to their V3 pseudo nodes =====
    for rate in v3_exchange_rates:
        token0, token1 = rate['original_token0'], rate['original_token1']
        token0_node_v3, token1_node_v3 = rate['token0_symbol'], rate['token1_symbol']
        pair_type_v3 = rate.get('pair_type', 'current')
        # Ensure nodes exist before adding edges (redundant if PASS 2 is comprehensive, but safe)
        if not G.has_node(token0): G.add_node(token0, is_base=True, token=token0, protocol='base')
        if not G.has_node(token1): G.add_node(token1, is_base=True, token=token1, protocol='base')
        if not G.has_node(token0_node_v3): G.add_node(token0_node_v3, is_pseudo=True, token=token0, pair_type=pair_type_v3, protocol='v3')
        if not G.has_node(token1_node_v3): G.add_node(token1_node_v3, is_pseudo=True, token=token1, pair_type=pair_type_v3, protocol='v3')

        if pair_type_v3 == 'current':
            G.add_edge(token0, token0_node_v3, weight=0, protocol='v3_connector', pair_type='current')
            G.add_edge(token0_node_v3, token0, weight=0, protocol='v3_connector', pair_type='current')
            G.add_edge(token1, token1_node_v3, weight=0, protocol='v3_connector', pair_type='current')
            G.add_edge(token1_node_v3, token1, weight=0, protocol='v3_connector', pair_type='current')
        elif pair_type_v3 == 'right':
            G.add_edge(token0_node_v3, token0, weight=0, protocol='v3_connector', pair_type='right')
            G.add_edge(token1, token1_node_v3, weight=0, protocol='v3_connector', pair_type='right')
        elif pair_type_v3 == 'left':
            G.add_edge(token0, token0_node_v3, weight=0, protocol='v3_connector', pair_type='left')
            G.add_edge(token1_node_v3, token1, weight=0, protocol='v3_connector', pair_type='left')

    # ===== FOURTH PASS (V3): Connect V3 pseudo nodes based on exchange rates =====
    for rate in v3_exchange_rates:
        token0_node_v3, token1_node_v3 = rate['token0_symbol'], rate['token1_symbol']
        pair_type_v3 = rate.get('pair_type', 'current')
        if rate.get('rate_token0_to_token1') is None or rate.get('rate_token1_to_token0') is None: continue
        try:
            swap_fee_v3 = float(rate['swap_fee'])
            raw_r0t1_v3, raw_r1t0_v3 = float(rate['rate_token0_to_token1']), float(rate['rate_token1_to_token0'])
            eff_r0t1_v3, eff_r1t0_v3 = (1 - swap_fee_v3) * raw_r0t1_v3, (1 - swap_fee_v3) * raw_r1t0_v3
            if eff_r0t1_v3 <= 0 or eff_r1t0_v3 <= 0: continue
            w0t1_v3, w1t0_v3 = -math.log(eff_r0t1_v3, log_base), -math.log(eff_r1t0_v3, log_base)
            cap_v3, pid_v3, tick_v3 = rate.get('liquidity_value_eth', 0), rate['pool_id'], rate.get('tick')

            if ('_tick' in token0_node_v3 and '_tick' in token1_node_v3 and rate['original_token0'] != rate['original_token1']):
                try:
                    tval0 = int(token0_node_v3.split('_tick')[1].split('_')[0])
                    tval1 = int(token1_node_v3.split('_tick')[1].split('_')[0])
                    if tval0 == tval1: # Same tick, different tokens
                        if pair_type_v3 == 'current':
                            G.add_edge(token0_node_v3, token1_node_v3, weight=w0t1_v3, rate=eff_r0t1_v3, capacity=cap_v3[1], pool_id=pid_v3, pair_type='current', protocol='v3_swap', tick=tick_v3)
                            G.add_edge(token1_node_v3, token0_node_v3, weight=w1t0_v3, rate=eff_r1t0_v3, capacity=cap_v3[0], pool_id=pid_v3, pair_type='current', protocol='v3_swap', tick=tick_v3)
                        elif pair_type_v3 == 'right': # token1 -> token0
                            G.add_edge(token1_node_v3, token0_node_v3, weight=w1t0_v3, rate=eff_r1t0_v3, capacity=cap_v3, pool_id=pid_v3, pair_type='right', protocol='v3_swap', tick=tick_v3)
                        elif pair_type_v3 == 'left': # token0 -> token1
                            G.add_edge(token0_node_v3, token1_node_v3, weight=w0t1_v3, rate=eff_r0t1_v3, capacity=cap_v3, pool_id=pid_v3, pair_type='left', protocol='v3_swap', tick=tick_v3)
                except (IndexError, ValueError, TypeError): pass
        except (ValueError, TypeError, ZeroDivisionError) as e: continue
    
    # --- V2 POOL PROCESSING (REFINED BASED ON VIRTUAL TICKS) ---
    
    # PASS 1 (V2): Add base tokens, V2 pseudo nodes, and V2 swap edges
    for segment in v2_virtual_tick_segments:
        pool_id_v2 = segment['pool_id']
        original_token0_v2 = segment['original_token0']
        original_token1_v2 = segment['original_token1']
        
        if not G.has_node(original_token0_v2): G.add_node(original_token0_v2, is_base=True, token=original_token0_v2, protocol='base')
        if not G.has_node(original_token1_v2): G.add_node(original_token1_v2, is_base=True, token=original_token1_v2, protocol='base')

        v2_pseudo_node_t0_repr = segment['token0_symbol']
        v2_pseudo_node_t1_repr = segment['token1_symbol']
        
        price_range_v2 = segment['price_range_p1_div_p0'] # This is a list [p_start, p_end]

        if not G.has_node(v2_pseudo_node_t0_repr):
            G.add_node(v2_pseudo_node_t0_repr, is_pseudo=True, token=original_token0_v2, 
                       pair_type=segment['pair_type'], protocol='v2_tick', 
                       price_range=price_range_v2, # Correctly use the list
                       pool_id=pool_id_v2)
        if not G.has_node(v2_pseudo_node_t1_repr):
            G.add_node(v2_pseudo_node_t1_repr, is_pseudo=True, token=original_token1_v2, 
                       pair_type=segment['pair_type'], protocol='v2_tick', 
                       price_range=price_range_v2, # Correctly use the list
                       pool_id=pool_id_v2)
        
        try:
            # swap_fee_v2 = float(segment['swap_fee']) # Already used in refined_v2 to calculate effective rate
            capacity_eth_v2 = segment['liquidity_value_eth']
            
            if segment['direction'] == 'token0_to_token1':
                if segment.get('rate_token0_to_token1') is None: continue
                effective_rate_v2 = float(segment['rate_token0_to_token1'])
                if effective_rate_v2 <= 0: continue
                weight_v2 = -math.log(effective_rate_v2, log_base)
                
                current_capacity = 0.0
                if segment['pair_type'] == 'current' and isinstance(capacity_eth_v2, list):
                    if len(capacity_eth_v2) > 0: # Check if list is not empty
                        current_capacity = float(capacity_eth_v2[0]) # Capacity for t0->t1
                elif segment['pair_type'] != 'current': # left
                    current_capacity = float(capacity_eth_v2)
                
                if current_capacity > 0:
                    G.add_edge(v2_pseudo_node_t0_repr, v2_pseudo_node_t1_repr,
                               weight=weight_v2, rate=effective_rate_v2, pool_id=pool_id_v2,
                               pair_type=segment['pair_type'], capacity=current_capacity, protocol='v2_swap')

            elif segment['direction'] == 'token1_to_token0':
                if segment.get('rate_token1_to_token0') is None: continue
                effective_rate_v2 = float(segment['rate_token1_to_token0'])
                if effective_rate_v2 <= 0: continue
                weight_v2 = -math.log(effective_rate_v2, log_base)

                current_capacity = 0.0
                if segment['pair_type'] == 'current' and isinstance(capacity_eth_v2, list):
                    if len(capacity_eth_v2) > 1: # Check if list has second element
                        current_capacity = float(capacity_eth_v2[1]) # Capacity for t1->t0
                elif segment['pair_type'] != 'current': # right
                    current_capacity = float(capacity_eth_v2)
                
                if current_capacity > 0:
                     G.add_edge(v2_pseudo_node_t1_repr, v2_pseudo_node_t0_repr,
                               weight=weight_v2, rate=effective_rate_v2, pool_id=pool_id_v2,
                               pair_type=segment['pair_type'], capacity=current_capacity, protocol='v2_swap')
        except (ValueError, TypeError, ZeroDivisionError, KeyError, AttributeError) as e:
            # print(f"Error adding V2 swap edge for pool {pool_id_v2}, segment {segment}: {e}")
            continue

    # PASS 2 (V2): Connect base tokens to their corresponding V2 pseudo nodes
    for segment in v2_virtual_tick_segments:
        original_token0_v2 = segment['original_token0']
        original_token1_v2 = segment['original_token1']
        # These are the pseudo nodes representing the original tokens within this segment's context
        v2_pseudo_node_for_t0_in_segment = segment['token0_symbol'] 
        v2_pseudo_node_for_t1_in_segment = segment['token1_symbol'] 
        pair_type_v2 = segment['pair_type'] # This is the pair_type of the *segment* (price range relative to P_current)

        # Connections for original_token0
        if pair_type_v2 == 'current':
            G.add_edge(original_token0_v2, v2_pseudo_node_for_t0_in_segment, weight=0, protocol='v2_connector', pair_type='current')
            G.add_edge(v2_pseudo_node_for_t0_in_segment, original_token0_v2, weight=0, protocol='v2_connector', pair_type='current')
        elif pair_type_v2 == 'right': 
            G.add_edge(v2_pseudo_node_for_t0_in_segment, original_token0_v2, weight=0, protocol='v2_connector', pair_type='right')
        elif pair_type_v2 == 'left':  
            G.add_edge(original_token0_v2, v2_pseudo_node_for_t0_in_segment, weight=0, protocol='v2_connector', pair_type='left')

        # Connections for original_token1
        if pair_type_v2 == 'current':
            G.add_edge(original_token1_v2, v2_pseudo_node_for_t1_in_segment, weight=0, protocol='v2_connector', pair_type='current')
            G.add_edge(v2_pseudo_node_for_t1_in_segment, original_token1_v2, weight=0, protocol='v2_connector', pair_type='current')
        elif pair_type_v2 == 'right': 
            G.add_edge(original_token1_v2, v2_pseudo_node_for_t1_in_segment, weight=0, protocol='v2_connector', pair_type='right')
        elif pair_type_v2 == 'left':  
            G.add_edge(v2_pseudo_node_for_t1_in_segment, original_token1_v2, weight=0, protocol='v2_connector', pair_type='left')

    # PASS 3 (V2): Connect consecutive V2 pseudo nodes of the SAME original token within the SAME pool
    # This allows flow along the V2 price curve for a single token.
    v2_nodes_to_link = defaultdict(list) # Key: (pool_id, original_token_address)
                                         # Value: list of {'name': pseudo_node_name, 'price_range': [p_start, p_end]}

    # Collect all pseudo nodes that represent a specific original token within a specific pool
    for segment in v2_virtual_tick_segments:
        pool_id = segment['pool_id']
        price_range = segment['price_range_p1_div_p0'] # [p_start_of_segment, p_end_of_segment]

        # segment['token0_symbol'] represents segment['original_token0']
        v2_nodes_to_link[(pool_id, segment['original_token0'])].append({
            'name': segment['token0_symbol'], 
            'price_range': price_range,
            'direction_of_segment': segment['direction'] # To infer which end of price_range is "entry" vs "exit" for this token
        })
        # segment['token1_symbol'] represents segment['original_token1']
        v2_nodes_to_link[(pool_id, segment['original_token1'])].append({
            'name': segment['token1_symbol'], 
            'price_range': price_range,
            'direction_of_segment': segment['direction']
        })
        
    for (pool_id, original_token), node_infos_list in v2_nodes_to_link.items():
        if len(node_infos_list) < 2: # Need at least two pseudo nodes for the same token to link
            continue

        # Sort these pseudo nodes based on their price range to establish an order.
        # A pseudo node represents a token *within a price segment*.
        # The naming convention f"{token_address}_v2_{pool_address[-6:]}_{p_end:.12f}_{p_start:.12f}_t0"
        # implies the price range. We need to sort these pseudo_nodes for the *same original_token*
        # by the price levels they represent.
        
        # Let's define a canonical price point for each pseudo_node for sorting.
        # If it's original_token0, its price relative to token1 is P.
        # If it's original_token1, its price relative to token0 is 1/P.
        # The price_range is P = t1/t0.
        
        # For a given original_token, its pseudo_nodes will span different price_ranges.
        # We want to connect them if their price_ranges are "adjacent".
        
        # Example: original_token0.
        # Segment1: [P_high1, P_low1], pseudo_node_t0_seg1 (represents original_token0 in this segment)
        # Segment2: [P_high2, P_low2], pseudo_node_t0_seg2
        # If P_low1 is very close to P_high2, then pseudo_node_t0_seg1 should connect to pseudo_node_t0_seg2.

        # This requires a more robust way of identifying "entry" and "exit" points of a token
        # for a given segment, or a different pseudo-node naming strategy from calculate_exchange_rates_v2_refined.
        # The current `token0_symbol` and `token1_symbol` are tied to the *segment* (a swap between two tokens).

        # Given the current structure, and your confirmation that the "推流" algorithm handles multi-tick consumption
        # by path re-evaluation, explicitly adding these V2 "flow along the curve" edges for the same token
        # might be an over-complication if not strictly necessary for the "推流" algorithm.
        # The base token connectors (PASS 2 V2) allow exiting one segment and entering another via the base token.

        # If direct V2 segment-to-segment flow for the same token is crucial and cannot rely on
        # going via base token, then `calculate_exchange_rates_v2_refined` would ideally output
        # nodes that represent *one token at one price boundary* (e.g., TokenA_PoolX_PriceY).
        # Then these nodes could be easily sorted and linked.

        # For now, this part remains complex with the current pseudo-node definition.
        # We will rely on the "推流" algorithm to handle multi-segment V2 paths via base nodes.
        pass # Placeholder for V2 same-token, consecutive segment linking.

    return G

def print_graph(token_graph, path, token_names, v3_data):
    """
    Save the graph to files with format: from to weight capacity
    """
    # Ensure output directory exists
    os.makedirs(os.path.dirname(path) if os.path.dirname(path) else '.', exist_ok=True)
    
    # Sort nodes to ensure consistent node IDs
    sorted_nodes = sorted(token_graph.nodes())
    node_map = {node: idx for idx, node in enumerate(sorted_nodes)}
    
    pool_balance_map = {}
    for edge in token_graph.edges(data=True):
        source, target, data = edge
        if '_' in source and 'v2' not in source:
            # print('debugging!!!!source2: ', source)
            pool_tick = source.split('_')[1]+source.split('_')[2]
            # print(data)
            if data.get('capacity', 0) > 0:
                # if isinstance(data.get('capacity', 0), list):
                    #print('debugging!!!!capacity: ', data.get('capacity', 0))
                if pool_tick in pool_balance_map and pool_balance_map[pool_tick] != 0:
                    value = pool_balance_map[pool_tick]
                    if find_token0_token1_pools(v3_data, source, data.get('pool_address', 'NA')):
                        pool_balance_map[pool_tick] = [data.get('capacity', 0), value]
                    else:
                        pool_balance_map[pool_tick] = [value, data.get('capacity', 0)]
                else:
                    pool_balance_map[pool_tick] = data.get('capacity', 0)
                
   #  print('debugging!!!!pool_balance_map: ', pool_balance_map)
    
    graph_file = '{}_token_graph.txt'.format(path)
    map_file = '{}_node_map.txt'.format(path)
    balance_file = '{}_pool_balance.txt'.format(path)
    
    # Write the graph with weights and capacities
    with open(graph_file, 'w') as f:
        for edge in token_graph.edges(data=True):
            source, target, data = edge
            source_idx = node_map[source]
            target_idx = node_map[target]
            weight = data['weight']
            capacity = data.get('capacity', 0)  # 如果没有capacity，默认为0
            # f.write(f"{source_idx} {target_idx} {weight} {capacity}\n")
            type = data.get('pair_type', 'do not know2')
            f.write(f"{source_idx} {target_idx} {weight} \n")
    # Write the node mapping
    with open(map_file, 'w') as f:
        for node, idx in node_map.items():
            name = token_names[node] if node in token_names else ''
            f.write(f"{idx} {node} {name}\n")
    
    with open(balance_file, 'w') as f:  
        for pool_tick, balance in pool_balance_map.items():
            f.write(f"{pool_tick} {balance}\n")

    return graph_file, map_file, balance_file
    
def load_ranked_pools(ranked_pools_file):
    """
    Load ranked pools from a text file and return the top k pools
    
    Args:
        ranked_pools_file: Path to the text file with ranked pools
        top_k: Number of top pools to return (None for all)
    
    Returns:
        Set of pool addresses (lowercase)
    """
    pools = {}
    try:
        with open(ranked_pools_file, 'r') as file:
            for line in file:
                parts = line.strip().split()
                if len(parts) >= 2:
                    pool_address = parts[0].lower()
                    liquidity = float(parts[1])
                    pools[pool_address] = liquidity
    except Exception as e:
        print("Error loading ranked pools: {}".format(e))
        return set()
    
    # Sort by liquidity (descending)
    # pools.sort(key=lambda x: x[1], reverse=True)
    
    # Return set of pool addresses
    return pools


def find_relevant_pools(G, token_pairs, max_hop=3, top_n=50, ranked_pools=None, select=False):
    # If select is False, return all pools in the graph
    if not select:
        all_pools = set()
        for u, v, data in G.edges(data=True):
            pool = data.get("pool_address")
            if pool:
                all_pools.add(pool)
        # print(f"Found {len(all_pools)} pools in the graph")
        return all_pools

    if len(token_pairs) != 1:
        print(f"Warning: Expected one token pair, got {len(token_pairs)}. Using first.")

    token0, token1 = token_pairs[0]
    token0, token1 = token0.lower(), token1.lower()

    if not G.has_node(token0) or not G.has_node(token1):
        print(f"One or both tokens not in graph: {token0}, {token1}")
        return [], set()

    # Use bidirectional BFS to find meeting nodes within max_hop
    forward = {token0: [[]]}  # 修改：存储路径列表
    backward = {token1: [[]]}  # 修改：存储路径列表
    f_queue = deque([(token0, [], set())])  # (node, path, visited_token_pairs)
    b_queue = deque([(token1, [], set())])  # (node, path, visited_token_pairs)

    candidate_paths = []

    for _ in range(max_hop // 2 + 1):
        def expand(queue, visited, other_visited, reverse=False):
            for _ in range(len(queue)):
                current, path, visited_pairs = queue.popleft()
                
                for neighbor in G.neighbors(current):
                    # 创建标准化的代币对
                    token_pair = tuple(sorted([current, neighbor]))
                    # 如果这对代币已经在当前路径中访问过，跳过
                    if token_pair in visited_pairs:
                        continue

                    edge_data_dict = G.get_edge_data(current, neighbor)
                    # 对于这对代币之间的每个池子都创建一个新路径
                    for key, edge_data in edge_data_dict.items():
                        pool = edge_data.get("pool_address")
                        liquidity = ranked_pools.get(pool, 0) if ranked_pools else 0
                        edge = (current, neighbor, pool, liquidity) if not reverse else (neighbor, current, pool, liquidity)
                        
                        # 创建新的visited_pairs集合，包含当前token pair
                        new_visited_pairs = visited_pairs | {token_pair}
                        new_path = path + [edge]
                        
                        if neighbor not in visited:
                            visited[neighbor] = [new_path]  # 修改：存储路径列表
                            queue.append((neighbor, new_path, new_visited_pairs))
                            
                        elif neighbor in other_visited:
                            path1 = new_path
                            
                            # 检查其他方向的所有路径
                            for other_path in other_visited[neighbor]:
                                # 获取其他路径中的代币对
                                other_path_pairs = set()
                                for edge in other_path:
                                    a, b = edge[0], edge[1]  # 只取前两个元素（代币地址）
                                    other_path_pairs.add(tuple(sorted([a, b])))
                                
                                # 如果没有重复的代币对，创建新路径
                                if not (new_visited_pairs & other_path_pairs):
                                    path2 = list(reversed(other_path))
                                    full_path = path1 + [(b, a, p, l) for a, b, p, l in path2]
                                    
                                    if len(full_path) <= max_hop:
                                        liqs = [l for _, _, _, l in full_path]
                                        if liqs:
                                            min_liq = min(liqs)
                                            heapq.heappush(candidate_paths, (-min_liq, full_path))
                        else:
                            visited[neighbor].append(new_path)
                            queue.append((neighbor, new_path, new_visited_pairs))

        expand(f_queue, forward, backward)
        expand(b_queue, backward, forward, reverse=True)

    if not candidate_paths:
        print(f"No path found between {token0} and {token1} within {max_hop} hops.")
        return [], set()

    candidate_paths_full = candidate_paths.copy()
    selected_pools = set()

    while candidate_paths and len(selected_pools) < top_n:
        _, path = heapq.heappop(candidate_paths)
        pools_in_path = [pool for _, _, pool, _ in path if pool and pool not in selected_pools]
        for pool in pools_in_path:
            selected_pools.add(pool)
            if len(selected_pools) >= top_n:
                break

    return selected_pools

def find_token0_token1_pools(v3_data, token, pool_address):
    """
    Find one token is token0 or token1 in one pool
    """
    for pool in v3_data:
        pool_data = json.loads(pool) if isinstance(pool, str) else pool
        if pool_data['poolType'] == 'uniswap_v3_like_pool':
            pool_static_info = pool_data['poolState']['poolStaticInfo']
            if pool_static_info['poolAddress'] == pool_address:
                if token in [pool_static_info['token0'], pool_static_info['token1']]:
                    return token == pool_static_info['token0']
    return 'NA'

def build_initial_graph(v3_data, v2_data):
    """
    Build initial graph where nodes are tokens and edges are pools
    
    Args:
        v3_data: List of V3 pool data
        v2_data: List of V2 pool data
    
    Returns:
        G: NetworkX graph with tokens as nodes and pools as edges
    """
    G = nx.MultiDiGraph()
    
    # Process V3 pools
    for pool in v3_data:
        # try:
            pool_data = json.loads(pool) if isinstance(pool, str) else pool
            
            if pool_data['poolType'] == 'uniswap_v3_like_pool':
                pool_static_info = pool_data['poolState']['poolStaticInfo']
                if pool_static_info['dexExchange'] == 'uniswap':
                    pool_state = pool_data['poolState']['poolState']
                    pool_address = pool_state['poolAddress'].lower()
                    
                    # Extract token addresses
                    token0 = pool_static_info['token0'].lower()
                    token1 = pool_static_info['token1'].lower()
                    
                    # Get liquidity
                    liquidity = int(pool_state.get('liquidity', 0))
                    
                    # Add nodes if they don't exist
                    if not G.has_node(token0):
                        G.add_node(token0)
                    if not G.has_node(token1):
                        G.add_node(token1)
                    
                    # Add edge between tokens with pool information
                    # Using MultiDiGraph to allow multiple pools between the same token pair
                    G.add_edge(token0, token1,
                             pool_address=pool_address,
                             liquidity=liquidity,
                             pool_type='v3')

                    G.add_edge(token1, token0,
                             pool_address=pool_address,
                             liquidity=liquidity,
                             pool_type='v3')
        # except Exception as e:
        #     continue
    
    # Process V2 pools
    for pool in v2_data:
        # try:
            pool_data = json.loads(pool) if isinstance(pool, str) else pool
            
            if pool_data['poolType'] == 'uniswap_v2_like_pool':
                pool_static_info = pool_data['poolState']['poolStaticInfo']
                if pool_static_info['dexExchange'] == 'uniswap':
                    pool_state = pool_data['poolState']['poolState']
                    pool_address = pool_state['poolAddress'].lower()
                    
                    # Extract token addresses
                    token0 = pool_static_info['token0'].lower()
                    token1 = pool_static_info['token1'].lower()
                    
                    # Calculate liquidity from token balances
                    token_balance0 = float(pool_state['tokenBalance0'])/10**int(pool_static_info['token0Decimals'])
                    token_balance1 = float(pool_state['tokenBalance1'])/10**int(pool_static_info['token1Decimals'])
                    liquidity = token_balance0 * token_balance1  # Simple liquidity measure
                    
                    # Add nodes if they don't exist
                    if not G.has_node(token0):
                        G.add_node(token0)
                    if not G.has_node(token1):
                        G.add_node(token1)
                    
                    # Add edge between tokens with pool information
                    # MultiDiGraph already handles multiple edges between the same token pair
                    G.add_edge(token0, token1,
                             pool_address=pool_address,
                             liquidity=liquidity,
                             pool_type='v2')
                    
                    G.add_edge(token1, token0,
                             pool_address=pool_address,
                             liquidity=liquidity,
                             pool_type='v2')
        # except Exception as e:
        #     continue
    
    return G

def main():
    """Main function to process pool data and create token graph"""
    try:
        # --- Get script directory to build robust paths ---
        SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
        # PARENT_DIR = os.path.dirname(SCRIPT_DIR) # This would be /home/<USER>/crypto-arbitrage-max-flow

        block_number = 21974203 # Restored block_number

        # File paths using absolute paths or robust relative paths
        v2_file_path = '/home/<USER>/crypto-arbitrage-max-flow/balance/prices/block-21974203/filtered-v2pools.json'
        v3_file_path = '/home/<USER>/crypto-arbitrage-max-flow/balance/prices/block-21974203/filtered-v3pools.json'
        price_file_path = '/home/<USER>/crypto-arbitrage-max-flow/balance/prices/block-21974203/tokens-quotes.json'
        
        # Assuming node_name.txt is in the same directory as the 'prices' folder, i.e., 'balance/'
        # node_name_file = os.path.join(SCRIPT_DIR, 'node_name.txt')
        # If node_name.txt is in /home/<USER>/crypto-arbitrage-max-flow/node_name.txt then use:
        node_name_file = '/home/<USER>/crypto-arbitrage-max-flow/balance/prices/node_name.txt'

        output_graph_dir = os.path.join(SCRIPT_DIR, 'graphs') # Output graphs to balance/graphs/
        os.makedirs(output_graph_dir, exist_ok=True)

        # Load token names
        token_names = load_token_names(node_name_file)
        print(f"Loaded {len(token_names)} token names")
        
        # Define token pairs to analyze using addresses
        token_pairs = [
            ('******************************************', '******************************************'),  # USDT-OHM
        ]
        
        print("\nProcessing token pairs:")
        for token0_address, token1_address in token_pairs:
            token0_name = token_names.get(token0_address.lower(), "Unknown")
            token1_name = token_names.get(token1_address.lower(), "Unknown")
            print(f"{token0_name}({token0_address})-{token1_name}({token1_address})")
        
        # Parameters
        k_v3 = 5 # For calculate_exchange_rates_v3

        # Parameters for the refined V2 calculation
        v2_percentage_steps_config = [0.001, 0.005, 0.01, 0.02, 0.05, 0.1, 0.15, 0.2]
        v2_num_current_layers_config = 1


        # Load token prices
        token_prices = load_token_prices(price_file_path)
        if not token_prices:
            print("Warning: Token prices could not be loaded. ETH value calculations might be affected.")
        
        # Load all pool data
        v2_data = load_data_from_json(v2_file_path)
        v3_data = load_data_from_json(v3_file_path)
        
        # Build initial graph
        initial_graph = build_initial_graph(v3_data, v2_data)
        print(f"\nInitial graph has {initial_graph.number_of_nodes()} nodes and {initial_graph.number_of_edges()} edges")
        
        # Find relevant pools
        # The extra empty strings for ranked_pools and top_n were likely placeholders, removed if not used.
        relevant_pools = find_relevant_pools(initial_graph, token_pairs, select=False)
        print(f"Found {len(relevant_pools)} relevant pools")
        
        # Process V3 pools
        filtered_v3_data = load_data_from_json(v3_file_path, relevant_pools)
        v3_pool_rates, v3_skipped_pools = calculate_exchange_rates_v3(
            filtered_v3_data, 
            k_v3, # Use k_v3
            token_prices, 
            liquidity_threshold=1
        )
        print(f"V3 pool rates processed: {len(v3_pool_rates)}, V3 pools skipped: {v3_skipped_pools}")
        
        # Process V2 pools using the refined function
        # Ensure 'calculate_exchange_rates_v2' in your file IS the refined version
        v2_pool_segments, v2_skipped_pools_processing = calculate_exchange_rates_v2(
            v2_data, # Pass all V2 data, filtering will happen next
            token_prices,
            percentage_steps=v2_percentage_steps_config,
            num_current_percentage_layers=v2_num_current_layers_config
        )
        print(f"V2 pool segments generated: {len(v2_pool_segments)}, V2 pools skipped during processing: {v2_skipped_pools_processing}")
        
        # Filter V2 pool segments based on relevant_pools (original pool_id)
        relevant_v2_pool_segments = [
            segment for segment in v2_pool_segments
            if segment['pool_id'].lower() in relevant_pools
        ]
        print(f"Relevant V2 pool segments after filtering: {len(relevant_v2_pool_segments)}")
        
        # Create final graph
        # Note: eth_trade_amount is removed as it's not used by the refined V2 logic in create_token_graph
        final_graph = create_token_graph(
            v3_pool_rates,
            relevant_v2_pool_segments,
            token_prices # Pass token_prices as token_prices_eth
        )
        
        # Save graph
        pair_identifier = '_'.join(
            f"{token_names.get(p[0].lower(), p[0][-6:])}_{token_names.get(p[1].lower(), p[1][-6:])}" # Fallback to last 6 chars if name unknown
            for p in token_pairs
        )
        graph_output_base_path = os.path.join(output_graph_dir, f'{block_number}_pairs_capacity_{pair_identifier}')
        
        graph_file, map_file, balance_file = print_graph(
            final_graph, 
            graph_output_base_path,
            token_names,
            filtered_v3_data # This is used by print_graph for V3 pool balance map logic
        )
        
        # Final summary
        print("\nSummary:")
        print(f"Processed {len(v3_pool_rates)} V3 pool rates, V3 pools skipped: {v3_skipped_pools}")
        print(f"Processed {len(relevant_v2_pool_segments)} relevant V2 pool segments (from {len(v2_pool_segments)} generated).")
        print(f"Original V2 pools skipped during segment generation: {v2_skipped_pools_processing}")
        print(f"Created graph with {len(final_graph.nodes())} nodes and {len(final_graph.edges())} edges")
        print(f"Saved output to {graph_file}, {map_file}, {balance_file}")
        
    except FileNotFoundError as e:
        print(f"Error: File not found - {e}")
        traceback.print_exc()
    except Exception as e:
        print(f"An unexpected error occurred: {e}") # Catch other potential errors
        traceback.print_exc()

def load_token_names(file_path):
    token_names = {}
    try:
        with open(file_path, 'r') as file:
            for line in file:
                parts = line.strip().split()
                if len(parts) >= 2:
                    # Note: address is in the second column
                    address = parts[1].lower()
                    name = parts[0]
                    token_names[address] = name
        return token_names
    except Exception as e:
        print(f"Error loading token names: {e}")
        return {}

if __name__ == "__main__":
    main()
