import math
import copy
from decimal import Decima<PERSON>, getcontext, ROUND_DOWN, ROUND_HALF_UP

# Set ultra-high precision for financial calculations
getcontext().prec = 256  # Increase the precision even further

EPSILON = Decimal('1e-18')  # A small threshold for floating-point precision


def price_from_tick(tick: int) -> Decimal:
    """
    Calculate price as 1.0001 raised to the power of the tick.
    This is the Uniswap V3 price formula.
    """
    return Decimal('1.0001') ** Decimal(tick)


def tick_from_price(price: Decimal) -> int:
    """
    Calculate the tick value corresponding to a given price using the formula:
    tick = log(price) / log(1.0001).
    Ensures proper flooring and handles small values by using epsilon.
    """
    epsilon = Decimal('1e-128')  # Threshold for very small prices
    correction_value = Decimal('1e-18')  # Prevent division by zero for very small prices

    if price <= epsilon:
        price = correction_value  # Correct the value to prevent mathematical errors

    # Return the floor of the logarithm of price divided by the logarithm of 1.0001
    return int(math.floor(math.log(float(price)) / math.log(1.0001)))


def sqrt_price_q96_to_decimal(sqrt_price_x96: int) -> Decimal:
    """
    Convert Q64.96 sqrt price (fixed-point 96-bit integer) to a Decimal with high precision.
    This conversion is necessary because most of the calculations in Uniswap are done using
    a 96-bit fixed-point representation.
    """
    return Decimal(sqrt_price_x96) / (Decimal(2) ** 96)


def decimal_to_sqrt_price_q96(sqrt_price: Decimal) -> int:
    """
    Convert a Decimal representing the square root price back to a Q64.96 fixed-point integer
    by multiplying with 2^96 and truncating the result.
    """
    return int((sqrt_price * (Decimal(2) ** 96)).to_integral(rounding=ROUND_DOWN))


# ----- Functions for token0 -> token1 (zeroForOne swap) -----
def calculate_dx(L: Decimal, sp_current: Decimal, sp_target: Decimal) -> Decimal:
    """
    Calculate the amount of token0 (dx) required to move the price from sp_current to sp_target.
    This function is used for token0 -> token1 (zeroForOne) swaps.
    """
    if sp_current < EPSILON or sp_target < EPSILON:
        print(f"sp_current or sp_target is zero {sp_target} & {sp_current}")

        sp_current = EPSILON
        sp_target = EPSILON

    return L * (sp_current - sp_target) / (sp_current * sp_target)


def calculate_dy(L: Decimal, sp_current: Decimal, sp_target: Decimal) -> Decimal:
    """
    Calculate the amount of token1 (dy) received when moving the price from sp_current to sp_target.
    This is used for token0 -> token1 (zeroForOne) swaps.
    """
    if sp_current < EPSILON or sp_target < EPSILON:
        print(f"sp_current or sp_target is zero {sp_target} & {sp_current}")
        sp_current = EPSILON
        sp_target = EPSILON

    return L * (sp_current - sp_target)


def get_next_sqrt_price(L: Decimal, sp: Decimal, dx_net: Decimal) -> Decimal:
    """
    Calculate the new square root price after consuming a certain amount of token0 (dx_net).
    Ensures the price is updated accurately while avoiding division by zero.
    """
    if L < EPSILON:
        print("L is zero")
        L = EPSILON

    numerator = L * sp
    denominator = L + dx_net * sp
    return (numerator / denominator).quantize(Decimal('1e-18'), rounding=ROUND_DOWN)


# ----- Functions for token1 -> token0 (oneForZero swap) -----
def calculate_dy_one_for_zero(L: Decimal, sp_current: Decimal, sp_target: Decimal) -> Decimal:
    """
    Calculate the amount of token1 (dy) required to move the price from sp_current to sp_target
    when performing a token1 -> token0 (oneForZero) swap.
    """
    return L * (sp_target - sp_current)


def calculate_dx_one_for_zero(L: Decimal, sp_current: Decimal, sp_target: Decimal) -> Decimal:
    """
    Calculate the amount of token0 (dx) output when moving the price from sp_current to sp_target
    during a token1 -> token0 (oneForZero) swap.
    """
    return L * ((Decimal(1) / sp_current) - (Decimal(1) / sp_target))


def get_next_sqrt_price_one_for_zero(L: Decimal, sp: Decimal, dy_net: Decimal) -> Decimal:
    """
    Calculate the new square root price after consuming a certain amount of token1 (dy_net) in a
    token1 -> token0 (oneForZero) swap.
    """
    return (sp + (dy_net / L)).quantize(Decimal('1e-18'), rounding=ROUND_DOWN)


# ----- Simulation of token0 -> token1 swap (zeroForOne) -----
def simulate_swap_zero_for_one(pool_data: dict, amount_in: int, fee: Decimal):
    """
    Simulate a Uniswap V3 exactInput swap (token0 -> token1) using the given pool data,
    amount of token0 to swap, and fee rate. The function calculates the amount of token1
    output, the amount of token0 consumed, and any fees collected during the swap.
    """
    pool_state = pool_data["poolState"]["poolState"]
    fee_rate = fee
    sp_current = sqrt_price_q96_to_decimal(int(pool_state["sqrtPriceX96"]))

    if sp_current <= Decimal('1e-18'):
        sp_current = Decimal('1e-18')

    L = Decimal(pool_state["liquidity"])  # Liquidity in the pool
    amount_remaining = Decimal(amount_in)  # Amount of token0 remaining for the swap
    token1_out = Decimal(0)  # Token1 output after the swap
    total_fee = Decimal(0)  # Total fee collected during the swap

    # Process ticks below current tick (descending order)
    ticks = sorted(
        [t for t in pool_state["tickBitMap"] if int(t[0]) < int(pool_state["currentTick"])],
        key=lambda x: -int(x[0])  # Sort ticks in descending order
    )

    for tick_info in ticks:
        tick_idx = int(tick_info[0])
        tick_data = tick_info[1]
        liquidity_net = Decimal(tick_data["liquidityNet"])
        initialized = tick_data["initialized"]

        if amount_remaining <= 0:
            break

        # Calculate the price corresponding to this tick
        sp_target = (Decimal('1.0001') ** (Decimal(tick_idx) / 2)).quantize(Decimal('1e-18'), rounding=ROUND_DOWN)
        if sp_target >= sp_current:
            continue

        dx_net_needed = calculate_dx(L, sp_current, sp_target)  # Calculate token0 required
        dx_gross_needed = dx_net_needed / (Decimal(1) - fee_rate)  # Gross token0 needed with fees

        if amount_remaining >= dx_gross_needed:
            token1_out += calculate_dy(L, sp_current, sp_target)  # Calculate token1 output
            total_fee += dx_gross_needed - dx_net_needed  # Calculate fees
            amount_remaining -= dx_gross_needed
            sp_current = sp_target

            if initialized:
                L -= liquidity_net  # Adjust liquidity when crossing a tick
        else:
            dx_net_available = amount_remaining * (Decimal(1) - fee_rate)
            sp_new = get_next_sqrt_price(L, sp_current, dx_net_available)
            token1_out += calculate_dy(L, sp_current, sp_new)
            total_fee += amount_remaining - dx_net_available
            amount_remaining = Decimal(0)
            sp_current = sp_new

    # Handle the final partial swap if input still remains
    if amount_remaining > 0:
        dx_net_available = amount_remaining * (Decimal(1) - fee_rate)
        sp_new = get_next_sqrt_price(L, sp_current, dx_net_available)
        token1_out += calculate_dy(L, sp_current, sp_new)
        total_fee += amount_remaining - dx_net_available
        sp_current = sp_new

    if sp_current == 0:
        sp_current = Decimal('1e-18')

    # Correct balance calculations (balance is based on liquidity and price)
    balance0 = Decimal(L / sp_current).to_integral(rounding=ROUND_DOWN)
    balance1 = Decimal(L * sp_current).to_integral(rounding=ROUND_DOWN)

    # Update the pool state after the swap
    updated_state = copy.deepcopy(pool_state)
    updated_state["sqrtPriceX96"] = str(decimal_to_sqrt_price_q96(sp_current))
    updated_state["currentTick"] = str(tick_from_price(sp_current ** 2))
    updated_state["liquidity"] = str(int(L))
    updated_state["balance0"] = str(balance0)
    updated_state["balance1"] = str(balance1)

    return {
        "token1_output": token1_out,
        "token0_consumed": Decimal(amount_in) - amount_remaining,
        "fees_collected": total_fee,
        "new_pool_state": updated_state,
        "final_price": sp_current ** 2
    }

# ----- Simulation of token1 -> token0 swap (oneForZero) -----
def simulate_swap_one_for_zero(pool_data: dict, amount_in: int, fee: Decimal):
    """
    Simulate a Uniswap V3 exactInput swap (token1 -> token0) using the given pool data,
    amount of token1 to swap, and fee rate. The function calculates the amount of token0
    output, the amount of token1 consumed, and any fees collected during the swap.
    """
    pool_state = pool_data["poolState"]["poolState"]
    fee_rate = fee
    sp_current = sqrt_price_q96_to_decimal(int(pool_state["sqrtPriceX96"]))

    if sp_current <= Decimal('1e-18'):
        sp_current = Decimal('1e-18')

    L = Decimal(pool_state["liquidity"])  # Liquidity in the pool
    amount_remaining = Decimal(amount_in)  # Amount of token1 remaining for the swap
    token0_out = Decimal(0)  # Token0 output after the swap
    total_fee = Decimal(0)  # Total fee collected during the swap

    # Process ticks above current tick (ascending order)
    ticks = sorted(
        [t for t in pool_state["tickBitMap"] if int(t[0]) > int(pool_state["currentTick"])],
        key=lambda x: int(x[0])  # Sort ticks in ascending order
    )

    for tick_info in ticks:
        tick_idx = int(tick_info[0])
        tick_data = tick_info[1]
        liquidity_net = Decimal(tick_data["liquidityNet"])
        initialized = tick_data["initialized"]

        if amount_remaining <= 0:
            break

        # Calculate the price corresponding to this tick
        sp_target = (Decimal('1.0001') ** (Decimal(tick_idx) / 2)).quantize(Decimal('1e-18'), rounding=ROUND_DOWN)
        if sp_target <= sp_current:
            continue

        dy_net_needed = calculate_dy_one_for_zero(L, sp_current, sp_target)  # Calculate token1 required
        dy_gross_needed = dy_net_needed / (Decimal(1) - fee_rate)  # Gross token1 needed with fees

        if amount_remaining >= dy_gross_needed:
            token0_out += calculate_dx_one_for_zero(L, sp_current, sp_target)  # Calculate token0 output
            total_fee += dy_gross_needed - dy_net_needed  # Calculate fees
            amount_remaining -= dy_gross_needed
            sp_current = sp_target

            if initialized:
                L += liquidity_net  # Adjust liquidity when crossing a tick
        else:
            dy_net_available = amount_remaining * (Decimal(1) - fee_rate)
            sp_new = get_next_sqrt_price_one_for_zero(L, sp_current, dy_net_available)
            token0_out += calculate_dx_one_for_zero(L, sp_current, sp_new)
            total_fee += amount_remaining - dy_net_available
            amount_remaining = Decimal(0)
            sp_current = sp_new

    # Handle the final partial swap if input still remains
    if amount_remaining > 0:
        dy_net_available = amount_remaining * (Decimal(1) - fee_rate)
        sp_new = get_next_sqrt_price_one_for_zero(L, sp_current, dy_net_available)
        token0_out += calculate_dx_one_for_zero(L, sp_current, sp_new)
        total_fee += amount_remaining - dy_net_available
        sp_current = sp_new

    # Correct balance calculations (balance is based on liquidity and price)
    balance0 = Decimal(L / sp_current).to_integral(rounding=ROUND_DOWN)
    balance1 = Decimal(L * sp_current).to_integral(rounding=ROUND_DOWN)

    # Update the pool state after the swap
    updated_state = copy.deepcopy(pool_state)
    updated_state["sqrtPriceX96"] = str(decimal_to_sqrt_price_q96(sp_current))
    updated_state["currentTick"] = str(tick_from_price(sp_current ** 2))
    updated_state["liquidity"] = str(int(L))
    updated_state["balance0"] = str(balance0)
    updated_state["balance1"] = str(balance1)

    return {
        "token0_output": token0_out,
        "token1_consumed": Decimal(amount_in) - amount_remaining,
        "fees_collected": total_fee,
        "new_pool_state": updated_state,
        "final_price": sp_current ** 2
    }
