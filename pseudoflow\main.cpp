#include "network_simplex.h"
#include "pseudoflow.h"
#include <chrono>
#include <iostream>
#include <cstdlib>
#include <string>

int main(int argc, char** argv) {
    if (argc < 4) {
        std::cout << "Usage: " << argv[0] << " <filename> <source> <sink>\n";
        return 1;
    }
    
    // Parse command-line arguments.
    std::string filename(argv[1]);
    uint s = std::stoi(argv[2]);
    uint t = std::stoi(argv[3]);

    std::cout << "Reading graph from file: " << filename << "\n";

    Pseudoflow solver;
    Mincost mincost_solver;

    // Time the graph reading process.
    auto t1 = std::chrono::high_resolution_clock::now();
    solver.readGraphFromFile(filename, s, t);
    mincost_solver.readGraphFromFile(filename, s, t);
    auto t2 = std::chrono::high_resolution_clock::now();

    double graph_load_time = std::chrono::duration<double>(t2 - t1).count();
    std::cout << "Graph loaded: " << solver.numNodes << " nodes, " 
              << solver.numArcs << " arcs in " << graph_load_time << " seconds\n";

    // Time the flow computation.
    auto initStart = std::chrono::high_resolution_clock::now();
    solver.simpleInitialization();
    llint min_cut_value = solver.mincut();
    llint min_cost_value = mincost_solver.mincost(min_cut_value);
    auto flowEnd = std::chrono::high_resolution_clock::now();

    double flow_time = std::chrono::duration<double>(flowEnd - initStart).count();

    std::cout << "Max flow value      : " << min_cut_value << "\n";
    std::cout << "Min cost value      : " << min_cost_value << "\n";
    std::cout << "Total time          : " << flow_time << " seconds\n";

    return 0;
}
