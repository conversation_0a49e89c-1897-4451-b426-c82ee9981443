#include "pseudoflow.h"

// Type aliases for convenience.
using uint = unsigned int;
using llint = long long int;
using ullint = unsigned long long int;

Pseudoflow::Pseudoflow() {}

// Reads the graph from file. The file is assumed to have lines with four
// unsigned integers: from, to, capacity, cost (the cost is ignored).
// The user-supplied source and sink are assumed to be 0-based.
void Pseudoflow::readGraphFromFile(const std::string &filename, uint s, uint t) {
    std::ifstream infile(filename);
    if (!infile) {
        std::cerr << "Error: cannot open file " << filename << "\n";
        std::exit(1);
    }
    // Store the user-provided source & sink (internally 1-based)
    source = s + 1;
    sink   = t + 1;

    std::string line;
    uint from, to, capacity, cost;
    uint maxNode = 0;
    numArcs = 0;

    // First pass: count valid arc lines and track maximum 0-based node id.
    while (std::getline(infile, line)) {
        std::istringstream iss(line);
        if (iss >> from >> to >> capacity >> cost) {
            ++numArcs;
            maxNode = std::max({maxNode, from, to});
        }
    }
    numNodes = maxNode + 1;  // because node IDs are 0-based

    // Allocate containers.
    nodes.resize(numNodes);
    strongRoots.resize(numNodes);
    labelCount.assign(numNodes, 0);
    arcs.resize(numArcs);

    // Initialize nodes.
    for (uint i = 0; i < numNodes; ++i) {
        nodes[i].number = i + 1;  // internal numbering is 1-based
        nodes[i].label = 0;
        nodes[i].excess = 0;
        // Other fields are already default‐initialized.
    }
    // Arcs are default‐constructed; we will fill them in below.

    // Rewind file for second pass.
    infile.clear();
    infile.seekg(0);

    int first = 0;
    int last  = numArcs - 1;
    while (std::getline(infile, line)) {
        std::istringstream iss(line);
        if (iss >> from >> to >> capacity >> cost) {
            if ((from + to) % 2) {
                arcs[first].from = &nodes[from];
                arcs[first].to = &nodes[to];
                arcs[first].capacity = capacity;
                ++first;
            } else {
                arcs[last].from = &nodes[from];
                arcs[last].to = &nodes[to];
                arcs[last].capacity = capacity;
                --last;
            }
            // Increment adjacent counters.
            nodes[from].numAdjacent++;
            nodes[to].numAdjacent++;
        }
    }
    infile.close();

    // Reserve space for each node’s outOfTree arcs.
    for (uint i = 0; i < numNodes; ++i) {
        nodes[i].outOfTree.reserve(nodes[i].numAdjacent);
    }

    // Final pass: add arcs to the proper node’s outOfTree vector.
    // (Here we follow the original logic to treat arcs incident to source/sink specially.)
    for (uint i = 0; i < numArcs; i++) {
        uint toNum = arcs[i].to->number;     // 1-based
        uint fromNum = arcs[i].from->number;   // 1-based
        capacity = arcs[i].capacity;
        if (!((source == toNum) || (sink == fromNum) || (fromNum == toNum))) {
            if ((source == fromNum) && (toNum == sink)) {
                arcs[i].flow = capacity;
            }
            else if (fromNum == source) {
                nodes[fromNum - 1].outOfTree.push_back(&arcs[i]);
            }
            else if (toNum == sink) {
                nodes[toNum - 1].outOfTree.push_back(&arcs[i]);
            }
            else {
                nodes[fromNum - 1].outOfTree.push_back(&arcs[i]);
            }
        }
    }
}

//-----------------------------------------------------------
// simpleInitialization performs the initial flow push from
// the source and sink, sets up node labels and populates the
// initial strong roots buckets.
void Pseudoflow::simpleInitialization() {
    // Process source outOfTree arcs.
    Node &src = nodes[source - 1];
    for (Arc* arcPtr : src.outOfTree) {
        arcPtr->flow = arcPtr->capacity;
        arcPtr->to->excess += arcPtr->capacity;
    }
    // Process sink outOfTree arcs.
    Node &snk = nodes[sink - 1];
    for (Arc* arcPtr : snk.outOfTree) {
        arcPtr->flow = arcPtr->capacity;
        arcPtr->from->excess -= arcPtr->capacity;
    }
    src.excess = 0;
    snk.excess = 0;
    // Set label 1 for nodes with positive excess and add them to bucket 1.
    for (auto &node : nodes) {
        if (node.excess > 0) {
            node.label = 1;
            labelCount[1]++;
            strongRoots[1].nodes.push_back(&node);
        }
    }
    // Set the source and sink labels.
    nodes[source - 1].label = numNodes;
    nodes[sink - 1].label = 0;
    labelCount[0] = (numNodes - 2) - labelCount[1];
}
//-----------------------------------------------------------

// The following functions implement the core pseudoflow operations.
// (They are a near-direct translation of the original C code.)

void Pseudoflow::addRelationship(Node* newParent, Node* child) {
    child->parent = newParent;
    newParent->children.push_back(child);
}

void Pseudoflow::breakRelationship(Node* oldParent, Node* child) {
    child->parent = nullptr;
    auto &vec = oldParent->children;
    vec.erase(std::remove(vec.begin(), vec.end(), child), vec.end());
}

void Pseudoflow::merge(Node* parent, Node* child, Arc* newArc) {
    Arc* oldArc;
    Node* current = child;
    Node* newParent = parent;
    while (current->parent) {
        oldArc = current->arcToParent;
        current->arcToParent = newArc;
        Node* oldParent = current->parent;
        breakRelationship(oldParent, current);
        addRelationship(newParent, current);
        newParent = current;
        current = oldParent;
        newArc = oldArc;
        newArc->direction = 1 - newArc->direction;
    }
    current->arcToParent = newArc;
    addRelationship(newParent, current);
}

void Pseudoflow::pushUpward(Arc* currentArc, Node* child, Node* parent, uint resCap) {
    if (resCap >= static_cast<uint>(child->excess)) {
        parent->excess += child->excess;
        currentArc->flow += child->excess;
        child->excess = 0;
        return;
    }
    currentArc->direction = 0;
    parent->excess += resCap;
    child->excess -= resCap;
    currentArc->flow = currentArc->capacity;
    parent->outOfTree.push_back(currentArc);
    breakRelationship(parent, child);
    strongRoots[child->label].nodes.push_back(child);
}

void Pseudoflow::pushDownward(Arc* currentArc, Node* child, Node* parent, uint flow) {
    if (flow >= static_cast<uint>(child->excess)) {
        parent->excess += child->excess;
        currentArc->flow -= child->excess;
        child->excess = 0;
        return;
    }
    currentArc->direction = 1;
    child->excess -= flow;
    parent->excess += flow;
    currentArc->flow = 0;
    parent->outOfTree.push_back(currentArc);
    breakRelationship(parent, child);
    strongRoots[child->label].nodes.push_back(child);
}

void Pseudoflow::pushExcess(Node* strongRoot) {
    Node* current = strongRoot;
    Node* parent = nullptr;
    Arc* arcToParent = nullptr;
    int prevEx = 1;
    while (current->excess && current->parent) {
        parent = current->parent;
        prevEx = parent->excess;
        arcToParent = current->arcToParent;
        if (arcToParent->direction) {
            pushUpward(arcToParent, current, parent,
                        arcToParent->capacity - arcToParent->flow);
        } else {
            pushDownward(arcToParent, current, parent, arcToParent->flow);
        }
        current = parent;
    }
    if (current->excess > 0 && prevEx <= 0)
        strongRoots[current->label].nodes.push_back(current);
}

// findWeakNode scans the outOfTree arcs of a strong node to find an arc 
// whose other endpoint has label equal to (highestStrongLabel - 1).
// (It returns the found arc and sets weakNode to point to that endpoint.)
Arc* Pseudoflow::findWeakNode(Node* strongNode, Node*& weakNode) {
    size_t size = strongNode->outOfTree.size();
    for (size_t i = strongNode->nextArc; i < size; ++i) {
        if (strongNode->outOfTree[i]->to->label == highestStrongLabel - 1) {
            strongNode->nextArc = i;
            weakNode = strongNode->outOfTree[i]->to;
            Arc* found = strongNode->outOfTree[i];
            // Remove the found arc from the vector (swap–pop technique)
            std::swap(strongNode->outOfTree[i], strongNode->outOfTree.back());
            strongNode->outOfTree.pop_back();
            return found;
        } else if (strongNode->outOfTree[i]->from->label == highestStrongLabel - 1) {
            strongNode->nextArc = i;
            weakNode = strongNode->outOfTree[i]->from;
            Arc* found = strongNode->outOfTree[i];
            std::swap(strongNode->outOfTree[i], strongNode->outOfTree.back());
            strongNode->outOfTree.pop_back();
            return found;
        }
    }
    strongNode->nextArc = strongNode->outOfTree.size();
    return nullptr;
}

void Pseudoflow::checkChildren(Node* curNode) {
    // Instead of following a linked list via nextScan pointers, we use an index
    while (curNode->nextScanIndex < curNode->children.size()) {
        if (curNode->children[curNode->nextScanIndex]->label == curNode->label)
            return;
        curNode->nextScanIndex++;
    }
    labelCount[curNode->label]--;
    curNode->label++;
    labelCount[curNode->label]++;
    curNode->nextArc = 0;
}

void Pseudoflow::processRoot(Node* strongRoot) {
    Node* strongNode = strongRoot;
    strongRoot->nextScanIndex = 0; // start scanning from beginning of children
    Node* weakNode = nullptr;
    Arc* outArc = findWeakNode(strongRoot, weakNode);
    if (outArc) {
        merge(weakNode, strongNode, outArc);
        pushExcess(strongRoot);
        return;
    }
    checkChildren(strongRoot);
    while (strongNode) {
        while (strongNode->nextScanIndex < strongNode->children.size()) {
            Node* temp = strongNode->children[strongNode->nextScanIndex];
            strongNode->nextScanIndex++;
            strongNode = temp;
            strongNode->nextScanIndex = 0;
            outArc = findWeakNode(strongNode, weakNode);
            if (outArc) {
                merge(weakNode, strongNode, outArc);
                pushExcess(strongRoot);
                return;
            }
            checkChildren(strongNode);
        }
        strongNode = strongNode->parent;
        if (strongNode)
            checkChildren(strongNode);
    }
    strongRoots[strongRoot->label].nodes.push_back(strongRoot);
    ++highestStrongLabel;
}

Node* Pseudoflow::getHighestStrongRoot() {
    for (uint i = highestStrongLabel; i > 0; --i) {
        if (!strongRoots[i].nodes.empty()) {
            highestStrongLabel = i;
            if (labelCount[i - 1] > 0) {
                Node* strongRoot = strongRoots[i].nodes.back();
                strongRoots[i].nodes.pop_back();
                return strongRoot;
            }
            while (!strongRoots[i].nodes.empty()) {
                Node* strongRoot = strongRoots[i].nodes.back();
                strongRoots[i].nodes.pop_back();
                liftAll(strongRoot);
            }
        }
    }
    if (strongRoots[0].nodes.empty())
        return nullptr;
    while (!strongRoots[0].nodes.empty()) {
        Node* strongRoot = strongRoots[0].nodes.back();
        strongRoots[0].nodes.pop_back();
        strongRoot->label = 1;
        labelCount[0]--;
        labelCount[1]++;
        strongRoots[strongRoot->label].nodes.push_back(strongRoot);
    }
    highestStrongLabel = 1;
    Node* strongRoot = strongRoots[1].nodes.back();
    strongRoots[1].nodes.pop_back();
    return strongRoot;
}

// liftAll lifts a subtree by setting the label of every node in the tree
// to numNodes.
void Pseudoflow::liftAll(Node* rootNode) {
    Node* current = rootNode;
    current->nextScanIndex = 0;
    labelCount[current->label]--;
    current->label = numNodes;
    while (current) {
        while (current->nextScanIndex < current->children.size()) {
            Node* temp = current->children[current->nextScanIndex];
            current->nextScanIndex++;
            current = temp;
            current->nextScanIndex = 0;
            labelCount[current->label]--;
            current->label = numNodes;
        }
        current = current->parent;
    }
}

// Runs phase 1 of the pseudoflow algorithm.
void Pseudoflow::pseudoflowPhase1() {
    Node* strongRoot = nullptr;
    while ((strongRoot = getHighestStrongRoot())) {
        processRoot(strongRoot);
    }
}

llint Pseudoflow::mincut() {
    Node* strongRoot = nullptr;
    while ((strongRoot = getHighestStrongRoot())) {
        processRoot(strongRoot);
    }
    
    // After phase1 the nodes are partitioned by their labels.
    // We use gap = numNodes as the threshold.
    llint mincut = 0;
    for (uint i = 0; i < numArcs; ++i) {
        // If the arc goes from a node with label >= numNodes
        // to one with label < numNodes, then it crosses the min cut.
        if (arcs[i].from->label >= numNodes && arcs[i].to->label < numNodes)
            mincut += arcs[i].capacity;
    }
    return mincut;
}

