# Arbitrage Path Detection and Simulation

This project provides tools to build a token-exchange graph, detect all simple paths up to *k* hops using an optimized DFS, and simulate those paths to evaluate potential arbitrage opportunities.

## 📁 Project Structure

**0. `process_v3_data.py`** (optional)

**Purpose:**

* Split wide V3 ticks in the data.
* Test appropriate values for `max_price_diff_pct`, `max_distance_from_current`, and `max_splits_per_tick` across various amounts and token pairs.

**Usage:**

```bash
python process_v3_data.py
```

**1. `graph.py`**

**Purpose:**

* Construct the token graph and create node mappings.
* Compute per-edge weights and balances, as well as directional liquidity capacities.

**Usage:**

```bash
python graph.py --split-ticks
```

**1.1 `merge_ticks.py`** (optional)

**Purpose:**

* Merge ticks that have very small capacity.

**Usage:**

```bash
python merge_ticks.py
```

**2. `dfs.cpp` & `run.py`**

**Purpose:**

* Detect all simple paths within *k* hops using a custom C++ DFS implementation.

**Usage:**

```bash
# Compile the DFS implementation
g++ -O3 dfs.cpp -o dfs

# Run via the Python wrapper
python run.py
```

**3. `path_allocation.py`**

**Purpose:**

* Allocate input amounts across the detected paths.

**Usage:**

```bash
python path_allocation.py --split-ticks
```

**4. `path_simulation.py`**

**Purpose:**

* Simulate token swaps along each path to compute expected profit or output.

**Usage:**

```bash
python path_simulation.py
```
