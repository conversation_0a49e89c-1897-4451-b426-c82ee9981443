#include <vector>
#include <iostream>
#include <fstream>
#include <algorithm>
#include <chrono>  // Add this for timing
#include <iomanip> // Add this for output formatting
#include <sstream>
#include <string>
#include <unordered_set>

class PathEnumerator {
public:
    std::vector<std::vector<int>> all_paths;
    std::chrono::milliseconds enum_duration{0};

private:
    std::vector<std::vector<int>> adj;
    std::vector<bool> visited;
    std::vector<int> current_path;
    int max_length;
    std::unordered_set<std::string> path_signatures;

    // Helper function to create a unique signature for a path
    std::string getPathSignature(const std::vector<int>& path) {
        std::string signature;
        for (int vertex : path) {
            signature += std::to_string(vertex) + ",";
        }
        return signature;
    }

    void dfs(int current, int target, int length) {
        if (length > max_length) return;
        
        // Mark current vertex as visited
        visited[current] = true;
        current_path.push_back(current);

        if (current == target) {
            // Check if this is a new path (not a duplicate)
            std::string signature = getPathSignature(current_path);
            if (path_signatures.find(signature) == path_signatures.end()) {
                path_signatures.insert(signature);
                all_paths.push_back(current_path);
            }
        } else {
            for (int next : adj[current]) {
                // Only visit vertices that haven't been visited yet in this path
                if (!visited[next]) {
                    dfs(next, target, length + 1);
                }
            }
        }

        // Backtrack: remove the current vertex from the path and mark as unvisited
        current_path.pop_back();
        visited[current] = false;
    }

public:
    PathEnumerator(int n) : adj(n), visited(n, false) {}

    void addEdge(int u, int v) {
        adj[u].push_back(v);
    }

    void findAllPaths(int source, int target, int maxLen) {
        auto enum_start = std::chrono::high_resolution_clock::now();
        
        max_length = maxLen;
        all_paths.clear();
        current_path.clear();
        path_signatures.clear();
        std::fill(visited.begin(), visited.end(), false);
        
        dfs(source, target, 0);
        
        auto enum_end = std::chrono::high_resolution_clock::now();
        enum_duration = std::chrono::duration_cast<std::chrono::milliseconds>(enum_end - enum_start);
        std::cout << "Time for enumerating all paths: " << enum_duration.count() << " ms\n";
        std::cout << "Total number of paths enumerated: " << all_paths.size() << "\n";
    }

    auto getEnumDuration() const { return enum_duration; }
};

int main() {
    std::string filename;
    std::cout << "Enter the graph file name: ";
    std::cin >> filename;

    // First pass: count number of vertices
    std::ifstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Could not open file: " << filename << std::endl;
        return 1;
    }

    int max_vertex = -1;
    int from, to;
    std::string line;
    
    // Read each line from the file
    while (std::getline(file, line)) {
        std::istringstream iss(line);
        // Try to read the first two items from each line
        if (iss >> from >> to) {
            max_vertex = std::max(max_vertex, std::max(from, to));
        }
    }
    file.close();

    // Create graph with appropriate size (add 1 because vertices are 0-based)
    PathEnumerator pe(max_vertex + 1);

    // Second pass: read edges
    file.open(filename);
    while (std::getline(file, line)) {
        std::istringstream iss(line);
        // Read only the first two items from each line
        if (iss >> from >> to) {
            pe.addEdge(from, to);
        }
    }
    file.close();

    // Get source and target vertices from user
    int source, target, max_length;
    std::cout << "Enter source vertex: ";
    std::cin >> source;
    std::cout << "Enter target vertex: ";
    std::cin >> target;
    std::cout << "Enter maximum path length: ";
    std::cin >> max_length;

    // Enumerate all paths
    pe.findAllPaths(source, target, max_length);

    // Create output file name
    std::string outfile = "results/all_paths_" + std::to_string(source) + "_" + 
                         std::to_string(target) + "_" + 
                         std::to_string(max_length) + ".txt";
    std::ofstream output(outfile);
    if (!output.is_open()) {
        std::cerr << "Could not create output file: " << outfile << std::endl;
        return 1;
    }

    // Write results
    output << "Path enumeration time: " << pe.getEnumDuration().count() << " milliseconds\n\n";
    output << "Total simple paths enumerated: " << pe.all_paths.size() << "\n\n";
    
    // Print all paths
    output << "All simple paths from " << source << " to " << target << " (max length " << max_length << "):\n\n";
    for (size_t i = 0; i < pe.all_paths.size(); ++i) {
        output << "Path " << (i + 1) << ":\n";
        for (int vertex : pe.all_paths[i]) {
            output << vertex << " ";
        }
        output << "\n\n";
    }

    output.close();
    std::cout << "Results have been saved to: " << outfile << std::endl;

    return 0;
} 