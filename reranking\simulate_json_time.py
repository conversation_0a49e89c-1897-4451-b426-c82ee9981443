import pandas as pd
import json
import os
from web3 import Web3
import math
import copy
import time  # Import time module for measuring execution time
from decimal import Decimal, getcontext, ROUND_DOWN, ROUND_HALF_UP
import re  # Add re import for path extraction
from collections import defaultdict
import argparse

# Add reranking statistics tracking
class RerankingStats:
    def __init__(self):
        self.rounds = 0
        self.path_changes_per_round = []
        self.update_times_per_round = []
        self.sort_times_per_round = []
        self.total_update_time = 0
        self.total_sort_time = 0
    
    def report(self, log_func):
        """Report statistics to the provided log function"""
        log_func("\n" + "="*80)
        log_func("RERANKING STATISTICS")
        log_func("="*80)
        log_func(f"Total reranking rounds: {self.rounds}")
        
        if self.rounds > 0:
            log_func(f"\nPath changes per round:")
            for i, changes in enumerate(self.path_changes_per_round):
                log_func(f"  Round {i+1}: {changes} paths changed")
            
            log_func(f"\nTime spent updating weights/capacities:")
            for i, t in enumerate(self.update_times_per_round):
                log_func(f"  Round {i+1}: {t:.6f} seconds")
            log_func(f"  Total: {self.total_update_time:.6f} seconds")
            
            log_func(f"\nTime spent sorting paths:")
            for i, t in enumerate(self.sort_times_per_round):
                log_func(f"  Round {i+1}: {t:.6f} seconds")
            log_func(f"  Total: {self.total_sort_time:.6f} seconds")
        
        log_func("="*80)

def read_path_enumeration_results(file_path):
    """Extract paths from non-disjoint path file."""
    with open(file_path, 'r') as f:
        content = f.read()
    
    paths = []
    
    path_blocks = re.findall(r"Path \d+:\s*([\d\s]+)", content)
    
    for path in path_blocks:
        paths.append(path.strip())
    
    return {
        'paths': paths
    }

def process_and_save_paths(
    path_file: str,
    node_map_file: str,
    output_dir: str = "results/path_analysis_df",
    name: str = None,
    is_disjoint: bool = False,
    num_paths: int = None
) -> pd.DataFrame:
    """
    Process paths from a path file, analyze them, and save the results.
    
    Args:
        path_file: Path to the file containing path results
        node_map_file: Path to the node map file
        output_dir: Directory to save the output CSV file
        name: Name for the output file (derived from path_file if None)
        is_disjoint: Whether the paths are disjoint
        num_paths: Number of paths to analyze (None for all)
    
    Returns:
        DataFrame with token path information
    """
    # Create log directory if it doesn't exist
    os.makedirs('results/log', exist_ok=True)
    process_log_path = f"results/log/process_paths_{os.path.basename(path_file)}.txt"
    process_log = open(process_log_path, "w", encoding="utf-8")
    
    # Function to log messages
    def log_process(message):
        process_log.write(f"{message}\n")
    
    # Use filename as name if not provided
    if name is None:
        name = os.path.basename(path_file).split('.')[0]
    
    # Read node map
    with open(node_map_file, 'r') as f:
        node_map = {}
        for line in f:
            parts = line.strip().split()
            if len(parts) >= 2:
                index, token = parts[0], parts[1]
                node_map[int(index)] = token
    
    # Extract paths
    paths_data = read_path_enumeration_results(path_file)
    paths = paths_data['paths']
    
    # Limit number of paths if specified
    if num_paths is not None:
        paths = paths[:num_paths]
    
    # Prepare data for DataFrame
    data = []
    
    for i, path_str in enumerate(paths):
        # Convert node IDs to token addresses
        token_path = [node_map[int(node_id)] for node_id in path_str.split()]
        
        # Get clean token path
        clean_path = []
    
        # Extract base tokens (removing any suffixes)
        for token in token_path:
            base_token = token.split('_')[0] if '_' in token else token
            if base_token not in clean_path:
                clean_path.append(base_token)
        
        # Add to data
        data.append({
            'path_id': i + 1,
            'path': ' -> '.join(clean_path),
            'token_sequence': clean_path
        })
    
    # Create DataFrame
    df = pd.DataFrame(data)
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Save to CSV
    output_file = f'{output_dir}/path_analysis_{name}.csv'
    df.to_csv(output_file, index=False)
    
    log_process(f"Path analysis saved to {output_file}")
    log_process(f"Total paths analyzed: {len(df)}")
    
    # Close log file
    process_log.close()
    return df

# ----- SIMULATION V2-----
class V2Simulator:
    """Simulator for Uniswap V2 pools."""
    
    def __init__(self, node_rpc_url: str) -> None:
        self.web3 = Web3(Web3.HTTPProvider(node_rpc_url))
        
        if not self.web3.is_connected():
            raise Exception("Failed to connect to the network")
        
        # Router contract for V2 pools
        V2_ROUTER2_ADDRESS = "0x7a250d5630B4cF539739dF2C5dAcb4c659F2488D"
        V2_ROUTER2_ABI = [
            {
                "inputs": [
                    {"internalType": "uint256", "name": "amountIn", "type": "uint256"},
                    {"internalType": "uint256", "name": "reserveIn", "type": "uint256"},
                    {"internalType": "uint256", "name": "reserveOut", "type": "uint256"},
                ],
                "name": "getAmountOut",
                "outputs": [
                    {"internalType": "uint256", "name": "amountOut", "type": "uint256"}
                ],
                "stateMutability": "pure",
                "type": "function",
            }
        ]
        
        self.router_contract = self.web3.eth.contract(
            address=V2_ROUTER2_ADDRESS, abi=V2_ROUTER2_ABI
        )
    
    def get_amount_out(self, in_amount: int, in_reserve: int, out_reserve: int) -> int:
        """Calculate output amount for V2 pools."""
        out_amount = self.router_contract.functions.getAmountOut(
            in_amount, in_reserve, out_reserve
        ).call()
        
        return out_amount


# ----- SIMULATION V3-----
# Set ultra-high precision for financial calculations
getcontext().prec = 256  # Increase the precision even further

EPSILON = Decimal('1e-18')  # A small threshold for floating-point precision


def price_from_tick(tick: int) -> Decimal:
    """
    Calculate price as 1.0001 raised to the power of the tick.
    This is the Uniswap V3 price formula.
    """
    return Decimal('1.0001') ** Decimal(tick)


def tick_from_price(price: Decimal) -> int:
    """
    Calculate the tick value corresponding to a given price using the formula:
    tick = log(price) / log(1.0001).
    Ensures proper flooring and handles small values by using epsilon.
    """
    epsilon = Decimal('1e-128')  # Threshold for very small prices
    correction_value = Decimal('1e-18')  # Prevent division by zero for very small prices

    if price <= epsilon:
        price = correction_value  # Correct the value to prevent mathematical errors

    # Return the floor of the logarithm of price divided by the logarithm of 1.0001
    return int(math.floor(math.log(float(price)) / math.log(1.0001)))


def sqrt_price_q96_to_decimal(sqrt_price_x96: int) -> Decimal:
    """
    Convert Q64.96 sqrt price (fixed-point 96-bit integer) to a Decimal with high precision.
    This conversion is necessary because most of the calculations in Uniswap are done using
    a 96-bit fixed-point representation.
    """
    return Decimal(sqrt_price_x96) / (Decimal(2) ** 96)


def decimal_to_sqrt_price_q96(sqrt_price: Decimal) -> int:
    """
    Convert a Decimal representing the square root price back to a Q64.96 fixed-point integer
    by multiplying with 2^96 and truncating the result.
    """
    return int((sqrt_price * (Decimal(2) ** 96)).to_integral(rounding=ROUND_DOWN))


# ----- Functions for token0 -> token1 (zeroForOne swap) -----
def calculate_dx(L: Decimal, sp_current: Decimal, sp_target: Decimal) -> Decimal:
    """
    Calculate the amount of token0 (dx) required to move the price from sp_current to sp_target.
    This function is used for token0 -> token1 (zeroForOne) swaps.
    """
    if sp_current < EPSILON or sp_target < EPSILON:
        log_debug = get_debug_logger()
        log_debug(f"sp_current or sp_target is zero {sp_target} & {sp_current}")

        sp_current = EPSILON
        sp_target = EPSILON

    return L * (sp_current - sp_target) / (sp_current * sp_target)


def calculate_dy(L: Decimal, sp_current: Decimal, sp_target: Decimal) -> Decimal:
    """
    Calculate the amount of token1 (dy) received when moving the price from sp_current to sp_target.
    This is used for token0 -> token1 (zeroForOne) swaps.
    """
    if sp_current < EPSILON or sp_target < EPSILON:
        log_debug = get_debug_logger()
        log_debug(f"sp_current or sp_target is zero {sp_target} & {sp_current}")
        
        sp_current = EPSILON
        sp_target = EPSILON

    return L * (sp_current - sp_target)


def get_next_sqrt_price(L: Decimal, sp: Decimal, dx_net: Decimal) -> Decimal:
    """
    Calculate the new square root price after consuming a certain amount of token0 (dx_net).
    Ensures the price is updated accurately while avoiding division by zero.
    """
    if L < EPSILON:
        log_debug = get_debug_logger()
        log_debug("L is zero")
        
        L = EPSILON

    numerator = L * sp
    denominator = L + dx_net * sp
    return (numerator / denominator).quantize(Decimal('1e-18'), rounding=ROUND_DOWN)


# ----- Functions for token1 -> token0 (oneForZero swap) -----
def calculate_dy_one_for_zero(L: Decimal, sp_current: Decimal, sp_target: Decimal) -> Decimal:
    """
    Calculate the amount of token1 (dy) required to move the price from sp_current to sp_target
    when performing a token1 -> token0 (oneForZero) swap.
    """
    return L * (sp_target - sp_current)


def calculate_dx_one_for_zero(L: Decimal, sp_current: Decimal, sp_target: Decimal) -> Decimal:
    """
    Calculate the amount of token0 (dx) output when moving the price from sp_current to sp_target
    during a token1 -> token0 (oneForZero) swap.
    """
    return L * ((Decimal(1) / sp_current) - (Decimal(1) / sp_target))


def get_next_sqrt_price_one_for_zero(L: Decimal, sp: Decimal, dy_net: Decimal) -> Decimal:
    """
    Calculate the new square root price after consuming a certain amount of token1 (dy_net) in a
    token1 -> token0 (oneForZero) swap.
    """
    return (sp + (dy_net / L)).quantize(Decimal('1e-18'), rounding=ROUND_DOWN)


def get_debug_logger():
    """Create a logger for mathematical debug information."""
    os.makedirs('results/log', exist_ok=True)
    debug_log_path = f"results/log/mathematical_debug.txt"
    
    def log_debug(message):
        with open(debug_log_path, "a", encoding="utf-8") as debug_log:
            debug_log.write(f"{message}\n")
    
    return log_debug

# ----- Simulation of token0 -> token1 swap (zeroForOne) -----
def simulate_swap_zero_for_one(pool_data: dict, amount_in: int, fee: Decimal):
    """
    Simulate a Uniswap V3 exactInput swap (token0 -> token1) using the given pool data,
    amount of token0 to swap, and fee rate. The function calculates the amount of token1
    output, the amount of token0 consumed, and any fees collected during the swap.
    """
    log_debug = get_debug_logger()
    pool_state = pool_data["poolState"]["poolState"]
    fee_rate = fee
    sp_current = sqrt_price_q96_to_decimal(int(pool_state["sqrtPriceX96"]))

    if sp_current <= Decimal('1e-18'):
        log_debug(f"sp_current is too small in simulate_swap_zero_for_one: {sp_current}, setting to 1e-18")
        sp_current = Decimal('1e-18')

    L = Decimal(pool_state["liquidity"])  # Liquidity in the pool
    amount_remaining = Decimal(amount_in)  # Amount of token0 remaining for the swap
    token1_out = Decimal(0)  # Token1 output after the swap
    total_fee = Decimal(0)  # Total fee collected during the swap

    # Process ticks below current tick (descending order)
    ticks = sorted(
        [t for t in pool_state["tickBitMap"] if int(t[0]) < int(pool_state["currentTick"])],
        key=lambda x: -int(x[0])  # Sort ticks in descending order
    )

    for tick_info in ticks:
        tick_idx = int(tick_info[0])
        tick_data = tick_info[1]
        liquidity_net = Decimal(tick_data["liquidityNet"])
        initialized = tick_data["initialized"]

        if amount_remaining <= 0:
            break

        # Calculate the price corresponding to this tick
        sp_target = (Decimal('1.0001') ** (Decimal(tick_idx) / 2)).quantize(Decimal('1e-18'), rounding=ROUND_DOWN)
        if sp_target >= sp_current:
            continue

        dx_net_needed = calculate_dx(L, sp_current, sp_target)  # Calculate token0 required
        dx_gross_needed = dx_net_needed / (Decimal(1) - fee_rate)  # Gross token0 needed with fees

        if amount_remaining >= dx_gross_needed:
            token1_out += calculate_dy(L, sp_current, sp_target)  # Calculate token1 output
            total_fee += dx_gross_needed - dx_net_needed  # Calculate fees
            amount_remaining -= dx_gross_needed
            sp_current = sp_target

            if initialized:
                L -= liquidity_net  # Adjust liquidity when crossing a tick
        else:
            dx_net_available = amount_remaining * (Decimal(1) - fee_rate)
            sp_new = get_next_sqrt_price(L, sp_current, dx_net_available)
            token1_out += calculate_dy(L, sp_current, sp_new)
            total_fee += amount_remaining - dx_net_available
            amount_remaining = Decimal(0)
            sp_current = sp_new

    # Handle the final partial swap if input still remains
    if amount_remaining > 0:
        dx_net_available = amount_remaining * (Decimal(1) - fee_rate)
        sp_new = get_next_sqrt_price(L, sp_current, dx_net_available)
        token1_out += calculate_dy(L, sp_current, sp_new)
        total_fee += amount_remaining - dx_net_available
        sp_current = sp_new

    if sp_current == 0:
        log_debug("sp_current became zero after swap, setting to 1e-18")
        sp_current = Decimal('1e-18')

    # Correct balance calculations (balance is based on liquidity and price)
    balance0 = Decimal(L / sp_current).to_integral(rounding=ROUND_DOWN)
    balance1 = Decimal(L * sp_current).to_integral(rounding=ROUND_DOWN)

    # Update the pool state after the swap
    updated_state = copy.deepcopy(pool_state)
    updated_state["sqrtPriceX96"] = str(decimal_to_sqrt_price_q96(sp_current))
    updated_state["currentTick"] = str(tick_from_price(sp_current ** 2))
    updated_state["liquidity"] = str(int(L))
    updated_state["balance0"] = str(balance0)
    updated_state["balance1"] = str(balance1)

    return {
        "token1_output": token1_out,
        "token0_consumed": Decimal(amount_in) - amount_remaining,
        "fees_collected": total_fee,
        "new_pool_state": updated_state,
        "final_price": sp_current ** 2
    }

# ----- Simulation of token1 -> token0 swap (oneForZero) -----
def simulate_swap_one_for_zero(pool_data: dict, amount_in: int, fee: Decimal):
    """
    Simulate a Uniswap V3 exactInput swap (token1 -> token0) using the given pool data,
    amount of token1 to swap, and fee rate. The function calculates the amount of token0
    output, the amount of token1 consumed, and any fees collected during the swap.
    """
    log_debug = get_debug_logger()
    pool_state = pool_data["poolState"]["poolState"]
    fee_rate = fee
    sp_current = sqrt_price_q96_to_decimal(int(pool_state["sqrtPriceX96"]))

    if sp_current <= Decimal('1e-18'):
        log_debug(f"sp_current is too small in simulate_swap_one_for_zero: {sp_current}, setting to 1e-18")
        sp_current = Decimal('1e-18')

    L = Decimal(pool_state["liquidity"])  # Liquidity in the pool
    amount_remaining = Decimal(amount_in)  # Amount of token1 remaining for the swap
    token0_out = Decimal(0)  # Token0 output after the swap
    total_fee = Decimal(0)  # Total fee collected during the swap

    # Process ticks above current tick (ascending order)
    ticks = sorted(
        [t for t in pool_state["tickBitMap"] if int(t[0]) > int(pool_state["currentTick"])],
        key=lambda x: int(x[0])  # Sort ticks in ascending order
    )

    for tick_info in ticks:
        tick_idx = int(tick_info[0])
        tick_data = tick_info[1]
        liquidity_net = Decimal(tick_data["liquidityNet"])
        initialized = tick_data["initialized"]

        if amount_remaining <= 0:
            break

        # Calculate the price corresponding to this tick
        sp_target = (Decimal('1.0001') ** (Decimal(tick_idx) / 2)).quantize(Decimal('1e-18'), rounding=ROUND_DOWN)
        if sp_target <= sp_current:
            continue

        dy_net_needed = calculate_dy_one_for_zero(L, sp_current, sp_target)  # Calculate token1 required
        dy_gross_needed = dy_net_needed / (Decimal(1) - fee_rate)  # Gross token1 needed with fees

        if amount_remaining >= dy_gross_needed:
            token0_out += calculate_dx_one_for_zero(L, sp_current, sp_target)  # Calculate token0 output
            total_fee += dy_gross_needed - dy_net_needed  # Calculate fees
            amount_remaining -= dy_gross_needed
            sp_current = sp_target

            if initialized:
                L += liquidity_net  # Adjust liquidity when crossing a tick
        else:
            dy_net_available = amount_remaining * (Decimal(1) - fee_rate)
            sp_new = get_next_sqrt_price_one_for_zero(L, sp_current, dy_net_available)
            token0_out += calculate_dx_one_for_zero(L, sp_current, sp_new)
            total_fee += amount_remaining - dy_net_available
            amount_remaining = Decimal(0)
            sp_current = sp_new

    # Handle the final partial swap if input still remains
    if amount_remaining > 0:
        dy_net_available = amount_remaining * (Decimal(1) - fee_rate)
        sp_new = get_next_sqrt_price_one_for_zero(L, sp_current, dy_net_available)
        token0_out += calculate_dx_one_for_zero(L, sp_current, sp_new)
        total_fee += amount_remaining - dy_net_available
        sp_current = sp_new

    # Correct balance calculations (balance is based on liquidity and price)
    balance0 = Decimal(L / sp_current).to_integral(rounding=ROUND_DOWN)
    balance1 = Decimal(L * sp_current).to_integral(rounding=ROUND_DOWN)

    # Update the pool state after the swap
    updated_state = copy.deepcopy(pool_state)
    updated_state["sqrtPriceX96"] = str(decimal_to_sqrt_price_q96(sp_current))
    updated_state["currentTick"] = str(tick_from_price(sp_current ** 2))
    updated_state["liquidity"] = str(int(L))
    updated_state["balance0"] = str(balance0)
    updated_state["balance1"] = str(balance1)

    return {
        "token0_output": token0_out,
        "token1_consumed": Decimal(amount_in) - amount_remaining,
        "fees_collected": total_fee,
        "new_pool_state": updated_state,
        "final_price": sp_current ** 2
    }


class UniswapV3Mathematical:
    """Mathematical implementation for Uniswap V3 pools."""
    
    def __init__(self) -> None:
        # The node_rpc_url is maintained for compatibility but not used
        self.v3_pools_data = []  # This will be filled when initialize_pools is called
    
    def initialize_pools(self, v3_pools_data):
        """Initialize with pool data from the loaded JSON"""
        self.v3_pools_data = v3_pools_data
    
    def exact_input(
        self,
        token_in: str,
        token_out: str,
        fee: int,
        amount_in: int,
        block_no: int,
        sqrt_price_limit_x96: int = 0,
    ) -> int:
        """Simulate exact input swap for V3 pools by mathematical calculation."""
        # Get the pool data for this token pair and fee
        pool_data = self._get_pool_data(token_in, token_out, fee, block_no)
        
        if not pool_data:
            raise ValueError(f"Pool not found for {token_in} -> {token_out} with fee {fee}")
        
        # Convert fee to decimal (e.g. 3000 -> 0.003)
        fee_decimal = Decimal(fee) / Decimal(1000000)
        
        # Determine if this is a token0 to token1 swap or vice versa
        pool_token0 = pool_data["poolState"]["poolStaticInfo"]["token0"].lower()
        
        if token_in.lower() == pool_token0:
            # token0 to token1 swap
            result = simulate_swap_zero_for_one(pool_data, amount_in, fee_decimal)
            return int(result["token1_output"])
        else:
            # token1 to token0 swap
            result = simulate_swap_one_for_zero(pool_data, amount_in, fee_decimal)
            return int(result["token0_output"])
    
    def _get_pool_data(self, token_in: str, token_out: str, fee: int, block_no: int) -> dict:
        """
        Get pool data for a given token pair and fee.
        Uses the pool data loaded from JSON files.
        """
        token_in = token_in.lower()
        token_out = token_out.lower()
        
        # 找到匹配代币对和费率的池子
        for pool in self.v3_pools_data:
            pool_info = pool["poolState"]["poolStaticInfo"]
            pool_token0 = pool_info["token0"].lower()
            pool_token1 = pool_info["token1"].lower()
            pool_fee = int(pool_info["swapFee"])
            
            # 检查池子是否匹配代币和费率
            tokens_match = (
                (token_in == pool_token0 and token_out == pool_token1) or
                (token_in == pool_token1 and token_out == pool_token0)
            )
            
            if tokens_match and pool_fee == fee:
                return pool
        
        return None


# Replace V3Simulator with UniswapV3Mathematical
V3Simulator = UniswapV3Mathematical


def load_node_mappings(node_name_file, node_mapping_file):
    """
    Load both node name and node mapping information into dictionaries.
    
    Args:
        node_name_file: Path to the file containing token symbol to address mappings
        node_mapping_file: Path to the file containing token address to node ID mappings
        
    Returns:
        Tuple of (node_name, node_index, index_name) dictionaries where:
        - node_name: Maps token symbol to token address
        - node_index: Maps token address to node ID
        - index_name: Maps node ID to token symbol
    """
    # Create log directory if it doesn't exist
    os.makedirs('results/log', exist_ok=True)
    mappings_log_path = f"results/log/node_mappings_load.txt"
    mappings_log = open(mappings_log_path, "a", encoding="utf-8")  # Append mode
    
    # Function to log messages
    def log_mappings(message):
        mappings_log.write(f"{message}\n")
    
    log_mappings(f"Loading node mappings from {node_name_file} and {node_mapping_file}")
    
    # Load symbol to address mapping
    node_name = {}
    with open(node_name_file, 'r') as file:
        for line in file:
            line = line.strip()
            if line:
                parts = line.split()
                symbol = parts[0]
                address = parts[1]
                node_name[symbol] = address
    
    log_mappings(f"Loaded {len(node_name)} symbol to address mappings")
    
    # Load address to node ID mapping
    node_index = {}
    with open(node_mapping_file, 'r') as file:
        for line in file:
            line = line.strip()
            if line:
                parts = line.split()
                node_id = parts[0]
                address = parts[1]
                node_index[address] = node_id
    
    log_mappings(f"Loaded {len(node_index)} address to node ID mappings")
    
    # Create node ID to symbol mapping
    index_name = {}
    for name, addr in node_name.items():
        try:
            index_name[int(node_index[addr])] = name
        except (KeyError, ValueError):
            # Skip if address not in node_index
            pass
    
    log_mappings(f"Created {len(index_name)} node ID to symbol mappings")
    mappings_log.close()
    
    return node_name, node_index, index_name

def calculate_proportions_bps(
    from_token: str, 
    paths: list, 
    path_tokens: list,
    price_dict: dict, 
    balance_dict: dict,
    pool_weights: dict,
    token_decimals_dict: dict,
    original_amount: int,
    lambda_threshold: int = 0
):
    """
    Calculate the proportions for each path in basis points (BPS) using dynamic path re-ranking.
    Allocate to paths based on their weights until all flow is allocated or no paths remain.
    Each edge will consider all available pools and all ticks with capacity.
    
    Args:
        from_token: Source token
        paths: List of pool addresses for each path
        path_tokens: List of tokens for each path
        price_dict: Dictionary of token prices
        balance_dict: Dictionary of pool capacities
        pool_weights: Dictionary of pool weights
        token_decimals_dict: Dictionary of token decimals
        input_amount: Input amount
        lambda_threshold: Lambda threshold for filtering
        
    Returns:
        Tuple of (proportions in BPS, remaining amount, path_pool_allocations)
    """
    # Create a separate allocation log file
    allocation_log_path = f"results/log/allocation_log_{from_token[0:4]}_{original_amount}.txt"
    allocation_log = open(allocation_log_path, "w", encoding="utf-8")
    
    # Function to print to both console and log file
    def log_msg(message):
        allocation_log.write(message + "\n")
    
    # Initialize reranking statistics
    rerank_stats = RerankingStats()
    
    log_msg(f"Starting allocation process for {from_token} with amount {original_amount}")
    
    if from_token not in price_dict:
        log_msg(f"Warning: Could not find price for input token or no valid paths")
        allocation_log.close()
        return [], original_amount, {}

    decimals = token_decimals_dict.get(from_token, 6)
    input_amount = original_amount / (10 ** decimals) * price_dict[from_token]
    log_msg(f"Converting {original_amount} tokens to {input_amount} ETH equivalent")
    
    # Initialize data structures
    proportions_bps = [0] * len(paths)
    total_remaining = input_amount
    
    # Make working copies of pool_weights and balance_dict to update during allocation
    working_weights = copy.deepcopy(pool_weights)
    working_capacities = copy.deepcopy(balance_dict)
    
    # Track if an edge is totally exhausted (no pools with capacity for the token pair)
    # Format: {(path_idx, edge_idx): bool}
    edge_exhausted = {}
    
    # Initialize path weights and capacities
    path_weights = []
    path_capacities = []
    path_edges = []  # Store the selected edges (pools) for each path
    
    # Calculate initial path weights and capacities
    for i, pools in enumerate(paths):
        # Extract token path
        tokens = path_tokens[i]
        
        # Calculate path weight and capacity, and get selected edges
        path_weight, path_capacity, selected_edges = calculate_path_weight_and_capacity(
            tokens, pools, working_weights, working_capacities
        )
        
        path_weights.append(path_weight)
        path_capacities.append(path_capacity)
        path_edges.append(selected_edges)
    
    # Print initial path weights and ranking
    log_msg("\n===== INITIAL PATH WEIGHTS AND RANKING =====")
    # Create a list of (index, weight, capacity) tuples
    path_data = [(i, path_weights[i], path_capacities[i]) for i in range(len(paths))]
    # Sort by weight (ascending)
    path_data.sort(key=lambda x: x[1])
    # Print the ranking
    for rank, (idx, weight, capacity) in enumerate(path_data[:10]):  # Show top 10
        log_msg(f"Rank {rank+1}: Path {idx+1} - Weight: {weight}, Capacity: {capacity}")
    log_msg("============================================\n")
    
    # Keep track of paths that have been allocated to and exhausted
    allocated_paths = []
    exhausted_paths = set()
    
    # Track allocated amounts per pool for each edge in each path
    # Format: {(path_idx, edge_idx, pool_address, tick): allocated_amount}
    pool_allocations = {}
    
    # Track direct pool proportions in BPS - this is the key change
    # Format: {pool_address: proportion_bps} or {multi_key: proportion_bps}
    pool_proportion_bps = {}
    
    # Format for path_pool_allocations: {path_idx: {edge_idx: [(pool_address, tick, amount, proportion, pool_type), ...], ...}}
    path_pool_allocations = {}
    
    allocation_round = 0
    
    # Continue allocating until all flow is allocated or all paths are exhausted
    while total_remaining > 0 and len(exhausted_paths) < len(paths):
        # Recalculate path weights and capacities at the start of each round
        if allocation_round > 0:
            log_msg("\n----- RECALCULATING PATH WEIGHTS AND CAPACITIES -----")
            rerank_stats.rounds += 1
            path_changes_count = 0
            
            # Store old path data for comparison
            old_path_data = {i: (path_weights[i], path_capacities[i]) if i < len(path_weights) else (float('inf'), 0) 
                             for i in range(len(paths)) if i not in exhausted_paths}
            
            # Re-calculate path data to get updated weights and capacities
            path_weights = []
            path_capacities = []
            path_edges = []
            
            # Time the weight/capacity update process
            update_start_time = time.time()
            
            for i, pools in enumerate(paths):
                # Skip paths that are already exhausted
                if i in exhausted_paths:
                    path_weights.append(float('inf'))
                    path_capacities.append(0)
                    path_edges.append([])
                    continue
                
                # Skip paths where any edge is completely exhausted (no available pools)
                path_exhausted = False
                for edge_idx in range(len(path_tokens[i]) - 1):
                    if edge_exhausted.get((i, edge_idx), False):
                        path_exhausted = True
                        break
                
                if path_exhausted:
                    exhausted_paths.add(i)
                    path_weights.append(float('inf'))
                    path_capacities.append(0)
                    path_edges.append([])
                    log_msg(f"Path {i+1} is exhausted (one or more edges have no available pools)")
                    continue
                    
                # Calculate path weight and capacity with updated capacities
                tokens = path_tokens[i]
                
                # For each path, we now consider ALL pools that could be used for each edge
                all_pools_for_path = []
                for edge_idx in range(len(tokens) - 1):
                    token_in = tokens[edge_idx].lower()
                    token_out = tokens[edge_idx + 1].lower()
                    token_pair_key = f"{token_in}_{token_out}"
                    
                    # Find all pools for this token pair
                    available_pools = []
                    if token_pair_key in working_weights:
                        for pool_addr in working_weights[token_pair_key]:
                            # Check if this pool has any ticks with capacity
                            has_capacity = False
                            if pool_addr in working_capacities.get(token_pair_key, {}):
                                for capacity_entry in working_capacities[token_pair_key][pool_addr]:
                                    if capacity_entry.get('capacity', 0) > 0:
                                        has_capacity = True
                                        break
                            
                            if has_capacity:
                                available_pools.append(pool_addr)
                    
                    # If no pools have capacity for this edge, mark it as exhausted
                    if not available_pools:
                        edge_exhausted[(i, edge_idx)] = True
                        path_exhausted = True
                        log_msg(f"Edge {edge_idx+1} of path {i+1} ({token_in}->{token_out}) is completely exhausted")
                    else:
                        all_pools_for_path.append(available_pools)
                
                # If any edge is exhausted, mark the path as exhausted
                if path_exhausted:
                    exhausted_paths.add(i)
                    path_weights.append(float('inf'))
                    path_capacities.append(0)
                    path_edges.append([])
                    log_msg(f"Path {i+1} is exhausted (one or more edges have no available pools)")
                    continue
                
                path_weight, path_capacity, selected_edges = calculate_path_weight_and_capacity(
                    tokens, all_pools_for_path, working_weights, working_capacities
                )
                
                # Print changes in weight and capacity for this path
                if i in old_path_data:
                    old_weight, old_capacity = old_path_data[i]
                    weight_change = path_weight - old_weight
                    capacity_change = path_capacity - old_capacity
                    
                    # Check if weight or capacity changed significantly
                    weight_changed = abs(weight_change) > 1e-10
                    capacity_changed = abs(capacity_change) > 1e-10
                    
                    # Check for pool/tick changes in the edges
                    old_edges = path_edges[i] if i < len(path_edges) else []
                    pool_tick_changes = []
                    
                    for j, edge in enumerate(selected_edges):
                        if j < len(old_edges):
                            old_edge = old_edges[j]
                            # Check if pool or tick changed
                            if edge['pool'] != old_edge['pool'] or edge['tick'] != old_edge['tick']:
                                pool_tick_changes.append((j, old_edge['pool'], old_edge['tick'], edge['pool'], edge['tick']))
                    
                    # If any significant change, count this path as changed
                    if weight_changed or capacity_changed or pool_tick_changes:
                        path_changes_count += 1
                    
                    log_msg(f"Path {i+1} updates:")
                    log_msg(f"  Weight: {old_weight} -> {path_weight} (Change: {weight_change:+.6f})")
                    log_msg(f"  Capacity: {old_capacity} -> {path_capacity} (Change: {capacity_change:+.6f})")
                    
                    if pool_tick_changes:
                        log_msg(f"  POOL/TICK CHANGES DETECTED:")
                        for edge_idx, old_pool, old_tick, new_pool, new_tick in pool_tick_changes:
                            log_msg(f"    Edge {edge_idx+1}: Pool {old_pool}@Tick {old_tick} -> Pool {new_pool}@Tick {new_tick}")
                
                path_weights.append(path_weight)
                path_capacities.append(path_capacity)
                path_edges.append(selected_edges)
                
                # Mark path as exhausted if it now has zero capacity
                if path_capacity <= 0:
                    exhausted_paths.add(i)
                    log_msg(f"  Path {i+1} now exhausted (zero capacity)")
            
            # Record time spent updating weights/capacities
            update_end_time = time.time()
            update_time = update_end_time - update_start_time
            rerank_stats.update_times_per_round.append(update_time)
            rerank_stats.total_update_time += update_time
            
            # Time the path sorting process
            sort_start_time = time.time()
            
            # Update path_data with new weights and capacities
            path_data = [(i, path_weights[i], path_capacities[i]) for i in range(len(paths)) if i not in exhausted_paths]
            path_data.sort(key=lambda x: x[1])  # Sort by weight
            
            # Record time spent sorting paths
            sort_end_time = time.time()
            sort_time = sort_end_time - sort_start_time
            rerank_stats.sort_times_per_round.append(sort_time)
            rerank_stats.total_sort_time += sort_time
            
            # Record the number of paths that changed
            rerank_stats.path_changes_per_round.append(path_changes_count)
            
            log_msg("\n----- UPDATED PATH RANKING -----")
            log_msg(f"Reranking round {rerank_stats.rounds}: {path_changes_count} paths changed")
            log_msg(f"Update time: {update_time:.6f}s, Sort time: {sort_time:.6f}s")
            for rank, (idx, weight, capacity) in enumerate(path_data[:5]):  # Show top 5
                log_msg(f"Rank {rank+1}: Path {idx+1} - Weight: {weight}, Capacity: {capacity}")
            log_msg("--------------------------------")
        
        # If we've processed all paths by weight, stop
        if not path_data:
            log_msg("No more paths with capacity available")
            break
            
        allocation_round += 1
        log_msg(f"\n===== ALLOCATION ROUND {allocation_round} =====")
        
        # Get the path with the current rank
        idx, weight, capacity = path_data[0]  # Always get the current best path
        
        # Skip if capacity is zero
        if capacity <= 0:
            log_msg(f"Path {idx+1} has zero capacity, marking as exhausted")
            exhausted_paths.add(idx)
            continue
        
        # Determine amount to allocate to this path
        amount_to_allocate = min(capacity, total_remaining)
        
        if amount_to_allocate <= 0:
            log_msg(f"Path {idx+1} has no capacity to allocate")
            exhausted_paths.add(idx)
            continue
        
        # Update the proportion for this path
        proportion_bps = int((amount_to_allocate * 10000) // input_amount)
        proportions_bps[idx] += proportion_bps  # Add to existing proportion
        
        # Record that we've allocated to this path
        if idx not in allocated_paths:
            allocated_paths.append(idx)
        
        # Update remaining amount
        total_remaining -= amount_to_allocate
        
        # Print allocation details
        log_msg(f"Selected Path {idx+1}")
        log_msg(f"Path: {' -> '.join(path_tokens[idx])}")
        log_msg(f"Weight: {weight}, Capacity: {capacity}")
        log_msg(f"Allocated amount: {amount_to_allocate}")
        log_msg(f"Proportion: {proportion_bps/100:.2f}% ({proportion_bps} bps)")
        log_msg(f"Remaining: {total_remaining}")
        
        # Get the edges for this path
        selected_edges = path_edges[idx]
        
        # Update capacities for each edge in the path
        log_msg(f"\nUpdating capacities for path {idx+1}:")
        for edge_idx, edge_data in enumerate(selected_edges):
            token_in = path_tokens[idx][edge_idx].lower()
            token_out = path_tokens[idx][edge_idx + 1].lower()
            pool_address = edge_data['pool']
            tick = edge_data['tick']
            pool_type = edge_data.get('pool_type', 'v3')
            
            # Print edge details before update
            log_msg(f"  Edge {edge_idx+1}: {token_in} -> {token_out}, Pool: {pool_address}")
            log_msg(f"  Tick: {tick}, Pool Type: {pool_type}, Weight: {edge_data['weight']}, Capacity before: {edge_data['capacity']}")
            
            # Store allocation for this pool
            pool_key = (idx, edge_idx, pool_address, tick)
            if pool_key not in pool_allocations:
                pool_allocations[pool_key] = 0
            pool_allocations[pool_key] += amount_to_allocate
            
            # Add to path_pool_allocations
            if idx not in path_pool_allocations:
                path_pool_allocations[idx] = {}
            if edge_idx not in path_pool_allocations[idx]:
                path_pool_allocations[idx][edge_idx] = []
            
            # Create a unique identifier for this pool
            # For multiple token pairs in the same pool, use a composite key
            existing_from_to = None
            pool_identifier = pool_address.lower()
            
            # Check if this pool is already being used for a different token pair
            if pool_identifier in pool_proportion_bps:
                # The pool might already exist with different token pair
                found = False
                for key in pool_proportion_bps:
                    if isinstance(key, str) and key.startswith(f"{pool_identifier}_"):
                        # Found a multi-key entry for this pool
                        parts = key.split('_', 3)  # Split into [pool_address, token_in, token_out]
                        if len(parts) >= 3:
                            existing_from = parts[1]
                            existing_to = parts[2]
                            if existing_from != token_in or existing_to != token_out:
                                existing_from_to = (existing_from, existing_to)
                                found = True
                                break
                
                if found and existing_from_to:
                    # This pool is already used for a different token pair
                    # Create a new multi-key for this token pair
                    pool_identifier = f"{pool_address}_{token_in}_{token_out}"
                    
                    # Also convert the existing entry to a multi-key if it isn't already
                    if pool_address in pool_proportion_bps:
                        # Create a new multi-key for the existing entry
                        existing_key = f"{pool_address}_{existing_from_to[0]}_{existing_from_to[1]}"
                        pool_proportion_bps[existing_key] = pool_proportion_bps[pool_address]
                        del pool_proportion_bps[pool_address]
            
            # Update direct proportion in BPS for this pool
            # Calculate the proportion of the total input amount being allocated to this pool in this round
            pool_bps = int((amount_to_allocate * 10000) // input_amount)
            
            if pool_identifier not in pool_proportion_bps:
                pool_proportion_bps[pool_identifier] = 0
            pool_proportion_bps[pool_identifier] += pool_bps
            
            # Store both the allocation amount and the direct BPS proportion
            path_pool_allocations[idx][edge_idx].append((
                pool_address, 
                tick, 
                amount_to_allocate,  # Amount allocated in this round
                edge_data['capacity'],  # Store capacity for reference
                pool_type,            # Store pool_type
                pool_bps              # Direct BPS proportion allocated to this pool in this round
            ))
            
            # Update capacities for this edge
            token_pair_key = f"{token_in}_{token_out}"
            if token_pair_key in working_capacities and pool_address in working_capacities[token_pair_key]:
                # Find the capacity entry for this tick
                tick_updated = False
                for capacity_entry in working_capacities[token_pair_key][pool_address]:
                    if capacity_entry.get('tick') == tick:
                        # Update capacity
                        old_capacity = capacity_entry.get('capacity', 0)
                        new_capacity = max(0, old_capacity - amount_to_allocate)
                        capacity_entry['capacity'] = new_capacity
                        log_msg(f"  Updated capacity: {old_capacity} -> {new_capacity}")
                        
                        # Check if this tick is now exhausted
                        if new_capacity <= 0:
                            log_msg(f"  TICK EXHAUSTED: Pool {pool_address}@Tick {tick} now has zero capacity")
                        
                        tick_updated = True
                        break
                
                if not tick_updated:
                    log_msg(f"  WARNING: Could not find tick {tick} for pool {pool_address}")
                
                # Check if all ticks for this pool are exhausted
                all_ticks_exhausted = True
                for capacity_entry in working_capacities[token_pair_key][pool_address]:
                    if capacity_entry.get('capacity', 0) > 0:
                        all_ticks_exhausted = False
                        break
                
                if all_ticks_exhausted:
                    log_msg(f"  Pool {pool_address} has all ticks exhausted for {token_in}->{token_out}")
                
                # Check if all pools for this token pair are exhausted
                all_pools_exhausted = True
                for pool_addr, cap_entries in working_capacities[token_pair_key].items():
                    for cap_entry in cap_entries:
                        if cap_entry.get('capacity', 0) > 0:
                            all_pools_exhausted = False
                            log_msg(f"  Still available: Pool {pool_addr} for {token_in}->{token_out}")
                            break
                    if not all_pools_exhausted:
                        break
                
                # Mark edge as exhausted only if all pools are exhausted
                if all_pools_exhausted:
                    edge_exhausted[(idx, edge_idx)] = True
                    log_msg(f"  Edge {edge_idx+1} ({token_in}->{token_out}) is completely exhausted (all pools)")
        
        log_msg(f"===== END OF ROUND {allocation_round} =====")
        
        # Check if remaining amount is below threshold
        if total_remaining < lambda_threshold:
            log_msg(f"Remaining amount {total_remaining} is below threshold {lambda_threshold}")
            break
    
    log_msg("\n===== ALLOCATION COMPLETED =====")
    log_msg(f"Allocated to {len(allocated_paths)} paths: {[p+1 for p in allocated_paths]}")
    log_msg(f"Total allocation: {input_amount - total_remaining} ({(input_amount - total_remaining) / input_amount * 100:.2f}%)")
    
    # Calculate original token units for remaining amount
    remaining_amount_original = total_remaining / price_dict[from_token] * (10 ** decimals)
    log_msg(f"Remaining amount (original units): {remaining_amount_original}")
    
    # Add path_input_amounts to log for easier debugging
    path_input_amounts = {}
    for i, proportion_bps in enumerate(proportions_bps):
        path_input_amounts[i] = (original_amount * proportion_bps) // 10000
    
    log_msg("\nPath Input Amounts:")
    for path_idx, amount in path_input_amounts.items():
        if amount > 0:
            log_msg(f"Path {path_idx+1}: {amount} ({proportions_bps[path_idx]/100:.2f}%)")
    
    # Log the pool proportions directly calculated during allocation
    log_msg("\nPool Proportions (direct calculation):")
    for pool_addr, bps in sorted(pool_proportion_bps.items(), key=lambda x: -x[1])[:20]:  # Top 20 pools
        if '_' in pool_addr and pool_addr.count('_') >= 2:
            # This is a multi-key entry (pool used for multiple token pairs)
            parts = pool_addr.split('_', 3)
            original_pool = parts[0]
            token_in = parts[1]
            token_out = parts[2]
            log_msg(f"Pool {original_pool} ({token_in}->{token_out}): {bps/100:.2f}% ({bps} bps)")
        else:
            log_msg(f"Pool {pool_addr}: {bps/100:.2f}% ({bps} bps)")
    
    # Consolidate path_pool_allocations using the direct BPS proportions
    for path_idx in path_pool_allocations:
        for edge_idx in path_pool_allocations[path_idx]:
            # Group by pool address
            pool_groups = {}
            for pool_addr, tick, amount, capacity, p_type, pool_bps in path_pool_allocations[path_idx][edge_idx]:
                if pool_addr not in pool_groups:
                    pool_groups[pool_addr] = {
                        "tick": tick,
                        "pool_bps": 0,
                        "pool_type": p_type
                    }
                # Sum up the BPS for this pool in this edge
                pool_groups[pool_addr]["pool_bps"] += pool_bps
            
            # Create new entries with consolidated proportions
            consolidated_entries = []
            for pool_addr, data in pool_groups.items():
                consolidated_entries.append((
                    pool_addr,
                    data["tick"],
                    data["pool_type"],
                    data["pool_bps"] / 10000.0  # Convert BPS to proportion
                ))
            
            # Replace with consolidated entries
            path_pool_allocations[path_idx][edge_idx] = consolidated_entries
    
    # Add detailed pool allocations summary to log
    log_msg("\nDetailed Pool Allocations (by path and edge):")
    for path_idx in sorted(path_pool_allocations.keys()):
        path_input = path_input_amounts.get(path_idx, 0)
        if path_input <= 0:
            continue
            
        log_msg(f"\nPath {path_idx+1} (Input: {path_input}):")
        for edge_idx, pools_data in sorted(path_pool_allocations[path_idx].items()):
            token_in = path_tokens[path_idx][edge_idx].lower()
            token_out = path_tokens[path_idx][edge_idx+1].lower()
            log_msg(f"  Edge {edge_idx+1} ({token_in} -> {token_out}):")
            
            for pool_addr, tick, pool_type, proportion in pools_data:
                # Calculate the actual amount that should be swapped through this pool for this path
                actual_amount = int(path_input * proportion)
                log_msg(f"    Pool: {pool_addr}, Tick: {tick}, Type: {pool_type}")
                log_msg(f"    Allocated: {actual_amount} tokens ({proportion*100:.2f}% of path input)")
    
    # Close the allocation log file
    log_msg(f"\nAllocation log saved to: {allocation_log_path}")
    allocation_log.close()
    
    # Store the reranking statistics as an attribute of the function
    # so it can be accessed by simulate_paths_from_analysis
    calculate_proportions_bps.last_rerank_stats = rerank_stats
    
    # For the result, use the directly calculated pool_proportion_bps
    return proportions_bps, remaining_amount_original, path_pool_allocations, pool_proportion_bps


def calculate_path_weight_and_capacity(tokens, pools, pool_weights, pool_capacities):
    """
    Calculate the weight and capacity of a path.
    
    Args:
        tokens: List of tokens in the path
        pools: List of lists of pool addresses for each edge in the path
              (can be a list of available pools for each edge or a flat list of pools)
        pool_weights: Dictionary of pool weights
        pool_capacities: Dictionary of pool capacities
        
    Returns:
        Tuple of (path_weight, path_capacity, selected_edges)
    """
    total_weight = 0
    min_capacity = float('inf')
    selected_edges = []
    
    # Check if pools is a list of lists (multiple pools per edge) or a flat list (one pool per edge)
    is_pools_list_of_lists = False
    if pools and isinstance(pools[0], list):
        is_pools_list_of_lists = True
    
    # For each hop in the path
    for i in range(len(tokens) - 1):
        token_in = tokens[i].lower()
        token_out = tokens[i+1].lower()
        
        candidate_pools = []
        if is_pools_list_of_lists:
            # If pools is a list of lists, use the pools for this edge
            if i < len(pools):
                candidate_pools = pools[i]
        else:
            # If pools is a flat list, use the i-th pool if available
            if i < len(pools):
                candidate_pools = [pools[i].lower()]
        
        # Skip if no pools are available for this edge
        if not candidate_pools:
            total_weight = float('inf')
            min_capacity = 0
            break
        
        # Find the best pool and tick for this edge
        best_weight, best_capacity, best_pool, best_tick, best_pool_type = find_best_pool_for_edge(
            token_in, token_out, candidate_pools, pool_weights, pool_capacities
        )
        
        # Add edge weight to path weight
        if best_weight != float('inf'):
            total_weight += best_weight
        
        # Update min capacity if this edge has lower capacity
        if best_capacity < min_capacity:
            min_capacity = best_capacity
        
        # Record selected edge info
        edge_info = {
            'pool': best_pool if best_pool else (candidate_pools[0] if candidate_pools else None),
            'tick': best_tick,
            'weight': best_weight,
            'capacity': best_capacity,
            'pool_type': best_pool_type
        }
        selected_edges.append(edge_info)
    
    # If no valid capacity was found, return 0
    if min_capacity == float('inf'):
        min_capacity = 0
    
    return total_weight, min_capacity, selected_edges

def find_best_pool_for_edge(token_in, token_out, candidate_pools, pool_weights, pool_capacities):
    """
    Find the best pool and tick for a given edge (token pair).
    Consider all pools and all ticks for this edge.
    
    Args:
        token_in: Input token address
        token_out: Output token address
        candidate_pools: List of pool addresses for this edge
        pool_weights: Dictionary of pool weights
        pool_capacities: Dictionary of pool capacities
        
    Returns:
        Tuple of (best_weight, best_capacity, best_pool, best_tick, pool_type)
    """
    best_weight = float('inf')
    best_capacity = 0
    best_pool = None
    best_tick = None
    best_pool_type = None
    
    # Create token pair key (only use the direct direction)
    token_pair_key = f"{token_in.lower()}_{token_out.lower()}"
    
    # Check if the pair key exists in pool_weights
    if token_pair_key not in pool_weights:
        return best_weight, best_capacity, best_pool, best_tick, best_pool_type
    
    # Consider all pool+tick combinations for this token pair
    all_tick_options = []
    
    # Gather all possible pool+tick combinations with their weights and capacities
    for pool_address in candidate_pools:
        pool_address = pool_address.lower()
        
        # Check if pool exists for this pair
        if pool_address not in pool_weights[token_pair_key]:
            continue
        
        # Get weight data for all ticks in this pool
        weight_data_list = pool_weights[token_pair_key][pool_address]
        
        for tick_data in weight_data_list:
            tick = tick_data.get('tick')
            weight = tick_data.get('weight', float('inf'))
            pool_type = tick_data.get('pool_type', 'v3')  # Default to v3
            
            # Find capacity for this tick
            capacity = 0
            if token_pair_key in pool_capacities and pool_address in pool_capacities[token_pair_key]:
                for cap_data in pool_capacities[token_pair_key][pool_address]:
                    if cap_data.get('tick') == tick:
                        capacity = cap_data.get('capacity', 0)
                        break
            
            # Add this pool+tick option to our list
            all_tick_options.append({
                'pool': pool_address,
                'tick': tick,
                'weight': weight,
                'capacity': capacity,
                'pool_type': pool_type
            })
    
    # Sort options by weight (ascending)
    all_tick_options.sort(key=lambda x: x['weight'])
    
    # Select the best tick with non-zero capacity
    for option in all_tick_options:
        if option['capacity'] > 0:
            best_weight = option['weight']
            best_capacity = option['capacity']
            best_pool = option['pool']
            best_tick = option['tick']
            best_pool_type = option['pool_type']
            break
    
    # If no tick with capacity was found, pick the one with the best weight anyway
    if best_pool is None and all_tick_options:
        option = all_tick_options[0]  # best weight option
        best_weight = option['weight']
        best_capacity = 0  # No capacity
        best_pool = option['pool']
        best_tick = option['tick']
        best_pool_type = option['pool_type']
    
    return best_weight, best_capacity, best_pool, best_tick, best_pool_type

def get_token_pair_key(token_a, token_b):
    """
    Create a consistent key for a token pair.
    
    Args:
        token_a: First token address or symbol
        token_b: Second token address or symbol
        
    Returns:
        String key in the format "token1_token2" where token1 and token2 are normalized
    """
    token_a = token_a.lower().strip()
    token_b = token_b.lower().strip()
    
    # If tokens don't look like addresses (0x...), they might be symbols
    # In that case, try to get the addresses from a lookup table if available
    if hasattr(get_token_pair_key, 'token_lookup'):
        token_a = get_token_pair_key.token_lookup.get(token_a, token_a)
        token_b = get_token_pair_key.token_lookup.get(token_b, token_b)
        
    return f"{token_a}_{token_b}"

def initialize_token_lookup(token_mapping):
    """
    Initialize a token symbol to address lookup table.
    
    Args:
        token_mapping: Dictionary mapping token symbols to addresses
    """
    # Store the lookup dictionary as an attribute of the get_token_pair_key function
    # This allows it to be accessed without global variables
    get_token_pair_key.token_lookup = token_mapping


def simulate_paths_from_analysis(
    path_file: str,
    v3_pools_file: str,
    v2_pools_file: str,
    input_amount: int,
    node_rpc_url: str,
    block_number: int,
    name: str,
    balance_dict: dict,
    price_dict: dict,
    token_decimals_dict: dict,
    weights_file: str = None,
    capacities_file: str = None,
    lambda_threshold: float = 0
) -> pd.DataFrame:
    """
    Simulate paths from a path analysis file and generate results using a layer-by-layer approach.
    
    Args:
        path_file: Path to the CSV file with path analysis
        v3_pools_file: Path to the V3 pools data file
        input_amount: Input amount for simulation
        node_rpc_url: URL for the Ethereum node
        block_number: Block number for simulation
        name: Name for output files
        balance_dict: Dictionary of pool balances
        price_dict: Dictionary of token prices
        token_decimals_dict: Dictionary of token decimals
        weights_file: Path to the weights JSON file (optional)
        capacities_file: Path to the capacities JSON file (optional)
        lambda_threshold: Threshold for allocation (as a fraction of input)
    
    Returns:
        Tuple of (DataFrame with results, result dictionary)
    """
    # Create output directories
    os.makedirs('results', exist_ok=True)
    os.makedirs('results/path_analysis_json', exist_ok=True)
    os.makedirs('results/log', exist_ok=True)
    
    # Create simulation log file
    sim_log_path = f"results/path_analysis_json/path_simulation_{name}_{input_amount}.txt"
    log_file = open(sim_log_path, "w", encoding="utf-8")
    
    # Define a function to print to both console and log file
    def log_collect(message):
        log_file.write(message + "\n")
    
    log_collect(f"Starting path simulation for {name}")
    log_collect(f"Input amount: {input_amount}")
    log_collect(f"Block number: {block_number}")
    
    # Load path analysis data
    log_collect(f"Loading path analysis from {path_file}...")
    df = pd.read_csv(path_file)
    
    # Check if necessary columns exist
    if 'path' not in df.columns:
        log_collect(f"Error: 'path' column missing from {path_file}")
        log_file.close()
        return df, {"error": "path column missing"}
    
    input_token = df['path'].values[0].split(' -> ')[0]
    
    # Load pool weights and capacities if provided
    pool_weights = {}
    pool_capacities = {}
    
    if weights_file and os.path.exists(weights_file):
        log_collect(f"Loading weights from {weights_file}...")
        try:
            pool_weights = load_pool_weights(weights_file)
            log_collect(f"Loaded weights for {len(pool_weights)} token pairs")
        except Exception as e:
            log_collect(f"Error loading weights: {str(e)}")
    else:
        log_collect("No weights file provided or file not found, using default balance_dict")
    
    if capacities_file and os.path.exists(capacities_file):
        log_collect(f"Loading capacities from {capacities_file}...")
        try:
            pool_capacities = load_pool_capacities(capacities_file)
            log_collect(f"Loaded capacities for {len(pool_capacities)} token pairs")
        except Exception as e:
            log_collect(f"Error loading capacities: {str(e)}")
    else:
        log_collect("No capacities file provided or file not found, using default balance_dict")
    
    # Extract token paths from the dataset
    paths_tokens = []
    for i, row in df.iterrows():
        path_tokens = row['path'].split(' -> ')
        paths_tokens.append(path_tokens)
        
    # Generate pool addresses for paths
    log_collect("Generating pool addresses for paths...")
    token_pair_to_pools = {}
    
    # Build the lookup from weight/capacity data
    if pool_weights:
        for pair_key, pools_data in pool_weights.items():
            token_a, token_b = pair_key.split('_')
            token_pair = (token_a.lower(), token_b.lower())
            
            if token_pair not in token_pair_to_pools:
                token_pair_to_pools[token_pair] = []
                
            # Add all pool addresses for this token pair
            for pool_addr in pools_data.keys():
                if pool_addr not in token_pair_to_pools[token_pair]:
                    token_pair_to_pools[token_pair].append(pool_addr)
    
    # Now assign pool addresses to each path
    paths_pools = []
    for path_tokens in paths_tokens:
        pools = []
        for i in range(len(path_tokens) - 1):
            token_a = path_tokens[i].lower()
            token_b = path_tokens[i + 1].lower()
            
            # Try to find a pool for this token pair
            pair_key = (token_a, token_b)
            found_pool = False
            
            if pair_key in token_pair_to_pools and token_pair_to_pools[pair_key]:
                # Find the best pool (with lowest weight)
                best_pool = None
                best_weight = float('inf')
                
                for pool_addr in token_pair_to_pools[pair_key]:
                    # Check if this pool has weight/capacity data
                    pair_key_str = f"{token_a}_{token_b}"
                    reversed_pair_key = f"{token_b}_{token_a}"
                    
                    if (pair_key_str in pool_weights and pool_addr in pool_weights[pair_key_str]) or \
                       (reversed_pair_key in pool_weights and pool_addr in pool_weights[reversed_pair_key]):
                        # Found a valid pool
                        found_pool = True
                        
                        # Try to get the weight to find the best pool
                        pool_weight = float('inf')
                        if pair_key_str in pool_weights and pool_addr in pool_weights[pair_key_str]:
                            # Get lowest weight for this pool
                            for tick_data in pool_weights[pair_key_str][pool_addr]:
                                weight = tick_data.get('weight', float('inf'))
                                if weight < pool_weight:
                                    pool_weight = weight
                        elif reversed_pair_key in pool_weights and pool_addr in pool_weights[reversed_pair_key]:
                            # Get highest weight for this pool (reversed direction)
                            for tick_data in pool_weights[reversed_pair_key][pool_addr]:
                                weight = tick_data.get('weight', -float('inf'))
                                if -weight < pool_weight:  # Negate weight for reversed direction
                                    pool_weight = -weight
                        
                        # Update best pool if this one has better weight
                        if pool_weight < best_weight:
                            best_weight = pool_weight
                            best_pool = pool_addr
                
                if found_pool and best_pool:
                    pools.append(best_pool)
                else:
                    # Couldn't find a good pool, use the first one in the list
                    pools.append(token_pair_to_pools[pair_key][0])
                    found_pool = True
            
            if not found_pool:
                # No pool found for this token pair, use a placeholder
                log_collect(f"Warning: No pool found for {token_a} -> {token_b}")
                # Generate a deterministic placeholder based on the token addresses
                placeholder = f"0x{token_a[2:10]}{token_b[2:10]}"
                pools.append(placeholder)
        
        paths_pools.append(pools)
    
    # Update the DataFrame with the generated pool addresses
    df['involved_pool_addresses'] = paths_pools
    
    # Initialize pools_type_list by checking if pools are V2 or V3
    def determine_pool_types(pools):
        pool_types = []
        for pool_address in pools:
            pool_address = pool_address.lower()
            pool_type = 'v3'  # Default to V3
            
            # Check if this pool appears in pool_weights and has a pool_type
            for pair_key in pool_weights:
                if pool_address in pool_weights[pair_key]:
                    for tick_data in pool_weights[pair_key][pool_address]:
                        if 'pool_type' in tick_data:
                            pool_type = tick_data['pool_type']
                            break
                    if pool_type != 'v3':  # If we found a non-v3 type, no need to check further
                        break
            
            pool_types.append(pool_type)
        return pool_types
    
    df['pools_type_list'] = df['involved_pool_addresses'].apply(determine_pool_types)
    
    log_collect(f"Generated pool addresses for {len(paths_pools)} paths")
    
    # Load pools data
    log_collect("Loading pools data...")
    with open(v3_pools_file, "r") as file:
        v3_pools_data = json.load(file)
    with open(v2_pools_file, "r") as file:
        v2_pools_data = json.load(file)
    
    # Initialize V3 simulator
    log_collect("Initializing V3 simulator...")
    v3_simulator = V3Simulator()
    v3_simulator.initialize_pools(v3_pools_data)
    v2_simulator = V2Simulator(node_rpc_url)
    
    # Check if we have the necessary data
    if not pool_weights or not pool_capacities:
        log_collect("Using existing balance_dict since pool_weights or pool_capacities is missing")
        # Fall back to using balance_dict
        if not all(p.lower() in balance_dict for path in df['involved_pool_addresses'] for p in path):
            missing_pools = [p for path in df['involved_pool_addresses'] for p in path if p.lower() not in balance_dict]
            log_collect(f"Warning: {len(missing_pools)} pools in paths are missing from balance_dict")
    
    # Measure execution time of calculate_proportions_bps
    log_collect("\nStarting path allocation algorithm...")
    start_time = time.time()
    
    # Calculate lambda threshold in absolute terms (convert from percentage to amount)
    absolute_lambda_threshold = int(lambda_threshold * input_amount)
    log_collect(f"Using lambda threshold: {lambda_threshold} ({absolute_lambda_threshold} tokens)")
    
    try:
        # Call the path allocation function
        proportions_bps, remaining_amount, path_pool_allocations, pool_proportion_bps = calculate_proportions_bps(
            input_token, 
            df['involved_pool_addresses'].tolist(),
            paths_tokens,
            price_dict, 
            pool_capacities if pool_capacities else balance_dict,  # Use pool_capacities if available
            pool_weights if pool_weights else {},  # Use pool_weights if available
            token_decimals_dict,
            input_amount,
            absolute_lambda_threshold
        )
    except Exception as e:
        log_collect(f"Error during path allocation: {str(e)}")
        import traceback
        log_collect(traceback.format_exc())
        log_file.close()
        return df, {"error": str(e)}
    
    end_time = time.time()
    execution_time = end_time - start_time
    log_collect(f"Path allocation completed in {execution_time:.4f} seconds")
    log_collect(f"Number of paths processed: {len(paths_tokens)}")
    
    # Add proportions to DataFrame
    df['proportion_bps'] = proportions_bps
    
    # Calculate input amount for each path
    df['path_input_amount'] = df['proportion_bps'].apply(
        lambda x: (input_amount * x) // 10000
    )
    
    # Write detailed allocation information to log file
    log_collect("\n" + "="*80)
    log_collect("ALLOCATION SUMMARY")
    log_collect("="*80)
    
    # Sort paths by proportion (highest first) for the allocation log
    allocation_df = df.sort_values(by='proportion_bps', ascending=False).reset_index(drop=True)
    
    total_allocated = 0
    allocated_paths = 0
    for i, row in allocation_df.iterrows():
        path_id = row['path_id'] if 'path_id' in row else i + 1
        path_input = row['path_input_amount']
        proportion = row['proportion_bps']
        
        # Skip paths with zero input or zero proportion
        if path_input == 0 or proportion == 0:
            continue
            
        path_tokens = row['path']
        pool_addresses = row['involved_pool_addresses']
        pool_types = row['pools_type_list']
            
        total_allocated += path_input
        allocated_paths += 1
        
        log_collect(f"\nPath {path_id}:")
        log_collect(f"  Tokens: {path_tokens}")
        log_collect(f"  Proportion: {proportion/100:.2f}% ({proportion} bps)")
        log_collect(f"  Input amount: {path_input} ({path_input/input_amount*100:.4f}% of total)")
    
    log_collect("\nAllocation Summary:")
    log_collect(f"  Total input amount: {input_amount}")
    log_collect(f"  Paths with allocation: {allocated_paths} out of {len(paths_tokens)}")
    log_collect(f"  Total allocated: {total_allocated} ({total_allocated/input_amount*100:.4f}% of input)")
    log_collect(f"  Unallocated: {input_amount - total_allocated} ({(input_amount - total_allocated)/input_amount*100:.4f}% of input)")
    log_collect("="*80 + "\n")
    
    # Prepare result structure
    result = {
        "blockNumber": block_number,
        "fromAmount": str(input_amount),
        "from": "",  # Will be set from first path
        "toAmount": "0",  # Will be updated after simulation
        "to": "",  # Will be set from first path
        "route": {
            "fills": [],
        },
        'remaining_amount': remaining_amount
    }
    
    # Set source and destination tokens if available
    if not df.empty and 'path' in df.columns:
        first_path = df.iloc[0]['path']
        if ' -> ' in first_path:
            tokens = first_path.split(' -> ')
            result["from"] = tokens[0].lower()
            result["to"] = tokens[-1].lower()
    
    # Collect all tokens used in paths
    all_tokens = set()
    for _, row in df.iterrows():
        path_tokens = row['path']
        tokens = path_tokens.split(' -> ')
        for token in tokens:
            all_tokens.add(token.strip().lower())
    
    # Use the directly calculated pool proportions to create fills
    for pool_key, bps in pool_proportion_bps.items():
        # Skip pools with zero proportion
        if bps <= 0:
            continue
        
        # Check if this is a multi-key entry (pool used for multiple token pairs)
        if '_' in pool_key and pool_key.count('_') >= 2:
            # This is a multi-key with format: pool_address_token_in_token_out
            parts = pool_key.split('_', 3)
            pool_address = parts[0]
            token_in = parts[1]
            token_out = parts[2]
            
            # Determine pool type
            pool_type = "v3"  # Default
            token_pair_key = f"{token_in}_{token_out}"
            
            if token_pair_key in pool_weights and pool_address in pool_weights[token_pair_key]:
                for tick_data in pool_weights[token_pair_key][pool_address]:
                    if 'pool_type' in tick_data:
                        pool_type = tick_data['pool_type']
                        break
            
            # When logging, still find and log the tick for reference, but don't include in the result
            tick = None
            if pool_type == "v3" and token_pair_key in pool_capacities and pool_address in pool_capacities[token_pair_key]:
                # Get the first available tick with capacity
                for capacity_entry in pool_capacities[token_pair_key][pool_address]:
                    if capacity_entry.get('capacity', 0) > 0:
                        tick = capacity_entry.get('tick')
                        log_collect(f"Found tick {tick} for pool {pool_address}")
                        break
            
            fill = {
                "from": token_in,
                "to": token_out,
                "pool": pool_address,
                "source": "Uniswap_V" + pool_type[-1],  # Extract V2 or V3 from pool_type
                "poolTotalProportionBps": str(bps)
            }
            
            # Don't add tick to the fill
                
            result["route"]["fills"].append(fill)
        else:
            # This is a regular pool_address entry
            pool_address = pool_key
            
            # Find token pair and other details for this pool
            found = False
            for path_idx, edge_data in path_pool_allocations.items():
                for edge_idx, pools_data in edge_data.items():
                    for pool_addr, tick, pool_type, proportion in pools_data:
                        if pool_addr.lower() == pool_address.lower():
                            # Found the pool, get token pair
                            token_in = paths_tokens[path_idx][edge_idx].lower()
                            token_out = paths_tokens[path_idx][edge_idx + 1].lower()
                            
                            # Log the tick if found, for debugging purposes
                            if tick is not None:
                                log_collect(f"Found tick {tick} for pool {pool_address}")
                            
                            fill = {
                                "from": token_in,
                                "to": token_out,
                                "pool": pool_address,
                                "source": "Uniswap_V" + pool_type[-1],  # Extract V2 or V3 from pool_type
                                "poolTotalProportionBps": str(bps)
                            }
                            
                            # Don't add tick to the fill
                                
                            result["route"]["fills"].append(fill)
                            found = True
                            break
                    if found:
                        break
                if found:
                    break
    
    # ----- NEW GLOBAL POOL AGGREGATION SIMULATION -----
    log_collect("\nStarting simulation with global pool aggregation...")
    
    # Initialize V3 simulator
    log_collect("Initializing simulators...")
    v3_simulator = V3Simulator()
    v3_simulator.initialize_pools(v3_pools_data)
    v2_simulator = V2Simulator(node_rpc_url)
    
    # Use the fills from the route built above
    fills = result["route"]["fills"]
    
    # Define function for single hop simulation
    def single_hop_simulation(
            token_in_addr: str,
            token_out_addr: str,
            pool_addr: str,
            pool_type: str,
            amount_in: int,
            tick=None,
            fee=None
    ) -> int:
        """Perform a single hop simulation using either V2 or V3 data."""
        if amount_in == 0:
            return 0
        
        # Log the simulation
        log_collect(f"  Simulating {token_in_addr} -> {token_out_addr} via {pool_addr} (Type: {pool_type})")
        log_collect(f"  Input amount: {amount_in}")
        
        # Determine if it's V2 or V3
        pool_type_lower = pool_type.lower()
        amount_out = 0
        
        if "v3" in pool_type_lower:
            # Find pool data from v3_pools_data
            pool = None
            for p in v3_pools_data:
                if p["poolState"]["poolStaticInfo"]["poolAddress"].lower() == pool_addr.lower():
                    pool = p
                    break
            
            if not pool:
                log_collect(f"  Error: V3 pool {pool_addr} not found in data")
                return 0
            
            # Get the fee if not provided
            if fee is None:
                fee = int(pool["poolState"]["poolStaticInfo"]["swapFee"])
            
            try:
                # Use V3 simulator
                amount_out = v3_simulator.exact_input(
                    Web3.to_checksum_address(token_in_addr),
                    Web3.to_checksum_address(token_out_addr),
                    fee,
                    amount_in,
                    block_number,
                    0
                )
            except Exception as e:
                log_collect(f"  Error simulating V3 swap: {str(e)}")
                amount_out = 0
        
        elif "v2" in pool_type_lower:
            # Find pool data from v2_pools_data
            pool = None
            for p in v2_pools_data:
                if p["poolState"]["poolStaticInfo"]["poolAddress"].lower() == pool_addr.lower():
                    pool = p
                    break
            
            if not pool:
                log_collect(f"  Error: V2 pool {pool_addr} not found in data")
                return 0
            
            try:
                # Extract token information and reserves
                t0 = pool["poolState"]["poolStaticInfo"]["token0"].lower()
                t1 = pool["poolState"]["poolStaticInfo"]["token1"].lower()
                reserve0 = int(pool["poolState"]["poolState"]["tokenBalance0"])
                reserve1 = int(pool["poolState"]["poolState"]["tokenBalance1"])
                
                # Determine which reserves to use based on token direction
                if token_in_addr.lower() == t0 and token_out_addr.lower() == t1:
                    reserve_in = reserve0
                    reserve_out = reserve1
                elif token_in_addr.lower() == t1 and token_out_addr.lower() == t0:
                    reserve_in = reserve1
                    reserve_out = reserve0
                else:
                    log_collect(f"  Error: Token mismatch for V2 pool {pool_addr}")
                    return 0
                
                # Use V2 simulator
                amount_out = v2_simulator.get_amount_out(
                    amount_in,
                    reserve_in,
                    reserve_out
                )
            except Exception as e:
                log_collect(f"  Error simulating V2 swap: {str(e)}")
                amount_out = 0
        
        else:
            log_collect(f"  Error: Unknown pool type {pool_type}")
            amount_out = 0
        
        log_collect(f"  Output amount: {amount_out}")
        return amount_out
    
    log_collect(f"Using {len(fills)} fills from route information")
    log_collect("\n----- SIMULATION USING TOPOLOGICAL APPROACH -----")
    
    # Step 2: Create an adjacency list from fills
    adjacency = {}
    for fill in fills:
        token_in = fill["from"].lower()
        adjacency.setdefault(token_in, []).append(fill)
    
    # Step 3: Initialize token balances
    token_balances = {}
    input_token = result["from"].lower()
    target_token = result["to"].lower()
    token_balances[input_token] = input_amount
    
    log_collect(f"Starting with {input_amount} {input_token}")
    
    # Step 4: Simulate token flow using topological approach
    processed = set()
    changed = True
    iteration = 0
    
    # 构建入边计数
    incoming_edges = defaultdict(int)
    for token, edges in adjacency.items():
        for edge in edges:
            token_out = edge["to"].lower()
            incoming_edges[token_out] += 1

    # 跟踪已处理的入边
    processed_incoming = defaultdict(int)

    # 初始队列（仅包含起始代币）
    queue = [input_token]  
    processed_incoming[input_token] = incoming_edges[input_token]  # 起始代币的入边已全部处理

    while queue:
        token = queue.pop(0)
        
        # 跳过没有余额的代币
        if token not in token_balances or token_balances[token] <= 0:
            continue
        
        token_balance = token_balances[token]
        log_collect(f"Processing token {token} with balance {token_balance}")
        
        # 计算总分配比例
        total_proportion = 0
        for edge in adjacency.get(token, []):
            total_proportion += int(edge["poolTotalProportionBps"])
        
        if total_proportion <= 0:
            continue
        
        # 分配代币余额到各出边
        for edge in adjacency.get(token, []):
            proportion_bps = int(edge["poolTotalProportionBps"])
            edge_amount = (token_balance * proportion_bps) // total_proportion
            
            if edge_amount <= 0:
                continue
            
            token_in = edge["from"].lower()
            token_out = edge["to"].lower()
            pool_addr = edge["pool"].lower()
            pool_type = edge["source"]
            
            # 模拟交易
            output_amount = single_hop_simulation(
                token_in_addr=token_in,
                token_out_addr=token_out,
                pool_addr=pool_addr,
                pool_type=pool_type,
                amount_in=edge_amount,
                tick=edge.get("tick"),
                fee=edge.get("fee")
            )
            
            # 更新目标代币余额
            if token_out not in token_balances:
                token_balances[token_out] = 0
            token_balances[token_out] += output_amount
            
            # 更新入边计数
            processed_incoming[token_out] += 1
            
            # 检查目标代币是否所有入边都已处理
            if processed_incoming[token_out] == incoming_edges[token_out]:
                queue.append(token_out)
        
        # 该代币余额已分配完毕
        token_balances[token] = 0
    
    # Step 5: Get final output amount for target token
    final_output = token_balances.get(target_token, 0)
    
    # Format for readability if large number
    formatted_output = format(final_output, ',') if isinstance(final_output, int) else str(final_output)
    log_collect(f"\nFinal output amount for {target_token}: {formatted_output}")
    
    # Update the result with the final output amount
    result["toAmount"] = str(final_output)
    
    # Initialize output_amount column in DataFrame
    df['output_amount'] = ''
    
    # Allocate the output proportionally to paths based on their original allocation
    # This is to keep the DataFrame consistent with the expected output format
    total_proportion = sum(df['proportion_bps'])
    if total_proportion > 0 and final_output > 0:
        for i, row in df.iterrows():
            prop = row['proportion_bps']
            if prop > 0:
                # Allocate output proportionally
                path_output = (final_output * prop) // total_proportion
                df.at[i, 'output_amount'] = str(path_output)
    
    # Add reranking statistics to the existing log file
    # Extract and report reranking statistics from the calculation
    log_collect("\n" + "="*80)
    log_collect("RERANKING STATISTICS")
    log_collect("="*80)
    
    # Get reranking stats from the allocation process
    if hasattr(calculate_proportions_bps, 'last_rerank_stats'):
        stats = calculate_proportions_bps.last_rerank_stats
        log_collect(f"Total reranking rounds: {stats.rounds}")
        
        if stats.rounds > 0:
            log_collect(f"\nPath changes per round:")
            for i, changes in enumerate(stats.path_changes_per_round):
                log_collect(f"  Round {i+1}: {changes} paths changed")
            
            log_collect(f"\nTime spent updating weights/capacities:")
            for i, t in enumerate(stats.update_times_per_round):
                log_collect(f"  Round {i+1}: {t:.6f} seconds")
            log_collect(f"  Total: {stats.total_update_time:.6f} seconds")
            
            log_collect(f"\nTime spent sorting paths:")
            for i, t in enumerate(stats.sort_times_per_round):
                log_collect(f"  Round {i+1}: {t:.6f} seconds")
            log_collect(f"  Total: {stats.total_sort_time:.6f} seconds")
    else:
        log_collect("No reranking statistics available")
    
    log_collect("="*80)
    
    # Close the log file
    log_file.flush()
    log_file.close()
    
    return df, result



def load_token_prices(price_file_path):
    """
    Load token prices from the quotes file
    Returns a dictionary mapping token address to its WETH price
    """
    # Create log directory if it doesn't exist
    os.makedirs('results/log', exist_ok=True)
    token_prices_log_path = f"results/log/token_prices_load.txt"
    token_prices_log = open(token_prices_log_path, "a", encoding="utf-8")  # Append mode
    
    # Function to log messages
    def log_token_prices(message):
        token_prices_log.write(f"{message}\n")
    
    try:
        log_token_prices(f"Loading token prices from {price_file_path}")
        with open(price_file_path, 'r') as file:
            price_data = json.load(file)
        
        # Create price mapping
        token_prices = {}
        for token in price_data:
            address = token['address'].lower()
            # Use avg_quote as the price, default to 0 if not available
            try:
                price = float(token['quotes']['avg_quote'])
            except (KeyError, ValueError, TypeError):
                price = 0
            token_prices[address] = price
        
        log_token_prices(f"Loaded price data for {len(token_prices)} tokens")
        token_prices_log.close()
        return token_prices
    except Exception as e:
        log_token_prices(f"Error loading token prices: {e}")
        token_prices_log.close()
        return {}

def load_pool_capacities(capacity_file_path):
    """
    Load pool capacities from a JSON file.
    
    Args:
        capacity_file_path: Path to the JSON file containing pool capacities
        
    Returns:
        Dictionary mapping token pairs and pool addresses to their capacity information
    """
    # Create log directory if it doesn't exist
    os.makedirs('results/log', exist_ok=True)
    capacity_log_path = f"results/log/pool_capacities_load.txt"
    capacity_log = open(capacity_log_path, "a", encoding="utf-8")  # Append mode
    
    # Function to log messages
    def log_capacity(message):
        capacity_log.write(f"{message}\n")
    
    try:
        log_capacity(f"Loading pool capacities from {capacity_file_path}")
        with open(capacity_file_path, 'r') as f:
            capacities = json.load(f)
        log_capacity(f"Loaded capacities for {len(capacities)} token pairs")
        capacity_log.close()
        return capacities
    except Exception as e:
        log_capacity(f"Error loading pool capacities: {str(e)}")
        capacity_log.close()
        return {}


def load_pool_weights(weights_file_path):
    """
    Load pool weights from a JSON file.
    
    Args:
        weights_file_path: Path to the JSON file containing pool weights
        
    Returns:
        Dictionary mapping token pairs and pool addresses to their weight information
    """
    # Create log directory if it doesn't exist
    os.makedirs('results/log', exist_ok=True)
    weights_log_path = f"results/log/pool_weights_load.txt"
    weights_log = open(weights_log_path, "a", encoding="utf-8")  # Append mode
    
    # Function to log messages
    def log_weights(message):
        weights_log.write(f"{message}\n")
    
    try:
        log_weights(f"Loading pool weights from {weights_file_path}")
        with open(weights_file_path, 'r') as f:
            weights = json.load(f)
        log_weights(f"Loaded weights for {len(weights)} token pairs")
        weights_log.close()
        return weights
    except Exception as e:
        log_weights(f"Error loading pool weights: {str(e)}")
        weights_log.close()
        return {}

def load_pool_data(weights_file_path=None, capacities_file_path=None):
    """
    Load pool weights and capacities from JSON files.
    
    Args:
        weights_file_path: Path to the JSON file containing pool weights (optional)
        capacities_file_path: Path to the JSON file containing pool capacities (optional)
        
    Returns:
        Tuple of (pool_weights, pool_capacities) dictionaries
    """
    pool_weights = {}
    pool_capacities = {}
    
    # Create a log file if it doesn't exist
    os.makedirs('results/log', exist_ok=True)
    pool_data_log_path = f"results/log/pool_data_load.txt"
    pool_data_log = open(pool_data_log_path, "a", encoding="utf-8")  # Append mode
    
    # Function to log messages
    def log_pool_data(message):
        pool_data_log.write(f"{message}\n")
    
    # Load weights if file path provided
    if weights_file_path and os.path.exists(weights_file_path):
        try:
            with open(weights_file_path, 'r') as f:
                pool_weights = json.load(f)
            log_pool_data(f"Loaded weights for {len(pool_weights)} token pairs from {weights_file_path}")
        except Exception as e:
            log_pool_data(f"Error loading weights: {str(e)}")
    
    # Load capacities if file path provided
    if capacities_file_path and os.path.exists(capacities_file_path):
        try:
            with open(capacities_file_path, 'r') as f:
                pool_capacities = json.load(f)
            log_pool_data(f"Loaded capacities for {len(pool_capacities)} token pairs from {capacities_file_path}")
        except Exception as e:
            log_pool_data(f"Error loading capacities: {str(e)}")
    
    pool_data_log.close()
    return pool_weights, pool_capacities

def run_simulation(
    path_file,
    v3_pools_file,
    v2_pools_file,
    input_amount,
    node_rpc_url,
    block_number,
    balance_dict,
    price_dict,
    token_decimals_dict,
    name="default",
    weights_file=None,
    capacities_file=None,
    lambda_threshold=0
):
    """
    Run a simulation using the layer-by-layer approach.
    
    Args:
        path_file: Path to the path analysis file
        v3_pools_file: Path to the V3 pools file
        v2_pools_file: Path to the V2 pools file
        input_amount: Input amount
        node_rpc_url: Node RPC URL
        block_number: Block number
        balance_dict: Dictionary of pool balances
        price_dict: Dictionary of token prices
        token_decimals_dict: Dictionary of token decimals
        name: Name for output files
        weights_file: Path to the weights JSON file (optional)
        capacities_file: Path to the capacities JSON file (optional)
        lambda_threshold: Threshold for allocation (as a fraction of input)
        
    Returns:
        Result of the simulation
    """
    # Create output directories
    os.makedirs('results/log', exist_ok=True)
    
    # Create a separate log file for run_simulation
    run_log_path = f"results/log/run_simulation_{name}_{input_amount}.txt"
    run_log_file = open(run_log_path, "w", encoding="utf-8")
    
    # Define a function to print to both console and log file
    def log_run(message):
        run_log_file.write(message + "\n")
    
    log_run(f"Running simulation for {path_file} with input amount {input_amount}")
    log_run(f"Using weights file: {weights_file}")
    log_run(f"Using capacities file: {capacities_file}")
    log_run(f"Using lambda threshold: {lambda_threshold}")
    
    # Load pool weights and capacities
    pool_weights, pool_capacities = load_pool_data(weights_file, capacities_file)
    
    try:
        df, result = simulate_paths_from_analysis(
            path_file,
            v3_pools_file,
            v2_pools_file,
            input_amount,
            node_rpc_url,
            block_number,
            name,
            balance_dict,
            price_dict,
            token_decimals_dict,
            weights_file,
            capacities_file,
            lambda_threshold
        )
        
        # Make a copy of the result and ensure all number fields are properly stringified
        json_safe_result = result.copy()
        
        # Convert any potential numeric or Decimal values to strings to ensure proper JSON serialization
        if 'fromAmount' in json_safe_result and not isinstance(json_safe_result['fromAmount'], str):
            json_safe_result['fromAmount'] = str(json_safe_result['fromAmount'])
            
        if 'toAmount' in json_safe_result and not isinstance(json_safe_result['toAmount'], str):
            json_safe_result['toAmount'] = str(json_safe_result['toAmount'])
            
        if 'remaining_amount' in json_safe_result and not isinstance(json_safe_result['remaining_amount'], str):
            json_safe_result['remaining_amount'] = str(json_safe_result['remaining_amount'])
            
        # Ensure all numeric values in fills are strings and remove tick info
        if 'route' in json_safe_result and 'fills' in json_safe_result['route']:
            for fill in json_safe_result['route']['fills']:
                if 'poolTotalProportionBps' in fill and not isinstance(fill['poolTotalProportionBps'], str):
                    fill['poolTotalProportionBps'] = str(fill['poolTotalProportionBps'])
                
                # Remove tick information if present
                if 'tick' in fill:
                    del fill['tick']
        
        # Save result to JSON file
        os.makedirs('results/path_analysis_json', exist_ok=True)
        result_file = f"results/path_analysis_json/simulate_result_{name}_{input_amount}.json"
        with open(result_file, "w") as f:
            json.dump(json_safe_result, f, indent=2)
        
        log_run(f"Simulation result saved to {result_file}")
        log_run(f"Total output amount: {result['toAmount']}")
        
        # Close the log file
        log_run(f"Run simulation log saved to: {run_log_path}")
        run_log_file.close()
        
        return result
    
    except Exception as e:
        log_run(f"Error during simulation: {str(e)}")
        import traceback
        log_run(traceback.format_exc())
        run_log_file.close()
        return {"error": str(e)}


def main():
    # Add command-line argument parsing
    import argparse
    
    parser = argparse.ArgumentParser(description='Simulate token paths')
    parser.add_argument('--pairs', type=str, default="wbtc-spx", 
                        help='Comma-separated list of source-target pairs (e.g., "usdt-frax,usdt-ohm")')
    parser.add_argument('--max_length', type=int, default=3, help='Maximum path length')
    parser.add_argument('--allocation_threshold', type=float, default=0, 
                        help='Allocation threshold (as a fraction of input amount)')
    args = parser.parse_args()
    
    # Create main log file
    os.makedirs('results/log', exist_ok=True)
    main_log_path = f"results/log/main_execution_{time.strftime('%Y%m%d_%H%M%S')}.txt"
    main_log = open(main_log_path, "w", encoding="utf-8")
    
    # Function to log messages
    def log_main(message):
        main_log.write(f"{message}\n")
    
    # Configuration
    block_number = 21974203
    graph_name = 'combined'
    k = 10000
    max_length = args.max_length
    lambda_threshold = args.allocation_threshold
    
    log_main(f"Configuration:")
    log_main(f"  Max path length: {max_length}")
    log_main(f"  Allocation threshold: {lambda_threshold}")

    node_name_file = f"../data/node_name.txt"
    node_mapping_file = f"graphs/{block_number}_{graph_name}_node_map.txt"
    log_main(f"Loading node mappings from {node_name_file} and {node_mapping_file}")
    node_name, node_index, index_name = load_node_mappings(node_name_file, node_mapping_file)
    
    # Parse pairs from command-line argument
    pairs = args.pairs.split(',')
    log_main(f"Processing pairs: {pairs}")
    
    for pair in pairs:
        source_symbol, target_symbol = pair.split('-')
    
        source = node_index[node_name[source_symbol]]
        target = node_index[node_name[target_symbol]]

        names = [f'all_paths_{source}_{target}_{max_length}']

        node_rpc_url = ETH_NODE_URL

        log_main(f"Source symbol: {source_symbol}, Target symbol: {target_symbol}")
        log_main(f"Source node: {source}, Target node: {target}")
        log_main(f"Node RPC URL: {node_rpc_url}")

        if source_symbol == 'usdt':
            input_amounts = [200000000000, 1000000000000, 5000000000000]
        elif source_symbol == 'wbtc':
            input_amounts = [2000000000, 10000000000, 60000000000]
        elif source_symbol == 'reth':
            input_amounts = [20000000000000000000, 100000000000000000000, 500000000000000000000]
        elif source_symbol == 'spx':
            input_amounts = [40000000000000, 200000000000000, 1000000000000000]
        else:
            # Default for other tokens (use medium values)
            input_amounts = [1000000000000000000]

        log_main(f"Input amounts: {input_amounts}")

        print(f"\n{'=' * 80}")
        print(f"Processing pair: {source_symbol}-{target_symbol}")
        print(f"Max path length: {max_length}")
        print(f"Allocation threshold: {lambda_threshold}")
        print(f"Input amounts: {input_amounts}")
        print(f"{'=' * 80}\n")

        # Path to capacities and weights files
        capacities_file = f'graphs/{block_number}_pool_capacities.json'
        weights_file = f'graphs/{block_number}_pool_weights.json'
        
        # Check if weights and capacities files exist
        log_main(f"Checking weights file: {weights_file}")
        log_main(f"Checking capacities file: {capacities_file}")
        
        if not os.path.exists(weights_file):
            log_main(f"Warning: Weights file {weights_file} not found")
        
        if not os.path.exists(capacities_file):
            log_main(f"Warning: Capacities file {capacities_file} not found")
        
        # Load token prices and decimals
        log_main(f"Loading token prices from ../data/prices/block-{block_number}/tokens-quotes.json")
        price_dict = load_token_prices(f'../data/prices/block-{block_number}/tokens-quotes.json')
        log_main(f"Loading token decimals from ../data/token-decimals.csv")
        token_decimals_df = pd.read_csv(f'../data/token-decimals.csv')
        token_decimals_dict = {}
        for index, row in token_decimals_df.iterrows():
            token_decimals_dict[row['token_address']] = row['decimals']

        # Initialize token lookup for get_token_pair_key function
        token_lookup = {}
        for name, addr in node_name.items():
            token_lookup[name.lower()] = addr.lower()
        
        # Add addresses mapped to themselves for direct lookups
        for _, addr in node_name.items():
            token_lookup[addr.lower()] = addr.lower()
        
        # Initialize the lookup
        log_main("Initializing token lookup for get_token_pair_key function")
        initialize_token_lookup(token_lookup)

        # Process paths first if needed
        for name in names:
            path_file = f'results/{name}.txt'
            node_map_file = f'graphs/{block_number}_{graph_name}_node_map.txt'
            output_dir = 'results/path_analysis_df'
            
            # Process paths only if the CSV doesn't exist yet
            csv_path_file = f'{output_dir}/path_analysis_{name}.csv'
            if not os.path.exists(csv_path_file):
                log_main(f"Processing paths from {path_file}...")
                process_and_save_paths(
                    path_file=path_file,
                    node_map_file=node_map_file,
                    output_dir=output_dir,
                    name=name,
                    is_disjoint=False,
                    num_paths=k
                )
            else:
                log_main(f"Path analysis file {csv_path_file} already exists, skipping processing.")

        # Proceed with simulation
        for name in names:   
            # Load pool balances as fallback
            balance_dict = {}
            try:
                # Try to load balances from a separate file if available
                balance_file = f'../data/prices/block-{block_number}/pool-balances.json'
                if os.path.exists(balance_file):
                    with open(balance_file, 'r') as f:
                        balance_dict = json.load(f)
                    log_main(f"Loaded balances for {len(balance_dict)} pools from {balance_file}")
                else:
                    log_main(f"Balance file {balance_file} not found, using empty balance dict")
            except Exception as e:
                log_main(f"Error loading balances: {str(e)}")

            path_file = f'results/path_analysis_df/path_analysis_{name}.csv'
            v3_pools_file = f'../data/prices/block-{block_number}/filtered-v3pools.json'
            v2_pools_file = f'../data/prices/block-{block_number}/filtered-v2pools.json'
            
            for input_amount in input_amounts:
                # Call the run_simulation function with all required parameters
                log_main(f"Running simulation for {name} with input amount {input_amount}")
                result = run_simulation(
                    path_file=path_file,
                    v3_pools_file=v3_pools_file,
                    v2_pools_file=v2_pools_file,
                    input_amount=input_amount,
                    node_rpc_url=node_rpc_url,
                    block_number=block_number,
                    balance_dict=balance_dict,
                    price_dict=price_dict,
                    token_decimals_dict=token_decimals_dict,
                    name=name,
                    weights_file=weights_file,
                    capacities_file=capacities_file,
                    lambda_threshold=lambda_threshold
                )
                log_main(f"Simulation for {name} with input amount {input_amount} completed")
                if "error" in result:
                    log_main(f"Error in simulation: {result['error']}")
        
        log_main("Main execution completed")
    main_log.close()


if __name__ == "__main__":
    main()
