import json
import math
import networkx as nx
import pandas as pd
import os
import traceback
from collections import defaultdict
import heapq
from collections import defaultdict, deque
from decimal import Decimal, getcontext, ROUND_DOWN
import argparse

# Ensure Decimal precision is high enough for financial calculations
getcontext().prec = 60

# Define Decimal constants for V2 pool calculations
DECIMAL_ZERO = Decimal(0)
DECIMAL_ONE = Decimal(1)
# Define a small non-zero value to avoid division by zero
DECIMAL_EPSILON = Decimal('1E-18')

# Define capacity threshold for filtering ticks
CAPACITY_THRESHOLD = 1

def load_data_from_json(file_path, filtered_pools=None):
    """
    Load data from a JSON file
    
    Args:
        file_path: Path to the JSON file
        filtered_pools: Optional set of pool addresses to filter by
    """
    with open(file_path, 'r') as file:
        data = json.load(file)
    
    # If filtered_pools is provided, only return pools in the filter list
    if filtered_pools:
        filtered_data = []
        for pool in data:
            try:
                pool_data = json.loads(pool) if isinstance(pool, str) else pool
                pool_address = pool_data['poolState']['poolState']['poolAddress'].lower()
                if pool_address in filtered_pools:
                    filtered_data.append(pool)
            except (KeyError, json.JSONDecodeError):
                continue
        return filtered_data
    
    return data

def load_token_prices(price_file_path):
    """
    Load token prices from the quotes file
    Returns a dictionary mapping token address to its WETH price
    """
    try:
        with open(price_file_path, 'r') as file:
            price_data = json.load(file)
        
        # Create price mapping
        token_prices = {}
        for token in price_data:
            address = token['address'].lower()
            # Use avg_quote as the price, default to 0 if not available
            try:
                price = float(token['quotes']['avg_quote'])
            except (KeyError, ValueError, TypeError):
                price = 0
            token_prices[address] = price
            
        return token_prices
    except Exception as e:
        print(f"Error loading token prices: {e}")
        return {}

def calculate_exchange_rates_v3(pools, token_prices=None, liquidity_threshold=0, max_nearby_ticks=5, capacity_threshold=CAPACITY_THRESHOLD):
    """
    Compute the exchange rate using Uniswap V3's sqrtPriceX96 and tick data.
    
    This function processes Uniswap V3 pools to extract exchange rates at:
    1. Current price point
    2. Up to max_nearby_ticks to the left of current price
    3. Up to max_nearby_ticks to the right of current price
    
    Args:
        pools: List of pool data from Uniswap V3
        token_prices: Dictionary mapping token addresses to their WETH prices
        liquidity_threshold: Minimum liquidity value in ETH to consider a tick
        max_nearby_ticks: Maximum number of ticks to consider in each direction from current
        capacity_threshold: Minimum capacity value to add to the JSON output
        
    Returns:
        results: List of exchange rates for all pools
        skipped_pools: Number of pools skipped due to errors
        pool_capacities_dict: Dictionary mapping token pairs to {pool_address: [capacities]}
        pool_weights_dict: Dictionary mapping token pairs to {pool_address: [weights]}
    """
    results = []
    skipped_pools = 0
    pool_capacities_dict = defaultdict(lambda: defaultdict(list))  # {token_pair: {pool_address: [capacities]}}
    pool_weights_dict = defaultdict(lambda: defaultdict(list))     # {token_pair: {pool_address: [weights]}}
    
    for pool in pools:
        try:
            pool_data = json.loads(pool) if isinstance(pool, str) else pool
            
            if pool_data['poolType'] == 'uniswap_v3_like_pool':
                pool_static_info = pool_data['poolState']['poolStaticInfo']
                if pool_static_info['dexExchange'] == 'uniswap':
                    pool_state = pool_data['poolState']['poolState']
                    pool_address = pool_state['poolAddress'].lower()
                    
                    # Extract token addresses
                    token0 = pool_static_info['token0'].lower()
                    token1 = pool_static_info['token1'].lower()

                    token0_address = pool_static_info['token0'].lower()
                    token1_address = pool_static_info['token1'].lower()

                    token0_decimals = int(pool_static_info['token0Decimals'])
                    token1_decimals = int(pool_static_info['token1Decimals'])
                    
                    # Create token pair keys
                    token_pair_0to1 = f"{token0}_{token1}"
                    token_pair_1to0 = f"{token1}_{token0}"
                    
                    # ===== EXTRACT CURRENT PRICE AND POOL PARAMETERS =====
                    
                    # Get current tick and tick spacing
                    current_tick = int(pool_state.get('currentTick', 0))
                    tick_spacing = int(pool_static_info.get('tickSpacing', 1))
                    
                    # Get initialized ticks with liquidity
                    initialized_ticks = []
                    
                    for tick_entry in pool_state.get('tickBitMap', []):
                        if len(tick_entry) == 2:
                            tick_idx = int(tick_entry[0])
                            tick_data = tick_entry[1]
                            
                            # Only consider initialized ticks with liquidity
                            if tick_data.get('initialized', False) and int(tick_data.get('liquidityGross', 0)) > 0:
                                initialized_ticks.append((tick_idx, tick_data))
                    
                    # Sort ticks for easier processing
                    initialized_ticks.sort(key=lambda x: x[0])
                    
                    # Create a dictionary for quick lookup
                    tick_bitmap = {tick_idx: data for tick_idx, data in initialized_ticks}
                    
                    # Function to compute sqrt price from tick
                    def tick_to_price(tick):
                        """直接从 tick 计算价格 (token1/token0)"""
                        return 1.0001 ** tick
                    
                    def tick_to_adjusted_price(tick, token0_decimals, token1_decimals):
                        """返回考虑小数位的实际价格"""
                        raw_price = 1.0001 ** tick
                        decimal_adjustment = 10 ** (token0_decimals - token1_decimals)
                        return raw_price * decimal_adjustment
                            
                    # Find the active tick range containing current_tick
                    active_tick_lower = None
                    active_tick_upper = None
                    for i in range(len(initialized_ticks) - 1):
                        if initialized_ticks[i][0] <= current_tick < initialized_ticks[i+1][0]:
                            active_tick_lower = initialized_ticks[i][0]
                            active_tick_upper = initialized_ticks[i+1][0]
                            break
                    
                    # If we couldn't find the active tick range, use current tick as reference
                    if active_tick_lower is None and active_tick_upper is None:
                        active_tick_lower = current_tick
                        active_tick_upper = current_tick
                    
                    # Get swap fee (stored as parts per million)
                    swap_fee = int(pool_static_info.get('swapFee', 0)) / 1e6  # Convert to percentage
                    
                    # ===== PROCESS CURRENT TICK =====
                    if active_tick_lower is not None:
                        current_price = tick_to_price(current_tick)
                        
                        # Calculate current liquidity by accumulating liquidityNet values
                        current_liquidity = 0
                        for tick_idx, tick_data in initialized_ticks:
                            if tick_idx <= current_tick:
                                current_liquidity += int(tick_data.get('liquidityNet', 0))
                            else:
                                break
                        
                        price_lower = tick_to_price(active_tick_lower)
                        price_upper = tick_to_price(active_tick_upper)
                        
                        # Calculate token amounts in the current tick range
                        token0_balance = current_liquidity * (1/math.sqrt(current_price) - 1/math.sqrt(price_upper)) / (10**token0_decimals)
                        token1_balance = current_liquidity * (math.sqrt(current_price) - math.sqrt(price_lower)) / (10**token1_decimals)
                        
                        token0_value_eth = token0_balance * token_prices.get(token0_address, 0)
                        token1_value_eth = token1_balance * token_prices.get(token1_address, 0)
                        
                        # Calculate capacity in ETH
                        if token0_address in token_prices and token1_address in token_prices:
                            tick_value_eth = [token0_value_eth, token1_value_eth]
                            # if current_tick < active_tick_lower:
                            #     tick_value_eth = [token1_value_eth, token0_value_eth] # debugging
                            # else:
                            #     tick_value_eth = [token0_value_eth, token1_value_eth] # debugging
                            
                            if tick_value_eth[0] + tick_value_eth[1] < liquidity_threshold:
                                continue
                            
                            tick_price_lower = tick_to_adjusted_price(active_tick_lower, token0_decimals, token1_decimals)
                            tick_price_upper = tick_to_adjusted_price(active_tick_upper, token0_decimals, token1_decimals)
                            
                            # Apply swap fee multiplier
                            lambda_tax = float(swap_fee)
                            rate0to1 = (1 - lambda_tax) * tick_price_lower
                            rate1to0 = (1 - lambda_tax) * (1 / tick_price_upper)
                            
                            # Calculate edge weights as negative log of exchange rate
                            log_base = math.e  # Using natural logarithm for edge weights
                            weight0to1 = -math.log(rate0to1, log_base)
                            weight1to0 = -math.log(rate1to0, log_base)
                            
                            # Store weights and capacities for both directions
                            if tick_value_eth[1] >= capacity_threshold:
                                pool_capacities_dict[token_pair_0to1][pool_address].append({
                                    'tick': active_tick_lower,
                                    'capacity': tick_value_eth[1]  # token1 capacity for 0->1
                                })
                                
                                pool_weights_dict[token_pair_0to1][pool_address].append({
                                    'tick': active_tick_lower,
                                    'weight': weight0to1
                                })
                            
                            if tick_value_eth[0] >= capacity_threshold:
                                pool_capacities_dict[token_pair_1to0][pool_address].append({
                                    'tick': active_tick_lower,
                                    'capacity': tick_value_eth[0]  # token0 capacity for 1->0
                                })
                                
                                pool_weights_dict[token_pair_1to0][pool_address].append({
                                    'tick': active_tick_lower, 
                                    'weight': weight1to0
                                })

                            current_tick_result = {
                                'pool_id': pool_address,
                                'token0': token0,
                                'token1': token1,
                                'rate_token0_to_token1': rate0to1,
                                'rate_token1_to_token0': rate1to0,
                                'weight_token0_to_token1': weight0to1,
                                'weight_token1_to_token0': weight1to0,
                                'swap_fee': swap_fee,
                                'tick': active_tick_lower,
                                'liquidity': current_liquidity,
                                'capacity': tick_value_eth
                            }

                            ### debug
                            # if pool_address == '******************************************':
                            #     print("debugging!!!")
                            #     print(current_tick_result)
                            #     print(active_tick_lower, active_tick_upper)
                            #     print(rate0to1, rate1to0)
                            
                            
                            results.append(current_tick_result)
                    
                    # ===== PROCESS LEFT TICKS (price < current) =====
                    # left_ticks = []
                    # for i in reversed(range(len(initialized_ticks))):
                    #     if initialized_ticks[i][0] < active_tick_lower and len(left_ticks) < max_nearby_ticks:
                    #         left_ticks.append(initialized_ticks[i][0])
                    #     if len(left_ticks) >= max_nearby_ticks:
                    #         break

                    # for tick in left_ticks:
                    #     # Find the next initialized tick to the right
                    #     next_tick = None
                    #     for t, _ in initialized_ticks:
                    #         if t > tick:
                    #             next_tick = t
                    #             break
                        
                    #     if next_tick is None:
                    #         continue
                        
                    #     # Calculate liquidity for this tick
                    #     tick_liquidity = 0
                    #     for tick_idx, tick_data in initialized_ticks:
                    #         if tick_idx <= tick:
                    #             tick_liquidity += int(tick_data.get('liquidityNet', 0))
                    #         elif tick_idx > next_tick:
                    #             break
                                            
                        # price_lower = tick_to_price(tick)
                        # price_upper = tick_to_price(next_tick)
        
                    left_intervals = []
                    for i in reversed(range(len(initialized_ticks) - 1)):
                        if initialized_ticks[i+1][0] < current_tick:
                            left_intervals.append((initialized_ticks[i][0], initialized_ticks[i+1][0]))
                        if len(left_intervals) >= max_nearby_ticks:
                            break
                    
                                        
                    for lower, upper in left_intervals:
                        # Calculate liquidity for this interval by accumulating liquidityNet values
                        interval_liquidity = 0
                        for tick_idx, tick_data in initialized_ticks:
                            if tick_idx <= lower:
                                interval_liquidity += int(tick_data.get('liquidityNet', 0))
                            elif tick_idx > upper:
                                break

                        price_lower = tick_to_price(lower)
                        price_upper = tick_to_price(upper)
                    
                       
                        # No subdivision needed, process the tick normally
                        # For left ticks (below current price), token0 is minimal
                        # Calculate token1 amount in this tick range
                        token1_balance = interval_liquidity * (math.sqrt(price_upper) - math.sqrt(price_lower)) / (10**token1_decimals)
                        
                        if token1_address in token_prices:
                            # Capacity is the value of token1 for left ticks
                            tick_value_eth = token1_balance * token_prices.get(token1_address, 0)
                            
                            if tick_value_eth < liquidity_threshold:
                                continue
                            
                            tick_price = tick_to_adjusted_price(lower, token0_decimals, token1_decimals)
                            
                            # Apply swap fee multiplier
                            lambda_tax = float(swap_fee)
                            rate0to1 = (1 - lambda_tax) * tick_price
                            
                            # Calculate edge weights
                            log_base = math.e
                            weight0to1 = -math.log(rate0to1, log_base)
                            
                            # Store only token0->token1 direction (left ticks)
                            if tick_value_eth >= capacity_threshold:
                                pool_capacities_dict[token_pair_0to1][pool_address].append({
                                    'tick': lower,
                                    'capacity': tick_value_eth
                                })
                                
                                pool_weights_dict[token_pair_0to1][pool_address].append({
                                    'tick': lower,
                                    'weight': weight0to1
                                })
                            
                            left_tick_result = {
                                'pool_id': pool_address,
                                'token0': token0,
                                'token1': token1,
                                'rate_token0_to_token1': rate0to1,
                                'rate_token1_to_token0': None,  # Only token0->token1 for left ticks
                                'weight_token0_to_token1': weight0to1,
                                'weight_token1_to_token0': None,  # Only token0->token1 for left ticks
                                'swap_fee': swap_fee,
                                'tick': lower,
                                'liquidity': interval_liquidity,
                                'capacity': tick_value_eth,
                                'direction': 'token0_to_token1'
                            }
                            results.append(left_tick_result)
                    
                    # ===== PROCESS RIGHT TICKS (price > current) =====
                    # right_tick_indices = [i for i, (tick_idx, _) in enumerate(initialized_ticks) if tick_idx >= active_tick_upper]
                    # for idx in right_tick_indices[:max_nearby_ticks]:
                    #     tick, tick_data = initialized_ticks[idx]
                    #     # 下一个 tick
                    #     if idx + 1 < len(initialized_ticks):
                    #         next_tick, _ = initialized_ticks[idx + 1]
                    #     else:
                    #         continue  # 没有下一个 tick，跳过

                    #     # Calculate liquidity for this tick
                    #     tick_liquidity = 0
                    #     for tick_idx, tick_data in initialized_ticks:
                    #         if tick_idx <= tick:
                    #             tick_liquidity += int(tick_data.get('liquidityNet', 0))
                    #         elif tick_idx > next_tick:
                    #             break

                    right_intervals = []
                    for i in range(len(initialized_ticks) - 1):
                        if initialized_ticks[i][0] > current_tick:
                            right_intervals.append((initialized_ticks[i][0], initialized_ticks[i+1][0]))
                        if len(right_intervals) >= max_nearby_ticks:
                            break
                    
                    for lower, upper in right_intervals:
                        # Calculate liquidity for this interval by accumulating liquidityNet values
                        interval_liquidity = 0
                        for tick_idx, tick_data in initialized_ticks:
                            if tick_idx <= lower:
                                interval_liquidity += int(tick_data.get('liquidityNet', 0))
                            elif tick_idx > upper:
                                break

                        price_lower = tick_to_price(lower)
                        price_upper = tick_to_price(upper)

                        # 计算 token0_balance
                        token0_balance = interval_liquidity * (1/math.sqrt(price_lower) - 1/math.sqrt(price_upper)) / (10**token0_decimals)

                        if token0_address in token_prices:
                            tick_value_eth = token0_balance * token_prices.get(token0_address, 0)
                            if tick_value_eth < liquidity_threshold:
                                continue
                            
                            ### debugging
                            tick_price = tick_to_adjusted_price(upper, token0_decimals, token1_decimals)
                            lambda_tax = float(swap_fee)
                            rate1to0 = (1 - lambda_tax) * (1 / tick_price)
                            log_base = math.e
                            weight1to0 = -math.log(rate1to0, log_base)

                            if tick_value_eth >= capacity_threshold:
                                pool_capacities_dict[token_pair_1to0][pool_address].append({
                                    'tick': lower,
                                    'capacity': tick_value_eth
                                })
                                pool_weights_dict[token_pair_1to0][pool_address].append({
                                    'tick': lower,
                                    'weight': weight1to0
                                })

                            right_tick_result = {
                                'pool_id': pool_address,
                                'token0': token0,
                                'token1': token1,
                                'rate_token0_to_token1': None,
                                'rate_token1_to_token0': rate1to0,
                                'weight_token0_to_token1': None,
                                'weight_token1_to_token0': weight1to0,
                                'swap_fee': swap_fee,
                                'tick': lower,
                                'liquidity': interval_liquidity,
                                'capacity': tick_value_eth,
                                'direction': 'token1_to_token0'
                            }
                            results.append(right_tick_result)
        except Exception as e:
            skipped_pools += 1
            continue
    
    # Convert defaultdicts to regular dicts for easier serialization
    pool_capacities_dict_regular = {k: dict(v) for k, v in pool_capacities_dict.items()}
    pool_weights_dict_regular = {k: dict(v) for k, v in pool_weights_dict.items()}
    
    return results, skipped_pools, pool_capacities_dict_regular, pool_weights_dict_regular

def calculate_exchange_rates_v2(
    pools_data,
    token_prices_eth,
    percentage_steps=[0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5, 10],
    num_current_percentage_layers=2,
    liquidity_threshold=CAPACITY_THRESHOLD,
    capacity_threshold=CAPACITY_THRESHOLD
):
    """
    Calculates virtual tick information for V2 pools based on price percentage intervals.
    This function is designed to output data in a format similar to V3 pools.

    Args:
        pools_data: A list of V2 pool data dictionaries
        token_prices_eth: A dictionary mapping token addresses to their ETH prices
        percentage_steps: A list of floats representing percentage deviations from the current price
        num_current_percentage_layers: How many layers around current price are considered current
        liquidity_threshold: Minimum liquidity value in ETH to consider a pool
        capacity_threshold: Minimum capacity value to add to the JSON output
        
    Returns:
        A tuple containing:
        - results (list): A list of exchange rate dictionaries
        - processed_pools_count (int): The number of pools successfully processed
        - pool_capacities_dict (dict): Dictionary mapping token pairs to {pool_address: [capacities]}
        - pool_weights_dict (dict): Dictionary mapping token pairs to {pool_address: [weights]}
    """
    results = []
    processed_pools_count = 0
    pool_capacities_dict = defaultdict(lambda: defaultdict(list))  # {token_pair: {pool_address: [capacities]}}
    pool_weights_dict = defaultdict(lambda: defaultdict(list))     # {token_pair: {pool_address: [weights]}}

    # Convert percentage steps to Decimal for precision
    decimal_percentage_steps = sorted([Decimal(str(p)) for p in percentage_steps])

    for pool_entry in pools_data:
        try:
            if pool_entry.get("poolType") != "uniswap_v2_like_pool":
                continue

            pool_static_info = pool_entry["poolState"]["poolStaticInfo"]
            pool_state = pool_entry["poolState"]["poolState"]
            pool_address = pool_static_info["poolAddress"].lower()

            token0_address = pool_static_info["token0"].lower()
            token1_address = pool_static_info["token1"].lower()
            token0_decimals = int(pool_static_info["token0Decimals"])
            token1_decimals = int(pool_static_info["token1Decimals"])
            
            # Create token pair keys
            token_pair_0to1 = f"{token0_address}_{token1_address}"
            token_pair_1to0 = f"{token1_address}_{token0_address}"

            # Use Decimal for all reserve and price calculations
            r0_initial_raw = Decimal(pool_state["tokenBalance0"])
            r1_initial_raw = Decimal(pool_state["tokenBalance1"])

            # Adjust for decimals
            r0_initial = r0_initial_raw / (DECIMAL_ONE * 10 ** token0_decimals)
            r1_initial = r1_initial_raw / (DECIMAL_ONE * 10 ** token1_decimals)

            if r0_initial <= DECIMAL_EPSILON or r1_initial <= DECIMAL_EPSILON:
                continue

            k = r0_initial * r1_initial  # Constant product k = x*y
            # Fee rate from ppm to decimal (e.g., 3000 ppm -> 0.003)
            fee_rate = Decimal(pool_static_info["swapFee"]) / Decimal(1000000)

            # Current price P = r1/r0 (amount of token1 per 1 unit of token0)
            p_current = r1_initial / r0_initial
            if p_current <= DECIMAL_EPSILON:
                continue
            
            # --- Define price boundaries based on percentage steps ---
            # Left side boundaries (prices lower than p_current)
            left_boundaries = [p_current]
            for pct_step in decimal_percentage_steps:
                new_price = p_current * (DECIMAL_ONE - pct_step)
                # Ensure price is above epsilon to avoid divide-by-zero errors
                if new_price > DECIMAL_EPSILON:
                    left_boundaries.append(new_price)
            # Remove duplicates, sort descending
            left_boundaries = sorted(set(left_boundaries), reverse=True)

            # Right side boundaries (prices higher than p_current)
            right_boundaries = [p_current]
            for pct_step in decimal_percentage_steps:
                new_price = p_current * (DECIMAL_ONE + pct_step)
                # No need to check for non-zero here as we're adding to price
                right_boundaries.append(new_price)
            right_boundaries = sorted(set(right_boundaries))

            # Store current segment information for matching t0->t1 with t1->t0
            current_segments = []

            # --- Generate virtual tick segments for token0 -> token1 (price decreases) ---
            for i in range(len(left_boundaries) - 1):
                p_start_segment = left_boundaries[i]   # Upper price boundary of the segment
                p_end_segment = left_boundaries[i+1]   # Lower price boundary of the segment

                if p_end_segment >= p_start_segment or p_end_segment < DECIMAL_EPSILON or p_start_segment < DECIMAL_EPSILON: 
                    continue  # Price must decrease and both prices must be positive

                # Theoretical reserves at p_start_segment
                try:
                    r0_at_p_start = (k / p_start_segment).sqrt()
                    r1_at_p_start = (k * p_start_segment).sqrt()
                    
                    # Theoretical reserves at p_end_segment
                    r0_at_p_end = (k / p_end_segment).sqrt()
                except (ValueError, ZeroDivisionError, ArithmeticError):
                    # Skip if any calculation errors
                    continue

                # Net token0 input required to move price from p_start_segment to p_end_segment
                delta_r0_net_input = r0_at_p_end - r0_at_p_start
                if delta_r0_net_input <= DECIMAL_EPSILON: 
                    continue

                # Gross token0 input (capacity for this segment)
                fee_factor = DECIMAL_ONE - fee_rate
                if fee_factor <= DECIMAL_EPSILON:
                    continue  # Skip if fee is too large
                    
                # 示例：限制最大可消耗80%的池子流动性
                MAX_RESERVE_USAGE_PCT = Decimal(1)
                max_usable_r0 = r0_initial * MAX_RESERVE_USAGE_PCT
                max_usable_r1 = r1_initial * MAX_RESERVE_USAGE_PCT
                
                # 然后在计算capacity时加入限制
                capacity_token0_gross = min(delta_r0_net_input / fee_factor, max_usable_r0)
                
                # Net token1 output from this trade
                try:
                    delta_r1_net_output = r1_at_p_start - (k / r0_at_p_end)
                except (ValueError, ZeroDivisionError, ArithmeticError):
                    continue

                if capacity_token0_gross <= DECIMAL_EPSILON or delta_r1_net_output <= DECIMAL_EPSILON:
                    continue
                
                # Calculate effective exchange rate
                avg_effective_rate_t0_to_t1 = delta_r1_net_output / capacity_token0_gross
                
                price_token0_in_eth = Decimal(str(token_prices_eth.get(token0_address, '0')))
                liquidity_value_eth_segment = capacity_token0_gross * price_token0_in_eth
                
                if float(liquidity_value_eth_segment) < liquidity_threshold:
                    continue
                
                # Determine pair_type ("current" or "left")
                is_current_segment = (i < num_current_percentage_layers)
                pair_type = "current" if is_current_segment else "left"
                
                # Calculate edge weight as negative log of exchange rate
                log_base = math.e  # Using natural logarithm for edge weights
                weight0to1 = -math.log(float(avg_effective_rate_t0_to_t1), log_base)
                
                # Create a virtual tick index for V2 pool segments
                virtual_tick = f"v2_{i}"
                
                # For "current" type, liquidity_value_eth will be a list [cap_t0_t1, cap_t1_t0]
                # Initially set token1->token0 capacity to 0, will be updated later
                current_segment_liquidity_eth = [float(liquidity_value_eth_segment), 0] if pair_type == "current" else float(liquidity_value_eth_segment)
                
                # Store weights and capacities for token0->token1 direction
                if float(liquidity_value_eth_segment) >= capacity_threshold:
                    pool_capacities_dict[token_pair_0to1][pool_address].append({
                        'tick': virtual_tick,
                        'capacity': float(liquidity_value_eth_segment),
                        'pool_type': 'v2'
                    })
                    
                    pool_weights_dict[token_pair_0to1][pool_address].append({
                        'tick': virtual_tick,
                        'weight': weight0to1,
                        'pool_type': 'v2'
                    })
                
                result_entry = {
                    'pool_id': pool_address,
                    'token0': token0_address,
                    'token1': token1_address,
                    'rate_token0_to_token1': float(avg_effective_rate_t0_to_t1),
                    'rate_token1_to_token0': None,  # Only token0->token1 for left segments
                    'weight_token0_to_token1': weight0to1,
                    'weight_token1_to_token0': None,
                    'swap_fee': float(fee_rate),
                    'tick': virtual_tick,
                    'capacity': current_segment_liquidity_eth,
                    'direction': 'token0_to_token1',
                    'pool_type': 'v2',
                    'price_range': [float(p_start_segment), float(p_end_segment)],
                    'pair_type': pair_type
                }
                results.append(result_entry)
                
                # Keep track of current segments for easier matching later
                if pair_type == "current":
                    current_segments.append({
                        'index': len(results) - 1,
                        'tick': virtual_tick,
                        'direction': 'token0_to_token1'
                    })

            # --- Generate virtual tick segments for token1 -> token0 (price increases) ---
            for i in range(len(right_boundaries) - 1):
                p_start_segment = right_boundaries[i]  # Lower price boundary of the segment
                p_end_segment = right_boundaries[i+1]  # Upper price boundary of the segment

                # Safety checks for price values
                if (p_end_segment <= p_start_segment or 
                    p_end_segment < DECIMAL_EPSILON or 
                    p_start_segment < DECIMAL_EPSILON): 
                    continue  # Price must increase and both prices must be positive

                try:
                    r1_at_p_start = (k * p_start_segment).sqrt()
                    r1_at_p_end = (k * p_end_segment).sqrt()
                except (ValueError, ZeroDivisionError, ArithmeticError):
                    continue

                delta_r1_net_input = r1_at_p_end - r1_at_p_start  # Net token1 input
                if delta_r1_net_input < DECIMAL_EPSILON: 
                    continue
                
                fee_factor = DECIMAL_ONE - fee_rate
                if fee_factor < DECIMAL_EPSILON:
                    continue  # Skip if fee is too large
                    
                # 示例：限制最大可消耗80%的池子流动性
                MAX_RESERVE_USAGE_PCT = Decimal(1)
                max_usable_r0 = r0_initial * MAX_RESERVE_USAGE_PCT
                max_usable_r1 = r1_initial * MAX_RESERVE_USAGE_PCT
                
                # 然后在计算capacity时加入限制
                capacity_token1_gross = min(delta_r1_net_input / fee_factor, max_usable_r1)
                
                try:
                    delta_r0_net_output = (k / p_start_segment).sqrt() - (k / p_end_segment).sqrt()
                except (ValueError, ZeroDivisionError, ArithmeticError):
                    continue

                if capacity_token1_gross < DECIMAL_EPSILON or delta_r0_net_output < DECIMAL_EPSILON:
                    continue

                # Calculate effective exchange rate
                avg_effective_rate_t1_to_t0 = delta_r0_net_output / capacity_token1_gross
                
                price_token1_in_eth = Decimal(str(token_prices_eth.get(token1_address, '0')))
                liquidity_value_eth_segment = capacity_token1_gross * price_token1_in_eth
                
                if float(liquidity_value_eth_segment) < liquidity_threshold:
                    continue
                
                is_current_segment = (i < num_current_percentage_layers)
                pair_type = "current" if is_current_segment else "right"
                
                # Calculate edge weight
                log_base = math.e
                weight1to0 = -math.log(float(avg_effective_rate_t1_to_t0), log_base)
                
                # Create a virtual tick index for V2 pool segments
                virtual_tick = f"v2_{i + len(left_boundaries)}"
                
                # Try to match with an existing current segment
                matched_current = False
                if pair_type == "current" and current_segments:
                    # Find the corresponding current segment for the t0->t1 direction
                    for segment in current_segments:
                        if segment['direction'] == 'token0_to_token1':
                            # Update the corresponding current segment's token1->token0 capacity
                            idx = segment['index']
                            results[idx]["capacity"][1] = float(liquidity_value_eth_segment)
                            results[idx]["rate_token1_to_token0"] = float(avg_effective_rate_t1_to_t0)
                            results[idx]["weight_token1_to_token0"] = weight1to0
                            
                            # Store weights and capacities for token1->token0 direction using the same tick
                            matched_tick = segment['tick']
                            
                            if float(liquidity_value_eth_segment) >= capacity_threshold:
                                pool_capacities_dict[token_pair_1to0][pool_address].append({
                                    'tick': matched_tick,
                                    'capacity': float(liquidity_value_eth_segment),
                                    'pool_type': 'v2'
                                })
                                
                                pool_weights_dict[token_pair_1to0][pool_address].append({
                                    'tick': matched_tick,
                                    'weight': weight1to0,
                                    'pool_type': 'v2'
                                })
                            
                            matched_current = True
                            break
                
                # If we couldn't match with a current segment, create a new entry
                if not matched_current:
                    # For non-current right segments or if no corresponding t0->t1 entry found
                    current_segment_liquidity_eth = [0, float(liquidity_value_eth_segment)] if pair_type == "current" else float(liquidity_value_eth_segment)
                    
                    # Store weights and capacities for token1->token0 direction
                    if float(liquidity_value_eth_segment) >= capacity_threshold:
                        pool_capacities_dict[token_pair_1to0][pool_address].append({
                            'tick': virtual_tick,
                            'capacity': float(liquidity_value_eth_segment),
                            'pool_type': 'v2'
                        })
                        
                        pool_weights_dict[token_pair_1to0][pool_address].append({
                            'tick': virtual_tick,
                            'weight': weight1to0,
                            'pool_type': 'v2'
                        })
                    
                    result_entry = {
                        'pool_id': pool_address,
                        'token0': token0_address,
                        'token1': token1_address,
                        'rate_token0_to_token1': None, # Only token1->token0 for right segments
                        'rate_token1_to_token0': float(avg_effective_rate_t1_to_t0),
                        'weight_token0_to_token1': None,
                        'weight_token1_to_token0': weight1to0,
                        'swap_fee': float(fee_rate),
                        'tick': virtual_tick,
                        'capacity': current_segment_liquidity_eth,
                        'direction': 'token1_to_token0',
                        'pool_type': 'v2',
                        'price_range': [float(p_start_segment), float(p_end_segment)],
                        'pair_type': pair_type
                    }
                    results.append(result_entry)
                    
                    # Add to current segments if needed
                    if pair_type == "current":
                        current_segments.append({
                            'index': len(results) - 1,
                            'tick': virtual_tick,
                            'direction': 'token1_to_token0'
                        })
                
            processed_pools_count += 1
        except Exception as e:
            # Skip any pools that cause errors
            print(f"Error processing pool {pool_static_info['poolAddress'] if 'poolStatic_info' in locals() else 'unknown'}: {str(e)}")
            continue
            
    # Convert defaultdicts to regular dicts for easier serialization
    pool_capacities_dict_regular = {k: dict(v) for k, v in pool_capacities_dict.items()}
    pool_weights_dict_regular = {k: dict(v) for k, v in pool_weights_dict.items()}
    
    return results, processed_pools_count, pool_capacities_dict_regular, pool_weights_dict_regular

def create_token_graph(exchange_rates):
    """
    Create a token exchange graph with weighted edges
    
    This function builds a directed graph where:
    - Nodes represent tokens
    - Edges represent possible exchanges between tokens at current prices
    
    Args:
        exchange_rates: List of exchange rate dictionaries from calculate_exchange_rates_v3 and calculate_exchange_rates_v2
    Returns:
        G: NetworkX DiGraph representing the token exchange network
    """
    G = nx.MultiDiGraph()  # Use MultiDiGraph to allow multiple edges between nodes
    
    # Dictionary to track best rates for each token pair
    # Format: {(token0, token1, direction): (best_weight, best_rate, pool_id, tick, capacity, pool_type)}
    best_rates = {}
    
    # First pass: Find the best rate for each token pair and direction
    for rate in exchange_rates:
        token0 = rate['token0']
        token1 = rate['token1']
        pool_id = rate['pool_id']
        tick = rate['tick']
        pool_type = rate.get('pool_type', 'v3')  # Default to v3 for backward compatibility
        
        try:
            # Check token0 -> token1 direction
            if rate.get('rate_token0_to_token1') is not None:
                direction = '0to1'
                weight = rate.get('weight_token0_to_token1')
                rate_value = rate.get('rate_token0_to_token1')
                capacity = rate['capacity'] if isinstance(rate['capacity'], (int, float)) else rate['capacity'][1]
                
                pair_key = (token0, token1, direction)
                if pair_key not in best_rates or weight < best_rates[pair_key][0]:
                    best_rates[pair_key] = (weight, rate_value, pool_id, tick, capacity, pool_type)
            
            # Check token1 -> token0 direction
            if rate.get('rate_token1_to_token0') is not None:
                direction = '1to0'
                weight = rate.get('weight_token1_to_token0')
                rate_value = rate.get('rate_token1_to_token0')
                capacity = rate['capacity'] if isinstance(rate['capacity'], (int, float)) else rate['capacity'][0]
                
                pair_key = (token1, token0, direction)
                if pair_key not in best_rates or weight < best_rates[pair_key][0]:
                    best_rates[pair_key] = (weight, rate_value, pool_id, tick, capacity, pool_type)
        except (ValueError, TypeError, ZeroDivisionError):
            print(f"Error processing pool {pool_id}: {str(e)}")
            continue
    
    # Second pass: Add nodes and best edges to the graph
    for (src, dst, direction), (weight, rate_value, pool_id, tick, capacity, pool_type) in best_rates.items():
        # Add nodes if they don't exist
        if src not in G:
            G.add_node(src)
        if dst not in G:
            G.add_node(dst)
        
        # Add the best edge for this token pair and direction
        G.add_edge(src, dst,
                  key=f"{pool_id}_{tick}_{direction}",
                  weight=weight,
                  rate=rate_value,
                  pool_id=pool_id,
                  tick=tick,
                  pool_type=pool_type,
                  capacity=capacity)
    
    return G

def print_graph(token_graph, path, token_names):
    """
    Save the graph to files with format: from to
    Ensures only one edge per token pair is output
    """
    # Ensure output directory exists
    os.makedirs(os.path.dirname(path) if os.path.dirname(path) else '.', exist_ok=True)
    
    # Sort nodes to ensure consistent node IDs
    sorted_nodes = sorted(token_graph.nodes())
    node_map = {node: idx for idx, node in enumerate(sorted_nodes)}
    
    graph_file = '{}_token_graph.txt'.format(path)
    map_file = '{}_node_map.txt'.format(path)
    
    # Create a dictionary to track best edges between nodes
    # Format: {(source, target): (key, weight, data)}
    best_edges = {}
    
    # Find the best (lowest weight) edge for each pair of nodes
    for u, v, key, data in token_graph.edges(data=True, keys=True):
        edge_pair = (u, v)
        weight = data['weight']
        
        if edge_pair not in best_edges or weight < best_edges[edge_pair][1]:
            best_edges[edge_pair] = (key, weight, data)
    
    # Write the graph with only the best edges, but without weights
    with open(graph_file, 'w') as f:
        for (u, v), (key, weight, _) in best_edges.items():
            source_idx = node_map[u]
            target_idx = node_map[v]
            f.write(f"{source_idx} {target_idx}\n")
    
    # Write the node mapping
    with open(map_file, 'w') as f:
        for node, idx in node_map.items():
            name = token_names[node] if node in token_names else ''
            f.write(f"{idx} {node} {name}\n")

    return graph_file, map_file

def load_ranked_pools(ranked_pools_file):
    """
    Load ranked pools from a text file and return all pools
    
    Args:
        ranked_pools_file: Path to the text file with ranked pools
    
    Returns:
        Dictionary of pool addresses to liquidity
    """
    pools = {}
    try:
        with open(ranked_pools_file, 'r') as file:
            for line in file:
                parts = line.strip().split()
                if len(parts) >= 2:
                    pool_address = parts[0].lower()
                    liquidity = float(parts[1])
                    pools[pool_address] = liquidity
    except Exception as e:
        print("Error loading ranked pools: {}".format(e))
        return {}
    
    return pools

def build_initial_graph(v3_data, v2_data=None):
    """
    Build initial graph where nodes are tokens and edges are pools
    
    Args:
        v3_data: List of V3 pool data
        v2_data: Optional list of V2 pool data
    
    Returns:
        G: NetworkX graph with tokens as nodes and pools as edges
    """
    G = nx.MultiDiGraph()
    
    # Process V3 pools
    for pool in v3_data:
        try:
            pool_data = json.loads(pool) if isinstance(pool, str) else pool
            
            if pool_data['poolType'] == 'uniswap_v3_like_pool':
                pool_static_info = pool_data['poolState']['poolStaticInfo']
                if pool_static_info['dexExchange'] == 'uniswap':
                    pool_state = pool_data['poolState']['poolState']
                    pool_address = pool_state['poolAddress'].lower()
                    
                    # Extract token addresses
                    token0 = pool_static_info['token0'].lower()
                    token1 = pool_static_info['token1'].lower()
                    
                    # Get liquidity
                    liquidity = int(pool_state.get('liquidity', 0))
                    
                    # Add nodes if they don't exist
                    if not G.has_node(token0):
                        G.add_node(token0)
                    if not G.has_node(token1):
                        G.add_node(token1)
                    
                    # Add edge between tokens with pool information
                    G.add_edge(token0, token1,
                             key=f"{pool_address}_0to1",
                             pool_address=pool_address,
                             liquidity=liquidity,
                             pool_type='v3',
                             direction='0to1')

                    G.add_edge(token1, token0,
                             key=f"{pool_address}_1to0",
                             pool_address=pool_address,
                             liquidity=liquidity,
                             pool_type='v3',
                             direction='1to0')
        except Exception:
            continue
    
    # Process V2 pools if provided
    if v2_data:
        for pool in v2_data:
            try:
                pool_data = json.loads(pool) if isinstance(pool, str) else pool
                
                if pool_data['poolType'] == 'uniswap_v2_like_pool':
                    pool_static_info = pool_data['poolState']['poolStaticInfo']
                    pool_state = pool_data['poolState']['poolState']
                    pool_address = pool_static_info['poolAddress'].lower()
                    
                    # Extract token addresses
                    token0 = pool_static_info['token0'].lower()
                    token1 = pool_static_info['token1'].lower()
                    
                    # Calculate liquidity from reserves
                    try:
                        r0 = float(pool_state.get('tokenBalance0', 0))
                        r1 = float(pool_state.get('tokenBalance1', 0))
                        liquidity = r0 * r1  # Simple product as an indicator of liquidity
                    except (ValueError, TypeError):
                        liquidity = 0
                    
                    # Only add nodes/edges if they don't already exist in the graph
                    token_pair_exists = False
                    
                    # Check if this token pair already exists in the graph
                    if G.has_edge(token0, token1) or G.has_edge(token1, token0):
                        token_pair_exists = True
                    
                    # Add to graph if token pair doesn't exist
                    if not token_pair_exists:
                        # Add nodes if they don't exist
                        if not G.has_node(token0):
                            G.add_node(token0)
                        if not G.has_node(token1):
                            G.add_node(token1)
                        
                        # Add edges with pool information
                        G.add_edge(token0, token1,
                                 key=f"{pool_address}_0to1",
                                 pool_address=pool_address,
                                 liquidity=liquidity,
                                 pool_type='v2',
                                 direction='0to1')
                
                        G.add_edge(token1, token0,
                                 key=f"{pool_address}_1to0",
                                 pool_address=pool_address,
                                 liquidity=liquidity,
                                 pool_type='v2',
                                 direction='1to0')
            except Exception:
                continue
    
    return G

def main():
    """Main function to process pool data and create token graph"""
    parser = argparse.ArgumentParser(description='Build token graphs')
    parser.add_argument('--split-ticks', action='store_true', 
                       help='Enable V3 tick splitting for large price ranges')
    args = parser.parse_args()
    try:
        block_number = 21974203
        # File paths
        if args.split_ticks:
            v3_file_path = '../data/prices/block-{}/processed-v3pools.json'.format(block_number)
        else:
            v3_file_path = '../data/prices/block-{}/filtered-v3pools.json'.format(block_number)
        v2_file_path = '../data/prices/block-{}/filtered-v2pools.json'.format(block_number)
        price_file_path = f'../data/prices/block-{block_number}/tokens-quotes.json'
        node_name_file = '../data/node_name.txt'
        
        # Load token names
        token_names = load_token_names(node_name_file)
        print(f"Loaded {len(token_names)} token names")
        
        # Load token prices
        token_prices = load_token_prices(price_file_path)
        print(f"Loaded {len(token_prices)} token prices")
        
        # Load V3 pool data
        v3_data = load_data_from_json(v3_file_path)
        print(f"Loaded {len(v3_data)} V3 pools")
        
        # Load V2 pool data
        v2_data = []
        if os.path.exists(v2_file_path):
            v2_data = load_data_from_json(v2_file_path)
            print(f"Loaded {len(v2_data)} V2 pools")
        
        # Build initial graph with both V3 and V2 pools
        initial_graph = build_initial_graph(v3_data, v2_data)
        print(f"\nInitial graph has {initial_graph.number_of_nodes()} nodes and {initial_graph.number_of_edges()} edges")
        
        # Process V3 pools
        v3_pools, v3_skipped, v3_capacities_dict, v3_weights_dict = calculate_exchange_rates_v3(
            v3_data, 
            token_prices, 
            liquidity_threshold=CAPACITY_THRESHOLD, 
            max_nearby_ticks=100, 
            capacity_threshold=CAPACITY_THRESHOLD
        )
        print(f"Processed {len(v3_pools)} V3 pools, skipped {v3_skipped}")
        
        # Process V2 pools if available
        v2_pools, v2_processed = [], 0
        v2_capacities_dict, v2_weights_dict = {}, {}
        if v2_data:
            # Use balanced percentage steps with more granular values across the range
            # custom_percentage_steps = [0.0001, 0.001, 0.01, 0.05, 0.1, 0.2, 0.5, 1,
            # custom_percentage_steps = [0.01, 0.05, 0.1, 0.2, 0.5, 0.9, 0.99, 0.999, 
            #                            2, 5, 10, 25, 50, 90, 95, 98, 99, 100]
            # custom_percentage_steps = [
            #         0.00001, 0.0001, 0.001, 0.01, 0.05, 0.1, 0.2, 0.5,
            #         0.9, 0.99, 0.999, 0.9999, 1, 10, 100, 
            #     ]
            # custom_percentage_steps = [
            #         # 微小变动(适合高流动性池和微交易)
            #         1e-30, 1e-18, 1e-10, 1e-8, 5e-8, 1e-7, 5e-7, 1e-6, 5e-6, 1e-5, 5e-5,
            #         # 小幅变动(常见交易范围)
            #         0.0001, 0.0005, 0.001, 0.005, 0.01, 0.05,
            #         # 中等变动(较大交易)
            #         0.1, 0.2, 0.5,
            #         # 大幅变动(大额交易或低流动性池)
            #         1, 2, 5, 10, 20, 50, 100, 1000, 10e5, 10e10
            #     ]
            custom_percentage_steps = [
                    # 微小变动(适合高流动性池和微交易)
                    1e-8, 5e-8, 1e-7, 5e-7, 1e-6, 5e-6, 1e-5, 5e-5,
                    # 小幅变动(常见交易范围)
                    0.0001, 0.0005, 0.001, 0.005, 0.01, 0.05,
                    # 中等变动(较大交易)
                    0.1, 0.2, 0.5,
                    # 大幅变动(大额交易或低流动性池)
                    1, 2, 5, 10, 20, 50, 100, 1000
                ]
            v2_pools, v2_processed, v2_capacities_dict, v2_weights_dict = calculate_exchange_rates_v2(
                v2_data, 
                token_prices, 
                percentage_steps=custom_percentage_steps,
                num_current_percentage_layers=3,  # Include more layers as "current" price
                liquidity_threshold=CAPACITY_THRESHOLD,
                capacity_threshold=CAPACITY_THRESHOLD
            )
            print(f"Processed {v2_processed} V2 pools, created {len(v2_pools)} segments")
        
        # Merge pool capacities and weights dictionaries
        pool_capacities_dict = v3_capacities_dict.copy()
        pool_weights_dict = v3_weights_dict.copy()
        
        # Add pool_type to V3 entries
        for token_pair in pool_capacities_dict:
            for pool_address in pool_capacities_dict[token_pair]:
                # if pool_address == '0x56534741cd8b152df6d48adf7ac51f75169a83b2':
                #     print('debugging!!!!', pool_capacities_dict[token_pair][pool_address])
                for capacity_entry in pool_capacities_dict[token_pair][pool_address]:
                    if 'pool_type' not in capacity_entry:
                        capacity_entry['pool_type'] = 'v3'
                
        for token_pair in pool_weights_dict:
            for pool_address in pool_weights_dict[token_pair]:
                for weight_entry in pool_weights_dict[token_pair][pool_address]:
                    if 'pool_type' not in weight_entry:
                        weight_entry['pool_type'] = 'v3'
        
        # Merge V2 capacities and weights into the dictionaries
        for token_pair, pools in v2_capacities_dict.items():
            if token_pair not in pool_capacities_dict:
                pool_capacities_dict[token_pair] = {}
            
            for pool_address, capacities in pools.items():
                pool_capacities_dict[token_pair][pool_address] = capacities
        
        for token_pair, pools in v2_weights_dict.items():
            if token_pair not in pool_weights_dict:
                pool_weights_dict[token_pair] = {}
            
            for pool_address, weights in pools.items():
                pool_weights_dict[token_pair][pool_address] = weights
        
        # Save combined pool capacities and weights dictionaries
        os.makedirs('graphs', exist_ok=True)
        if args.split_ticks:
            capacities_file = f'graphs/{block_number}_processed_pool_capacities.json'
            weights_file = f'graphs/{block_number}_processed_pool_weights.json'
        else:
            capacities_file = f'graphs/{block_number}_pool_capacities.json'
            weights_file = f'graphs/{block_number}_pool_weights.json'
        
        with open(capacities_file, 'w') as f:
            json.dump(pool_capacities_dict, f)
        
        with open(weights_file, 'w') as f:
            json.dump(pool_weights_dict, f)
        
        print(f"Saved pool capacities to {capacities_file}")
        print(f"Saved pool weights to {weights_file}")
        
        # Combine V3 and V2 pools for the final graph
        all_pools = v3_pools + v2_pools
        
        # Create final graph
        final_graph = create_token_graph(all_pools)
        
        # Save graph
        graph_file, map_file = print_graph(
            final_graph, 
            f'graphs/{block_number}_combined',
            token_names
        )
        
        # Final summary
        print("\nSummary:")
        print(f"Created graph with {len(final_graph.nodes())} nodes and {len(final_graph.edges())} edges")
        print(f"Included {len(v3_pools)} V3 ticks and {len(v2_pools)} V2 price segments")
        print(f"Saved output to {graph_file}, {map_file}")
        
    except Exception as e:
        print("Error: {}".format(e))
        traceback.print_exc()

def load_token_names(file_path):
    token_names = {}
    try:
        with open(file_path, 'r') as file:
            for line in file:
                parts = line.strip().split()
                if len(parts) >= 2:
                    # Note: address is in the second column
                    address = parts[1].lower()
                    name = parts[0]
                    token_names[address] = name
        return token_names
    except Exception as e:
        print(f"Error loading token names: {e}")
        return {}

if __name__ == "__main__":
    main()
