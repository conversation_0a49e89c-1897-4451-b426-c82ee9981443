#include <vector>
#include <queue>
#include <limits>

struct Edge {
    double weight;
    double capacity;
    int id;
};

typedef std::queue<Edge> EdgeGroup;

struct Path {
    bool is_valid;
    double weight_sum;
    double min_capacity;
    std::vector<int> edge_id_list;
};

const double MIN_CAPACITY_THRESHOLD = 1e-8;

class RerankGraph {
    //set public for test
    public:
        std::vector<EdgeGroup> edge_groups;
        std::vector<Path> paths;
        std::vector<std::vector<int>> edge_group_paths;
        
        int minimal_path_index;
        int actual_scan = 0;
        int no_lazy_compute_scan = 0;

    public:
        void update_path(Path& path);
        void update_all_paths();
        void use_minimal_path(double& total_amount);
        void edge_group_pop_front(int id);
        void update_minimal_path_index();

        void initialize_edge_group_paths() {
            edge_group_paths.clear();
            edge_group_paths.resize(edge_groups.size());
            for (int i = 0; i < paths.size(); ++i) {
                for (int edge_id : paths[i].edge_id_list) {
                    edge_group_paths[edge_id].push_back(i);
                }
            }
        }
};

void RerankGraph::update_all_paths() {
    actual_scan = 0;
    minimal_path_index = 0;
    double minimal_weight = std::numeric_limits<double>::max();
    for(int i = 0; i < paths.size(); i++) {
        auto& path = paths[i];
        update_path(path);
        if(path.weight_sum < minimal_weight) {
            minimal_weight = path.weight_sum;
            minimal_path_index = i;
        }
    }
    initialize_edge_group_paths();
}

// update the path with the edge group
void RerankGraph::update_path(Path& path) {
    path.weight_sum = 0;
    path.min_capacity = std::numeric_limits<double>::max();
    for(auto id: path.edge_id_list) {
        path.weight_sum += edge_groups[id].front().weight;
        path.min_capacity = std::min(path.min_capacity, edge_groups[id].front().capacity);
    }
}

void RerankGraph::edge_group_pop_front(int id) {
    std::cout << "edge group pop front: " << id << std::endl;
    std::cout << "edge group size: " << edge_groups[id].size() << std::endl;
    auto old_edge = edge_groups[id].front();
    edge_groups[id].pop();
    if(edge_groups[id].empty()) {
        std::cout << "edge group is empty" << std::endl;
        for(auto path_id: edge_group_paths[id]) {
            auto& path = paths[path_id];
            path.is_valid = false;
        }
        return;
    }
    auto& new_edge = edge_groups[id].front();
    for(auto path_id: edge_group_paths[id]) {
        actual_scan++;
        auto& path = paths[path_id];
        path.weight_sum += new_edge.weight - old_edge.weight;
        // lazy update min_capacity
        path.min_capacity = std::min(path.min_capacity, new_edge.capacity);
        no_lazy_compute_scan += path.edge_id_list.size();
    }
}

void RerankGraph::update_minimal_path_index() {
    minimal_path_index = 0;
    double minimal_weight = std::numeric_limits<double>::max();
    for(int i = 0; i < paths.size(); i++) {
        if(paths[i].is_valid && paths[i].weight_sum < minimal_weight) {
            minimal_weight = paths[i].weight_sum;
            minimal_path_index = i;
        }
    }
}

void RerankGraph::use_minimal_path(double& total_amount) {
    auto& path = paths[minimal_path_index];
    double deletion_amount = 0;
    deletion_amount = std::min(total_amount, path.min_capacity);
    std::cout << "deletion amount: " << deletion_amount << std::endl;
    total_amount -= deletion_amount;

    for(auto id: path.edge_id_list) {
        actual_scan++;
        no_lazy_compute_scan++;
        edge_groups[id].front().capacity -= deletion_amount;
        if(edge_groups[id].front().capacity < MIN_CAPACITY_THRESHOLD) {
            edge_group_pop_front(id);
        }
        else{
            for(auto path_id: edge_group_paths[id]) {
                actual_scan++;
                auto& path = paths[path_id];
                // lazy update min_capacity
                path.min_capacity = std::min(path.min_capacity, edge_groups[id].front().capacity);
                no_lazy_compute_scan += path.edge_id_list.size();
            }
        }
    }
    actual_scan+=path.edge_id_list.size();
    if(path.is_valid) {
        update_path(path);
    }
}
