#ifndef NETWORK_SIMPLEX_H  // Prevent multiple inclusions
#define NETWORK_SIMPLEX_H

#include <lemon/list_graph.h>
#include <lemon/network_simplex.h>

#include <iostream>
#include <fstream>
#include <sstream>
#include <vector>
#include <string>
#include <algorithm>
#include <cstdlib>

using namespace lemon;
using uint = unsigned int;

class Mincost {
public:
    // Constructor
    Mincost();

    // Reads the input file, builds the graph, and stores the source and sink
    void readGraphFromFile(const std::string &filename, uint s, uint t);

    // Runs the network simplex with the given supply value and returns the minimum cost
    long long mincost(long long supplyValue);

    // Prints the flow on each arc
    void printFlow();

private:
    // LEMON graph and associated maps
    ListDigraph g;
    std::vector<ListDigraph::Node> nodeVector;
    ListDigraph::ArcMap<int> capMap;
    ListDigraph::ArcMap<int> costMap;
    ListDigraph::NodeMap<int> supplyMap;
    ListDigraph::ArcMap<int> flowMap;

    // Stored source and sink node indices
    uint sId_, tId_;
};

#endif