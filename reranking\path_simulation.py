import pandas as pd
import json
import os
import glob
from web3 import Web3
import math
import copy
import time
from decimal import Decimal, getcontext, ROUND_DOWN, ROUND_HALF_UP
from collections import defaultdict
import argparse

# Set ultra-high precision for financial calculations
getcontext().prec = 256

EPSILON = Decimal('1e-18')

# ----- SIMULATION V2-----
class V2Simulator:
    """Simulator for Uniswap V2 pools."""
    
    def __init__(self, node_rpc_url: str) -> None:
        self.web3 = Web3(Web3.HTTPProvider(node_rpc_url))
        
        if not self.web3.is_connected():
            raise Exception("Failed to connect to the network")
        
        # Router contract for V2 pools
        V2_ROUTER2_ADDRESS = "0x7a250d5630B4cF539739dF2C5dAcb4c659F2488D"
        V2_ROUTER2_ABI = [
            {
                "inputs": [
                    {"internalType": "uint256", "name": "amountIn", "type": "uint256"},
                    {"internalType": "uint256", "name": "reserveIn", "type": "uint256"},
                    {"internalType": "uint256", "name": "reserveOut", "type": "uint256"},
                ],
                "name": "getAmountOut",
                "outputs": [
                    {"internalType": "uint256", "name": "amountOut", "type": "uint256"}
                ],
                "stateMutability": "pure",
                "type": "function",
            }
        ]
        
        self.router_contract = self.web3.eth.contract(
            address=V2_ROUTER2_ADDRESS, abi=V2_ROUTER2_ABI
        )
    
    def get_amount_out(self, in_amount: int, in_reserve: int, out_reserve: int) -> int:
        """Calculate output amount for V2 pools."""
        out_amount = self.router_contract.functions.getAmountOut(
            in_amount, in_reserve, out_reserve
        ).call()
        
        return out_amount

# ----- SIMULATION V3-----
def price_from_tick(tick: int) -> Decimal:
    """Calculate price as 1.0001 raised to the power of the tick."""
    return Decimal('1.0001') ** Decimal(tick)

def tick_from_price(price: Decimal) -> int:
    """Calculate the tick value corresponding to a given price."""
    epsilon = Decimal('1e-128')
    correction_value = Decimal('1e-18')

    if price <= epsilon:
        price = correction_value

    return int(math.floor(math.log(float(price)) / math.log(1.0001)))

def sqrt_price_q96_to_decimal(sqrt_price_x96: int) -> Decimal:
    """Convert Q64.96 sqrt price to a Decimal with high precision."""
    return Decimal(sqrt_price_x96) / (Decimal(2) ** 96)

def decimal_to_sqrt_price_q96(sqrt_price: Decimal) -> int:
    """Convert a Decimal representing the square root price back to a Q64.96 fixed-point integer."""
    return int((sqrt_price * (Decimal(2) ** 96)).to_integral(rounding=ROUND_DOWN))

# ----- Functions for token0 -> token1 (zeroForOne swap) -----
def calculate_dx(L: Decimal, sp_current: Decimal, sp_target: Decimal) -> Decimal:
    """Calculate the amount of token0 (dx) required to move the price from sp_current to sp_target."""
    if sp_current < EPSILON or sp_target < EPSILON:
        sp_current = EPSILON
        sp_target = EPSILON

    return L * (sp_current - sp_target) / (sp_current * sp_target)

def calculate_dy(L: Decimal, sp_current: Decimal, sp_target: Decimal) -> Decimal:
    """Calculate the amount of token1 (dy) received when moving the price from sp_current to sp_target."""
    if sp_current < EPSILON or sp_target < EPSILON:
        sp_current = EPSILON
        sp_target = EPSILON

    return L * (sp_current - sp_target)

def get_next_sqrt_price(L: Decimal, sp: Decimal, dx_net: Decimal) -> Decimal:
    """Calculate the new square root price after consuming a certain amount of token0 (dx_net)."""
    if L < EPSILON:
        L = EPSILON

    numerator = L * sp
    denominator = L + dx_net * sp
    return (numerator / denominator).quantize(Decimal('1e-18'), rounding=ROUND_DOWN)

# ----- Functions for token1 -> token0 (oneForZero swap) -----
def calculate_dy_one_for_zero(L: Decimal, sp_current: Decimal, sp_target: Decimal) -> Decimal:
    """Calculate the amount of token1 (dy) required to move the price from sp_current to sp_target."""
    return L * (sp_target - sp_current)

def calculate_dx_one_for_zero(L: Decimal, sp_current: Decimal, sp_target: Decimal) -> Decimal:
    """Calculate the amount of token0 (dx) output when moving the price from sp_current to sp_target."""
    return L * ((Decimal(1) / sp_current) - (Decimal(1) / sp_target))

def get_next_sqrt_price_one_for_zero(L: Decimal, sp: Decimal, dy_net: Decimal) -> Decimal:
    """Calculate the new square root price after consuming a certain amount of token1 (dy_net)."""
    return (sp + (dy_net / L)).quantize(Decimal('1e-18'), rounding=ROUND_DOWN)

def get_debug_logger():
    """Create a logger for mathematical debug information."""
    os.makedirs('results/log', exist_ok=True)
    debug_log_path = f"results/log/mathematical_debug.txt"
    
    def log_debug(message):
        with open(debug_log_path, "a", encoding="utf-8") as debug_log:
            debug_log.write(f"{message}\n")
    
    return log_debug

# ----- Simulation of token0 -> token1 swap (zeroForOne) -----
def simulate_swap_zero_for_one(pool_data: dict, amount_in: int, fee: Decimal):
    """Simulate a Uniswap V3 exactInput swap (token0 -> token1)."""
    log_debug = get_debug_logger()
    pool_state = pool_data["poolState"]["poolState"]
    fee_rate = fee
    sp_current = sqrt_price_q96_to_decimal(int(pool_state["sqrtPriceX96"]))

    if sp_current <= Decimal('1e-18'):
        log_debug(f"sp_current is too small in simulate_swap_zero_for_one: {sp_current}, setting to 1e-18")
        sp_current = Decimal('1e-18')

    L = Decimal(pool_state["liquidity"])
    amount_remaining = Decimal(amount_in)
    token1_out = Decimal(0)
    total_fee = Decimal(0)

    # Process ticks below current tick (descending order)
    ticks = sorted(
        [t for t in pool_state["tickBitMap"] if int(t[0]) < int(pool_state["currentTick"])],
        key=lambda x: -int(x[0])
    )

    for tick_info in ticks:
        tick_idx = int(tick_info[0])
        tick_data = tick_info[1]
        liquidity_net = Decimal(tick_data["liquidityNet"])
        initialized = tick_data["initialized"]

        if amount_remaining <= 0:
            break

        sp_target = (Decimal('1.0001') ** (Decimal(tick_idx) / 2)).quantize(Decimal('1e-18'), rounding=ROUND_DOWN)
        if sp_target >= sp_current:
            continue

        dx_net_needed = calculate_dx(L, sp_current, sp_target)
        dx_gross_needed = dx_net_needed / (Decimal(1) - fee_rate)

        if amount_remaining >= dx_gross_needed:
            token1_out += calculate_dy(L, sp_current, sp_target)
            total_fee += dx_gross_needed - dx_net_needed
            amount_remaining -= dx_gross_needed
            sp_current = sp_target

            if initialized:
                L -= liquidity_net
        else:
            dx_net_available = amount_remaining * (Decimal(1) - fee_rate)
            sp_new = get_next_sqrt_price(L, sp_current, dx_net_available)
            token1_out += calculate_dy(L, sp_current, sp_new)
            total_fee += amount_remaining - dx_net_available
            amount_remaining = Decimal(0)
            sp_current = sp_new

    # Handle the final partial swap if input still remains
    if amount_remaining > 0:
        dx_net_available = amount_remaining * (Decimal(1) - fee_rate)
        sp_new = get_next_sqrt_price(L, sp_current, dx_net_available)
        token1_out += calculate_dy(L, sp_current, sp_new)
        total_fee += amount_remaining - dx_net_available
        sp_current = sp_new

    if sp_current == 0:
        log_debug("sp_current became zero after swap, setting to 1e-18")
        sp_current = Decimal('1e-18')

    balance0 = Decimal(L / sp_current).to_integral(rounding=ROUND_DOWN)
    balance1 = Decimal(L * sp_current).to_integral(rounding=ROUND_DOWN)

    updated_state = copy.deepcopy(pool_state)
    updated_state["sqrtPriceX96"] = str(decimal_to_sqrt_price_q96(sp_current))
    updated_state["currentTick"] = str(tick_from_price(sp_current ** 2))
    updated_state["liquidity"] = str(int(L))
    updated_state["balance0"] = str(balance0)
    updated_state["balance1"] = str(balance1)

    return {
        "token1_output": token1_out,
        "token0_consumed": Decimal(amount_in) - amount_remaining,
        "fees_collected": total_fee,
        "new_pool_state": updated_state,
        "final_price": sp_current ** 2
    }

# ----- Simulation of token1 -> token0 swap (oneForZero) -----
def simulate_swap_one_for_zero(pool_data: dict, amount_in: int, fee: Decimal):
    """Simulate a Uniswap V3 exactInput swap (token1 -> token0)."""
    log_debug = get_debug_logger()
    pool_state = pool_data["poolState"]["poolState"]
    fee_rate = fee
    sp_current = sqrt_price_q96_to_decimal(int(pool_state["sqrtPriceX96"]))

    if sp_current <= Decimal('1e-18'):
        log_debug(f"sp_current is too small in simulate_swap_one_for_zero: {sp_current}, setting to 1e-18")
        sp_current = Decimal('1e-18')

    L = Decimal(pool_state["liquidity"])
    amount_remaining = Decimal(amount_in)
    token0_out = Decimal(0)
    total_fee = Decimal(0)

    # Process ticks above current tick (ascending order)
    ticks = sorted(
        [t for t in pool_state["tickBitMap"] if int(t[0]) > int(pool_state["currentTick"])],
        key=lambda x: int(x[0])
    )

    for tick_info in ticks:
        tick_idx = int(tick_info[0])
        tick_data = tick_info[1]
        liquidity_net = Decimal(tick_data["liquidityNet"])
        initialized = tick_data["initialized"]

        if amount_remaining <= 0:
            break

        sp_target = (Decimal('1.0001') ** (Decimal(tick_idx) / 2)).quantize(Decimal('1e-18'), rounding=ROUND_DOWN)
        if sp_target <= sp_current:
            continue

        dy_net_needed = calculate_dy_one_for_zero(L, sp_current, sp_target)
        dy_gross_needed = dy_net_needed / (Decimal(1) - fee_rate)

        if amount_remaining >= dy_gross_needed:
            token0_out += calculate_dx_one_for_zero(L, sp_current, sp_target)
            total_fee += dy_gross_needed - dy_net_needed
            amount_remaining -= dy_gross_needed
            sp_current = sp_target

            if initialized:
                L += liquidity_net
        else:
            dy_net_available = amount_remaining * (Decimal(1) - fee_rate)
            sp_new = get_next_sqrt_price_one_for_zero(L, sp_current, dy_net_available)
            token0_out += calculate_dx_one_for_zero(L, sp_current, sp_new)
            total_fee += amount_remaining - dy_net_available
            amount_remaining = Decimal(0)
            sp_current = sp_new

    # Handle the final partial swap if input still remains
    if amount_remaining > 0:
        dy_net_available = amount_remaining * (Decimal(1) - fee_rate)
        sp_new = get_next_sqrt_price_one_for_zero(L, sp_current, dy_net_available)
        token0_out += calculate_dx_one_for_zero(L, sp_current, sp_new)
        total_fee += amount_remaining - dy_net_available
        sp_current = sp_new

    balance0 = Decimal(L / sp_current).to_integral(rounding=ROUND_DOWN)
    balance1 = Decimal(L * sp_current).to_integral(rounding=ROUND_DOWN)

    updated_state = copy.deepcopy(pool_state)
    updated_state["sqrtPriceX96"] = str(decimal_to_sqrt_price_q96(sp_current))
    updated_state["currentTick"] = str(tick_from_price(sp_current ** 2))
    updated_state["liquidity"] = str(int(L))
    updated_state["balance0"] = str(balance0)
    updated_state["balance1"] = str(balance1)

    return {
        "token0_output": token0_out,
        "token1_consumed": Decimal(amount_in) - amount_remaining,
        "fees_collected": total_fee,
        "new_pool_state": updated_state,
        "final_price": sp_current ** 2
    }

class UniswapV3Mathematical:
    """Mathematical implementation for Uniswap V3 pools."""
    
    def __init__(self) -> None:
        self.v3_pools_data = []
    
    def initialize_pools(self, v3_pools_data):
        """Initialize with pool data from the loaded JSON"""
        self.v3_pools_data = v3_pools_data
    
    def exact_input(
        self,
        token_in: str,
        token_out: str,
        fee: int,
        amount_in: int,
        block_no: int,
        sqrt_price_limit_x96: int = 0,
    ) -> int:
        """Simulate exact input swap for V3 pools by mathematical calculation."""
        pool_data = self._get_pool_data(token_in, token_out, fee, block_no)
        
        if not pool_data:
            raise ValueError(f"Pool not found for {token_in} -> {token_out} with fee {fee}")
        
        fee_decimal = Decimal(fee) / Decimal(1000000)
        
        pool_token0 = pool_data["poolState"]["poolStaticInfo"]["token0"].lower()
        
        if token_in.lower() == pool_token0:
            result = simulate_swap_zero_for_one(pool_data, amount_in, fee_decimal)
            return int(result["token1_output"])
        else:
            result = simulate_swap_one_for_zero(pool_data, amount_in, fee_decimal)
            return int(result["token0_output"])
    
    def _get_pool_data(self, token_in: str, token_out: str, fee: int, block_no: int) -> dict:
        """Get pool data for a given token pair and fee."""
        token_in = token_in.lower()
        token_out = token_out.lower()
        
        for pool in self.v3_pools_data:
            pool_info = pool["poolState"]["poolStaticInfo"]
            pool_token0 = pool_info["token0"].lower()
            pool_token1 = pool_info["token1"].lower()
            pool_fee = int(pool_info["swapFee"])
            
            tokens_match = (
                (token_in == pool_token0 and token_out == pool_token1) or
                (token_in == pool_token1 and token_out == pool_token0)
            )
            
            if tokens_match and pool_fee == fee:
                return pool
        
        return None

# Replace V3Simulator with UniswapV3Mathematical
V3Simulator = UniswapV3Mathematical

def single_hop_simulation_external(
        token_in_addr: str,
        token_out_addr: str,
        pool_addr: str,
        pool_type: str,
        amount_in: int,
        block_number: int,
        v3_pools_data: list,
        v2_pools_data: list,
        v3_simulator,
        v2_simulator,
        log_collect=None
) -> int:
    """Perform a single hop simulation using either V2 or V3 data/pool (test.py style)."""
    if amount_in == 0:
        return 0

    # Determine if it's V2 or V3
    pool_type_lower = pool_type.lower()
    amount_out = 0

    # Log helper
    def logger(msg):
        if log_collect:
            log_collect(msg)

    if pool_type_lower == "v3" or "uniswap_v3" in pool_type_lower:
        # Find pool data from v3_pools_data
        # v3_pools_data is a list of pool objects. Each object can differ in structure.
        # We'll check for "poolState" -> "poolStaticInfo" -> "poolAddress" or "address".
        found_pool = None
        for pool in v3_pools_data:
            # Some data might store in pool["poolState"]["poolStaticInfo"]["poolAddress"]
            # or might store in pool["address"]. Adjust if needed:
            candidate = None
            if "poolState" in pool and "poolStaticInfo" in pool["poolState"]:
                candidate = pool["poolState"]["poolStaticInfo"].get("poolAddress", "")
            elif "address" in pool:
                candidate = pool["address"]
            if candidate and candidate.lower() == pool_addr.lower():
                found_pool = pool
                break

        if not found_pool:
            if logger:
                logger(f"  Error: V3 pool {pool_addr} not found in data, returning 0.")
            return 0

        # Extract swap fee
        if "poolState" in found_pool and "poolStaticInfo" in found_pool["poolState"]:
            fee = int(found_pool["poolState"]["poolStaticInfo"]["swapFee"])
            token0 = Web3.to_checksum_address(found_pool["poolState"]["poolStaticInfo"]["token0"])
            token1 = Web3.to_checksum_address(found_pool["poolState"]["poolStaticInfo"]["token1"])
        else:
            # fallback
            fee = 3000  # default if not found
            token0 = Web3.to_checksum_address(token_in_addr)
            token1 = Web3.to_checksum_address(token_out_addr)

        # We'll call exact_input with the correct direction.
        # The Quoter in principle doesn't require matching token0/token1 order.
        # We just pass token_in_addr, token_out_addr, fee, amount_in.
        # If the direction is reversed, it still returns the correct out.
        try:
            amount_out = v3_simulator.exact_input(
                token_in=Web3.to_checksum_address(token_in_addr),
                token_out=Web3.to_checksum_address(token_out_addr),
                fee=fee,
                amount_in=amount_in,
                block_no=block_number,
                sqrt_price_limit_x96=0
            )
        except Exception as e:
            if logger:
                logger(f"    V3 simulation error: {e}")
            amount_out = 0

    elif pool_type_lower == "v2" or "uniswap_v2" in pool_type_lower:
        # Find pool data from v2_pools_data
        found_pool = None
        for pool in v2_pools_data:
            # Try both possible data structures
            candidate = None
            if "poolState" in pool and "poolStaticInfo" in pool["poolState"]:
                candidate = pool["poolState"]["poolStaticInfo"].get("poolAddress", "")
            elif "poolAddress" in pool:
                candidate = pool.get("poolAddress", "")
            
            if candidate and candidate.lower() == pool_addr.lower():
                found_pool = pool
                break

        if not found_pool:
            if logger:
                logger(f"  Error: V2 pool {pool_addr} not found in data, returning 0.")
            return 0

        # Handle different data structures
        try:
            if "poolState" in found_pool and "poolStaticInfo" in found_pool["poolState"]:
                # Original structure
                reserve0 = int(found_pool["poolState"]["poolState"]["tokenBalance0"])
                reserve1 = int(found_pool["poolState"]["poolState"]["tokenBalance1"])
                t0 = Web3.to_checksum_address(found_pool["poolState"]["poolStaticInfo"]["token0"])
                t1 = Web3.to_checksum_address(found_pool["poolState"]["poolStaticInfo"]["token1"])
            else:
                # Alternative structure
                reserve0 = int(found_pool["poolState"]["poolState"]["tokenBalance0"])
                reserve1 = int(found_pool["poolState"]["poolState"]["tokenBalance1"])
                t0 = Web3.to_checksum_address(found_pool["poolState"]["poolStaticInfo"]["token0"])
                t1 = Web3.to_checksum_address(found_pool["poolState"]["poolStaticInfo"]["token1"])

            if token_in_addr.lower() == t0.lower() and token_out_addr.lower() == t1.lower():
                # direct
                amount_out = v2_simulator.get_amount_out(amount_in, reserve0, reserve1)
            elif token_in_addr.lower() == t1.lower() and token_out_addr.lower() == t0.lower():
                # reversed
                amount_out = v2_simulator.get_amount_out(amount_in, reserve1, reserve0)
            else:
                # If mismatch, fallback: 0
                if logger:
                    logger(f"    Warning: tokens do not match pool token0/token1 ordering. Returning 0.")
                amount_out = 0
        except Exception as e:
            if logger:
                logger(f"    V2 simulation error: {e}")
            amount_out = 0

    else:
        # Unknown type
        if logger:
            logger(f"  Error: Unknown pool type {pool_type}, returning 0.")
        amount_out = 0

    return amount_out

def simulate_and_update_folder(
    folder_path: str,
    v3_pools_file: str,
    v2_pools_file: str,
    node_rpc_url: str,
    block_number: int
) -> dict:
    """
    Simulate all JSON files in a folder and update them with toAmount results.
    
    Args:
        folder_path: Path to the folder containing allocation JSON files
        v3_pools_file: Path to the V3 pools data file
        v2_pools_file: Path to the V2 pools data file
        node_rpc_url: URL for the Ethereum node
        block_number: Block number for simulation
    
    Returns:
        Dictionary with summary of results
    """
    # Find all JSON files in the folder
    json_files = glob.glob(os.path.join(folder_path, "*.json"))
    
    if not json_files:
        print(f"No JSON files found in folder: {folder_path}")
        return {"error": "No JSON files found"}
    
    print(f"Found {len(json_files)} JSON files to process in {folder_path}")
    
    # Create output directories
    os.makedirs('results', exist_ok=True)
    os.makedirs('results/simulation_json', exist_ok=True)
    os.makedirs('results/log', exist_ok=True)
    
    # Create summary log file
    summary_log_path = f"results/log/batch_simulation_summary.txt"
    summary_log = open(summary_log_path, "w", encoding="utf-8")
    
    def log_summary(message):
        print(message)
        summary_log.write(message + "\n")
    
    log_summary(f"Starting batch simulation for {len(json_files)} files")
    log_summary(f"Block number: {block_number}")
    log_summary(f"Folder: {folder_path}")
    log_summary("-" * 80)
    
    # Load pools data once for all simulations
    log_summary("Loading pools data...")
    try:
        with open(v3_pools_file, "r") as file:
            v3_pools_data = json.load(file)
        with open(v2_pools_file, "r") as file:
            v2_pools_data = json.load(file)
        log_summary(f"Loaded {len(v3_pools_data)} V3 pools and {len(v2_pools_data)} V2 pools")
    except Exception as e:
        error_msg = f"Error loading pools data: {str(e)}"
        log_summary(error_msg)
        summary_log.close()
        return {"error": error_msg}
    
    # Initialize simulators once
    log_summary("Initializing simulators...")
    v3_simulator = V3Simulator()
    v3_simulator.initialize_pools(v3_pools_data)
    v2_simulator = V2Simulator(node_rpc_url)
    
    results_summary = {
        "total_files": len(json_files),
        "successful": 0,
        "failed": 0,
        "results": []
    }
    
    # Process each JSON file
    for i, json_file in enumerate(json_files, 1):
        filename = os.path.basename(json_file)
        log_summary(f"\n[{i}/{len(json_files)}] Processing: {filename}")
        
        try:
            # Load original JSON
            with open(json_file, 'r') as f:
                original_data = json.load(f)
            
            # Check if it's a valid allocation JSON
            if "fromAmount" not in original_data or "route" not in original_data:
                log_summary(f"  Skipping {filename}: Not a valid allocation JSON")
                continue
            
            # Run simulation using the internal function
            file_result = simulate_allocation_internal(
                allocation_data=original_data,
                v3_pools_data=v3_pools_data,
                v2_pools_data=v2_pools_data,
                v3_simulator=v3_simulator,
                v2_simulator=v2_simulator,
                block_number=block_number,
                name=filename.replace('.json', '')
            )
            
            if "error" in file_result:
                log_summary(f"  Failed: {file_result['error']}")
                results_summary["failed"] += 1
                results_summary["results"].append({
                    "file": filename,
                    "status": "failed",
                    "error": file_result["error"]
                })
                continue
            
            # Update original JSON with toAmount
            original_data["toAmount"] = file_result["toAmount"]
            
            # Write back to original file
            with open(json_file, 'w') as f:
                json.dump(original_data, f, indent=2)
            
            log_summary(f"  Success: Updated {filename} with toAmount = {file_result['toAmount']}")
            log_summary(f"  From: {original_data['fromAmount']} -> To: {file_result['toAmount']}")
            
            results_summary["successful"] += 1
            results_summary["results"].append({
                "file": filename,
                "status": "success",
                "fromAmount": original_data["fromAmount"],
                "toAmount": file_result["toAmount"],
                "from_token": original_data["from"],
                "to_token": original_data["to"]
            })
            
        except Exception as e:
            error_msg = f"Unexpected error processing {filename}: {str(e)}"
            log_summary(f"  Failed: {error_msg}")
            results_summary["failed"] += 1
            results_summary["results"].append({
                "file": filename,
                "status": "failed",
                "error": error_msg
            })
    
    # Write summary
    log_summary("\n" + "=" * 80)
    log_summary("BATCH SIMULATION SUMMARY")
    log_summary("=" * 80)
    log_summary(f"Total files processed: {results_summary['total_files']}")
    log_summary(f"Successful simulations: {results_summary['successful']}")
    log_summary(f"Failed simulations: {results_summary['failed']}")
    log_summary(f"Success rate: {results_summary['successful']/results_summary['total_files']*100:.1f}%")
    
    # Save detailed results
    results_file = f"results/simulation_json/batch_results_summary.json"
    with open(results_file, "w") as f:
        json.dump(results_summary, f, indent=2)
    
    log_summary(f"\nDetailed results saved to: {results_file}")
    log_summary(f"Summary log saved to: {summary_log_path}")
    
    summary_log.close()
    return results_summary

def simulate_allocation_internal(
    allocation_data: dict,
    v3_pools_data: list,
    v2_pools_data: list,
    v3_simulator,
    v2_simulator,
    block_number: int,
    name: str
) -> dict:
    """
    Internal function to simulate a single allocation without reloading pools data.
    This is more efficient for batch processing.
    Uses the same logic as simulate_entire_route from test.py but maintains the original interface.
    """
    try:
        # Extract information from allocation data
        input_amount = int(allocation_data["fromAmount"])
        input_token = allocation_data["from"].lower()
        target_token = allocation_data["to"].lower()
        fills = allocation_data["route"]["fills"]
        
        # Create debug logger for this simulation
        debug_log_path = f"results/log/simulation_debug_{name}.txt"
        os.makedirs('results/log', exist_ok=True)
        
        def log_collect(msg):
            with open(debug_log_path, "a", encoding="utf-8") as debug_log:
                debug_log.write(f"{msg}\n")
        
        log_collect(f"Starting simulation for {name}")
        log_collect(f"From: {input_token}, To: {target_token}, Amount: {input_amount}")
        log_collect(f"Number of fills: {len(fills)}")
        
        # Collect adjacency: node -> list of edges (edge = fill dict)
        adjacency = {}
        for f in fills:
            node_from = f["from"].lower()
            adjacency.setdefault(node_from, []).append(f)
        
        # Build incoming edge counts for each node
        incoming_edges = {}
        for f in fills:
            node_to = f["to"].lower()
            incoming_edges[node_to] = incoming_edges.get(node_to, 0) + 1
        
        # Track processed incoming edges
        processed_incoming = {node: 0 for node in incoming_edges}
        # Mark source node as fully processed (it has no incoming edges in our model)
        processed_incoming[input_token] = incoming_edges.get(input_token, 0)
        
        # We'll keep a map: node_input_amount[node] = how much token flows into that node
        node_input_amount = {}
        # The input_token starts with input_amount
        node_input_amount[input_token] = input_amount
        
        # Queue of nodes ready to process (all incoming edges processed)
        ready_queue = [input_token]
        
        # Track nodes we've fully processed
        processed_nodes = set()
        
        log_collect(f"Starting topological simulation with {len(fills)} edges")
        log_collect(f"Source node {input_token} has initial amount {input_amount}")
        
        while ready_queue:
            # Get next node to process
            current_node = ready_queue.pop(0)
            
            # Skip if already processed or has no input
            if current_node in processed_nodes or current_node not in node_input_amount:
                continue
            
            # Get node's current input amount
            current_input = node_input_amount[current_node]
            log_collect(f"Processing node {current_node} with input {current_input}")
            
            # Skip if no outgoing edges
            if current_node not in adjacency:
                log_collect(f"Node {current_node} has no outgoing edges")
                processed_nodes.add(current_node)
                continue
            
            # Get outgoing edges
            out_edges = adjacency[current_node]
            
            # Sum the proportions for distribution
            total_bps = 0
            for edge in out_edges:
                # Check if proportionBps exists, otherwise use poolTotalProportionBps
                proportion_bps = int(edge.get("proportionBps", edge.get("poolTotalProportionBps", 0)))
                total_bps += proportion_bps
            
            if total_bps == 0:
                log_collect(f"Node {current_node} has no valid proportions on outgoing edges")
                processed_nodes.add(current_node)
                continue
            
            log_collect(f"Node {current_node}, input={current_input}, distributing among {len(out_edges)} edges (total BPS: {total_bps})")
            
            # Process each outgoing edge
            for edge in out_edges:
                # Get proportion - try proportionBps first, then poolTotalProportionBps
                proportion_bps = int(edge.get("proportionBps", edge.get("poolTotalProportionBps", 0)))
                if proportion_bps <= 0:
                    continue
                    
                # Calculate amount to allocate to this edge
                allocated_in = int(current_input * proportion_bps / total_bps)
                if allocated_in <= 0:
                    continue
                    
                token_in_addr = edge["from"].lower()
                token_out_addr = edge["to"].lower()
                pool_addr = edge["pool"].lower()
                source = edge["source"]  # e.g. "Uniswap_V3" or "Uniswap_V2"
                
                log_collect(f"  Edge: {token_in_addr} -> {token_out_addr} via {pool_addr}")
                log_collect(f"  Pool type: {source}, Proportion: {proportion_bps/100:.2f}%")
                log_collect(f"  Input: {allocated_in}")
                
                # Perform single hop simulation using the external function
                out_amt = single_hop_simulation_external(
                    token_in_addr=token_in_addr,
                    token_out_addr=token_out_addr,
                    pool_addr=pool_addr,
                    pool_type=source,
                    amount_in=allocated_in,
                    block_number=block_number,
                    v3_pools_data=v3_pools_data,
                    v2_pools_data=v2_pools_data,
                    v3_simulator=v3_simulator,
                    v2_simulator=v2_simulator,
                    log_collect=log_collect
                )
                
                log_collect(f"  Output: {out_amt}")
                
                # Add output to destination node's input
                dest_node = token_out_addr
                if dest_node not in node_input_amount:
                    node_input_amount[dest_node] = 0
                node_input_amount[dest_node] += out_amt
                
                # Update processed incoming count for destination node
                processed_incoming[dest_node] = processed_incoming.get(dest_node, 0) + 1
                
                # Check if destination node is now ready to process
                if processed_incoming[dest_node] == incoming_edges.get(dest_node, 0) and dest_node not in processed_nodes:
                    log_collect(f"Node {dest_node} is now ready for processing (all {processed_incoming[dest_node]} incoming edges processed)")
                    ready_queue.append(dest_node)
            
            # Mark current node as fully processed
            processed_nodes.add(current_node)
        
        # The final output is how much ended up in the target_token node
        final_output = node_input_amount.get(target_token, 0)
        log_collect(f"Final output for {target_token}: {final_output}")
        
        return {"toAmount": str(final_output)}
        
    except Exception as e:
        return {"error": str(e)}

def simulate_from_allocation_json(
    allocation_json_file: str,
    v3_pools_file: str,
    v2_pools_file: str,
    node_rpc_url: str,
    block_number: int,
    name: str
) -> dict:
    """
    Backwards compatibility function for single file simulation.
    """
    # Load allocation JSON
    try:
        with open(allocation_json_file, 'r') as f:
            allocation_data = json.load(f)
    except Exception as e:
        return {"error": f"Failed to load allocation JSON: {str(e)}"}
    
    # Load pools data
    try:
        with open(v3_pools_file, "r") as file:
            v3_pools_data = json.load(file)
        with open(v2_pools_file, "r") as file:
            v2_pools_data = json.load(file)
    except Exception as e:
        return {"error": f"Failed to load pools data: {str(e)}"}
    
    # Initialize simulators
    v3_simulator = V3Simulator()
    v3_simulator.initialize_pools(v3_pools_data)
    v2_simulator = V2Simulator(node_rpc_url)
    
    # Run simulation
    result = simulate_allocation_internal(
        allocation_data=allocation_data,
        v3_pools_data=v3_pools_data,
        v2_pools_data=v2_pools_data,
        v3_simulator=v3_simulator,
        v2_simulator=v2_simulator,
        block_number=block_number,
        name=name
    )
    
    if "error" not in result:
        # Create full result structure like the original function
        full_result = allocation_data.copy()
        full_result["toAmount"] = result["toAmount"]
        full_result["simulation_metadata"] = {
            "block_number": block_number,
            "simulation_time": time.time(),
            "name": name
        }
        
        # Save result to JSON file
        os.makedirs('results/simulation_json', exist_ok=True)
        result_file = f"results/simulation_json/simulation_result_{name}.json"
        with open(result_file, "w") as f:
            json.dump(full_result, f, indent=2)
        
        return full_result
    
    return result

def main():
    parser = argparse.ArgumentParser(description='Simulate token swaps from allocation JSON files')
    parser.add_argument('--folder', type=str, default = "results/path_allocation_json",
                        help='Path to the folder containing allocation JSON files')
    parser.add_argument('--block_number', type=int, default=21974203,
                        help='Block number for simulation')
    args = parser.parse_args()
    
    # Configuration
    block_number = args.block_number
    node_rpc_url = ETH_NODE_URL
    
    # Pool data files
    v3_pools_file = f'../data/prices/block-{block_number}/filtered-v3pools.json'
    v2_pools_file = f'../data/prices/block-{block_number}/filtered-v2pools.json'
    
    print(f"Processing folder: {args.folder}")
    print(f"Block number: {block_number}")
    
    # Check if folder exists
    if not os.path.exists(args.folder):
        print(f"Error: Folder {args.folder} does not exist")
        return
    
    # Check if pool data files exist
    if not os.path.exists(v3_pools_file):
        print(f"Error: V3 pools file not found: {v3_pools_file}")
        return
    
    if not os.path.exists(v2_pools_file):
        print(f"Error: V2 pools file not found: {v2_pools_file}")
        return
    
    # Run batch simulation
    results = simulate_and_update_folder(
        folder_path=args.folder,
        v3_pools_file=v3_pools_file,
        v2_pools_file=v2_pools_file,
        node_rpc_url=node_rpc_url,
        block_number=block_number
    )
    
    if "error" in results:
        print(f"Batch simulation failed: {results['error']}")
    else:
        print(f"\nBatch simulation completed!")
        print(f"Total files: {results['total_files']}")
        print(f"Successful: {results['successful']}")
        print(f"Failed: {results['failed']}")
        print(f"Success rate: {results['successful']/results['total_files']*100:.1f}%")

if __name__ == "__main__":
    main() 