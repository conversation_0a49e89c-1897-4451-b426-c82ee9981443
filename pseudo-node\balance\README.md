# Arbitrage Path Detection and Simulation

This project provides tools to construct a token exchange graph, detect profitable paths using optimized DFS, and simulate the execution of these paths to evaluate potential profits.

## 📁 Project Structure

### 1. `build_graph.py`
- **Purpose**: Constructs the token graph and node mapping.
### 🔧 **Required Modification**
Update the function `calculate_exchange_rates_v2()` to:
- **Compute the token exchange price** for different range. Note different direction (`token0 → token1` and `token1 → token0`) model seperately.
- **Calculate the directional liquidity capacity** (`liquidity_value_eth`) for each sudo edge in the graph.
  - Use `pool_balance_map` to retrieve the reserves.
  - For each V2 pool, current price range capacity include two values ([capacity for `token0 → token1`, capacity for `token1 → token0`]), left and right price range include one value.
  - Ensure directionality is preserved when modeling graph edges.

### 2. `create_relationship.py`
- **Purpose**: Identifies and records equivalent addresses (pseudo nodes), used to merge logically identical nodes in the graph.

### 3. `dfs_optimize_v3_not_disjoint.cpp` + `run.py`
- **Purpose**: Detects arbitrage paths using a custom optimized DFS algorithm.
- **Usage**:
  - Compile:
    ```bash
    g++ -O3 dfs_optimize_v3_not_disjoint.cpp -o dfs_no_disjoint
    ```
  - Run:
    ```bash
    python run.py
    ```

### 4. `path_analysis_balance.py`
- **Purpose**: Analyzes the detected paths and identifies the associated liquidity pools used.

### 5. `simulate_json_balance.py`
- **Purpose**: Simulates the execution of paths and computes final results such as expected profit.

## 🚀 Execution Pipeline

1. **Build the Graph**  
   ```bash
   python build_graph.py
   ```

2. **Create Address Relationships (Pseudo Nodes)**  
   ```bash
   python create_relationship.py
   ```

3. **Compile and Run DFS for Path Detection**  
   ```bash
   g++ -O3 dfs_optimize_v3_not_disjoint.cpp -o dfs_no_disjoint
   python run.py
   ```

4. **Analyze Paths**  
   ```bash
   python path_analysis_balance.py
   ```

5. **Simulate Path Execution**  
   ```bash
   python simulate_json_balance.py
   ```

## 📝 Notes

- Ensure all required data files and dependencies are available.
- Adjust `calculate_exchange_rates_v3()` as needed to reflect accurate Uniswap V3 price and liquidity computations.
