import pandas as pd
import json
import os
import time
from decimal import Decimal, getcontext
import re
from collections import defaultdict
import argparse
import copy

# Set ultra-high precision for financial calculations
getcontext().prec = 256

def read_path_enumeration_results(file_path):
    """Extract paths from non-disjoint path file."""
    with open(file_path, 'r') as f:
        content = f.read()
    
    paths = []
    path_blocks = re.findall(r"Path \d+:\s*([\d\s]+)", content)
    
    for path in path_blocks:
        paths.append(path.strip())
    
    return {'paths': paths}

def process_and_save_paths(
    path_file: str,
    node_map_file: str,
    output_dir: str = "results/path_analysis_df",
    name: str = None,
    is_disjoint: bool = False,
    num_paths: int = None
) -> pd.DataFrame:
    """Process paths from a path file, analyze them, and save the results."""
    os.makedirs('results/log', exist_ok=True)
    process_log_path = f"results/log/process_paths_{os.path.basename(path_file)}.txt"
    with open(process_log_path, "w", encoding="utf-8") as process_log:
        def log_process(message):
            process_log.write(f"{message}\n")
        
        if name is None:
            name = os.path.basename(path_file).split('.')[0]
        
        # Read node map
        with open(node_map_file, 'r') as f:
            node_map = {}
            for line in f:
                parts = line.strip().split()
                if len(parts) >= 2:
                    index, token = parts[0], parts[1]
                    node_map[int(index)] = token
        
        # Extract paths
        paths_data = read_path_enumeration_results(path_file)
        paths = paths_data['paths']
        
        if num_paths is not None:
            paths = paths[:num_paths]
        
        # Prepare data for DataFrame
        data = []
        
        for i, path_str in enumerate(paths):
            token_path = [node_map[int(node_id)] for node_id in path_str.split()]
            
            clean_path = []
            for token in token_path:
                base_token = token.split('_')[0] if '_' in token else token
                if base_token not in clean_path:
                    clean_path.append(base_token)
            
            data.append({
                'path_id': i + 1,
                'path': ' -> '.join(clean_path),
                'token_sequence': clean_path
            })
        
        df = pd.DataFrame(data)
        os.makedirs(output_dir, exist_ok=True)
        output_file = f'{output_dir}/path_analysis_{name}.csv'
        df.to_csv(output_file, index=False)
        
        log_process(f"Path analysis saved to {output_file}")
        log_process(f"Total paths analyzed: {len(df)}")
    return df

def load_node_mappings(node_name_file, node_mapping_file):
    """Load both node name and node mapping information into dictionaries."""
    node_name = {}
    with open(node_name_file, 'r') as file:
        for line in file:
            line = line.strip()
            if line:
                parts = line.split()
                symbol, address = parts[0], parts[1]
                node_name[symbol] = address
    
    node_index = {}
    with open(node_mapping_file, 'r') as file:
        for line in file:
            line = line.strip()
            if line:
                parts = line.split()
                node_id, address = parts[0], parts[1]
                node_index[address] = node_id
    
    index_name = {int(node_index[addr]): name for name, addr in node_name.items() if addr in node_index}
    
    return node_name, node_index, index_name

def load_pool_data(weights_file_path=None, capacities_file_path=None):
    """Load pool weights and capacities from JSON files."""
    pool_weights = {}
    if weights_file_path and os.path.exists(weights_file_path):
        with open(weights_file_path, 'r') as f:
            pool_weights = json.load(f)
    
    pool_capacities = {}
    if capacities_file_path and os.path.exists(capacities_file_path):
        with open(capacities_file_path, 'r') as f:
            pool_capacities = json.load(f)
            
    return pool_weights, pool_capacities

def find_best_pool_for_edge(token_in, token_out, pool_weights, pool_capacities):
    """Find the best pool for a given edge based on weight and capacity."""
    best_weight = float('inf')
    best_capacity = 0
    best_pool = None
    best_pool_type = 'v3'
    
    token_pair_key = f"{token_in.lower()}_{token_out.lower()}"
    
    if token_pair_key not in pool_weights:
        return best_weight, best_capacity, best_pool, best_pool_type
    
    candidate_pools = []
    for pool_address, ticks in pool_weights[token_pair_key].items():
        for tick_data in ticks:
            weight = tick_data.get('weight', float('inf'))
            tick = tick_data.get('tick')
            pool_type = tick_data.get('pool_type', 'v3')
            
            capacity = 0
            if token_pair_key in pool_capacities and pool_address in pool_capacities.get(token_pair_key, {}):
                for cap_data in pool_capacities[token_pair_key][pool_address]:
                    if cap_data.get('tick') == tick:
                        capacity = cap_data.get('capacity', 0)
                        break
            
            if capacity > 0:
                candidate_pools.append({
                    'pool': pool_address,
                    'weight': weight,
                    'capacity': capacity,
                    'pool_type': pool_type
                })

    if not candidate_pools:
        return best_weight, best_capacity, best_pool, best_pool_type

    # Find the pool with the best weight among those with capacity
    best_pool_info = min(candidate_pools, key=lambda x: x['weight'])
    
    return best_pool_info['weight'], best_pool_info['capacity'], best_pool_info['pool'], best_pool_info['pool_type']


def calculate_path_metrics(tokens, pool_weights, pool_capacities):
    """Calculate the weight, capacity, and used pools for a single path."""
    total_weight = 0
    min_capacity = float('inf')
    used_pools = []
    
    for i in range(len(tokens) - 1):
        token_in = tokens[i]
        token_out = tokens[i+1]
        
        weight, capacity, pool_addr, pool_type = find_best_pool_for_edge(
            token_in, token_out, pool_weights, pool_capacities
        )
        
        if pool_addr is None: # Path is not viable
            return float('inf'), 0, []

        total_weight += weight
        min_capacity = min(min_capacity, capacity)
        used_pools.append({
            "pool": pool_addr,
            "from": token_in,
            "to": token_out,
            "source": f"Uniswap_V{pool_type[-1]}"
        })

    return total_weight, min_capacity, used_pools


def calculate_disjoint_proportions_bps(
    from_token: str, 
    path_tokens: list,
    price_dict: dict, 
    pool_capacities: dict,
    pool_weights: dict,
    token_decimals_dict: dict,
    original_amount: int,
):
    """
    Allocate input amount to disjoint paths based on the best weight heuristic.
    A pool can only be used by one path.
    """
    allocation_log_path = f"results/log/allocation_log_disjoint_{from_token[0:4]}_{original_amount}.txt"
    os.makedirs(os.path.dirname(allocation_log_path), exist_ok=True)
    with open(allocation_log_path, "w", encoding="utf-8") as allocation_log:
        def log_msg(message):
            print(message)
            allocation_log.write(message + "\n")

        log_msg(f"Starting DISJOINT allocation for {from_token} with amount {original_amount}")

        # 1. Initial Calculation of all path metrics
        log_msg("\n===== STEP 1: Initial Path Metric Calculation =====")
        all_paths_metrics = []
        for i, tokens in enumerate(path_tokens):
            weight, capacity, used_pools = calculate_path_metrics(tokens, pool_weights, pool_capacities)
            if weight != float('inf') and capacity > 0:
                all_paths_metrics.append({
                    "path_id": i,
                    "tokens": tokens,
                    "weight": weight,
                    "capacity": capacity,
                    "pools": used_pools,
                })
        
        # 2. Sort paths by weight (best to worst)
        log_msg("\n===== STEP 2: Sorting All Paths by Weight =====")
        all_paths_metrics.sort(key=lambda x: x['weight'])
        
        log_msg("Top 5 initial paths:")
        for p in all_paths_metrics[:5]:
            log_msg(f"  Path {p['path_id']+1}: Weight={p['weight']:.4f}, Capacity={p['capacity']:.4f}")

        # 3. Iterative Allocation (Greedy Heuristic)
        log_msg("\n===== STEP 3: Greedy Allocation to Disjoint Paths =====")
        
        proportions_bps = [0] * len(path_tokens)
        total_remaining_amount = Decimal(original_amount)
        
        used_pools_set = set()
        final_fills = []

        for path_metric in all_paths_metrics:
            if total_remaining_amount <= 0:
                log_msg("Allocation complete: entire input amount has been distributed.")
                break

            path_id = path_metric["path_id"]
            
            # Check if any pool in this path is already used
            is_disjoint = True
            path_pool_addresses = [p['pool'] for p in path_metric['pools']]
            for pool_addr in path_pool_addresses:
                if pool_addr in used_pools_set:
                    is_disjoint = False
                    log_msg(f"Path {path_id+1} skipped: Pool {pool_addr} is already used by a better path.")
                    break
            
            if not is_disjoint:
                continue

            # This is a valid, disjoint path. Allocate to it.
            log_msg(f"\nAllocating to Path {path_id+1} (Weight: {path_metric['weight']:.4f})")
            
            # Use prices to convert capacity to original token units
            from_price = Decimal(price_dict.get(from_token, 1))
            from_decimals = token_decimals_dict.get(from_token, 18)
            
            # Capacity is in ETH-equivalent, convert to `from_token` amount
            capacity_in_from_token = (Decimal(path_metric['capacity']) / from_price) * (10**from_decimals)
            
            amount_to_allocate = min(total_remaining_amount, capacity_in_from_token)
            
            log_msg(f"  Path Capacity (in {from_token}): {capacity_in_from_token:.4f}")
            log_msg(f"  Amount to Allocate: {amount_to_allocate:.4f}")

            proportion = (amount_to_allocate / Decimal(original_amount)) * 10000
            proportions_bps[path_id] = int(proportion)
            
            total_remaining_amount -= amount_to_allocate
            
            # Add this path's pools to the used set and its fills to the final list
            for p in path_metric['pools']:
                used_pools_set.add(p['pool'])
                fill = {
                    "from": p["from"],
                    "to": p["to"],
                    "pool": p["pool"],
                    "source": p["source"],
                    "poolTotalProportionBps": str(proportions_bps[path_id])
                }
                final_fills.append(fill)

            log_msg(f"  Allocated {proportions_bps[path_id]/100:.2f}% ({proportions_bps[path_id]} bps)")
            log_msg(f"  Remaining input amount: {total_remaining_amount:.4f}")


    return proportions_bps, final_fills


def generate_path_allocation_json(
    path_file: str,
    input_amount: int,
    name: str,
    price_dict: dict,
    token_decimals_dict: dict,
    weights_file: str,
    capacities_file: str,
) -> dict:
    """
    Generate disjoint path allocation percentages in JSON format.
    """
    os.makedirs('results/path_allocation_json', exist_ok=True)
    os.makedirs('results/log', exist_ok=True)
    
    df = pd.read_csv(path_file)
    input_token = df['path'].values[0].split(' -> ')[0].lower()
    
    pool_weights, pool_capacities = load_pool_data(weights_file, capacities_file)
    
    paths_tokens = [row['path'].split(' -> ') for i, row in df.iterrows()]
    
    start_time = time.time()
    
    proportions_bps, final_fills = calculate_disjoint_proportions_bps(
        input_token,
        paths_tokens,
        price_dict, 
        pool_capacities,
        pool_weights,
        token_decimals_dict,
        input_amount
    )
    
    execution_time = time.time() - start_time
    
    # Prepare result structure
    result = {
        "fromAmount": str(input_amount),
        "from": input_token,
        "to": df['path'].values[0].split(' -> ')[-1].lower(),
        "route": {"fills": final_fills},
        'allocation_metadata': {
            'type': 'disjoint_heuristic',
            'total_paths': len(paths_tokens),
            'allocated_paths': len([p for p in proportions_bps if p > 0]),
            'execution_time_seconds': execution_time,
        }
    }
    
    result_file = f"results/path_allocation_json/allocation_disjoint_{name}_{input_amount}.json"
    with open(result_file, "w") as f:
        json.dump(result, f, indent=2)
    
    print(f"Disjoint path allocation result saved to {result_file}")
    
    return result

def main():
    parser = argparse.ArgumentParser(description='Generate DISJOINT path allocation percentages for a batch of test pairs.')
    parser.add_argument('--block', type=int, default=21974203, help='Block number')
    args = parser.parse_args()

    # --- Test Configuration ---
    test_cases = [
        {'pair': 'wbtc-usdt', 'max_length': 3},
        {'pair': 'wbtc-spx', 'max_length': 3},
        {'pair': 'reth-wsteth', 'max_length': 3},
        {'pair': 'usdt-ohm', 'max_length': 3},
        {'pair': 'usdt-frax', 'max_length': 3},
        {'pair': 'usdt-cbbtc', 'max_length': 3},
        {'pair': 'spx-peas', 'max_length': 3},
        {'pair': 'spx-peas', 'max_length': 6},
    ]

    # Define amounts for each source token, matching the original test setup
    amounts_by_source = {
        'wbtc': [2000000000, 10000000000, 60000000000],
        'reth': [1000000000000000000, 20000000000000000000, 500000000000000000000],
        'usdt': [200000000000, 1000000000000, 5000000000000],
        'spx':  [40000000000000, 200000000000000, 1000000000000000],
    }
    
    # --- Common Setup ---
    block_number = args.block
    graph_name = 'combined'
    
    node_name_file = f"../data/node_name.txt"
    node_mapping_file = f"graphs/{block_number}_{graph_name}_node_map.txt"
    node_name, node_index, index_name = load_node_mappings(node_name_file, node_mapping_file)
    
    capacities_file = f'graphs/{block_number}_pool_capacities.json'
    weights_file = f'graphs/{block_number}_pool_weights.json'
    
    try:
        with open(f'../data/prices/block-{block_number}/tokens-quotes.json') as f:
            token_prices_data = json.load(f)
        price_dict = {token['address'].lower(): float(token['quotes']['avg_quote']) for token in token_prices_data}
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"Could not load token prices: {e}")
        return

    token_decimals_df = pd.read_csv(f'../data/token-decimals.csv')
    token_decimals_dict = {row['token_address']: row['decimals'] for _, row in token_decimals_df.iterrows()}

    # --- Execution Loop ---
    for case in test_cases:
        pair = case['pair']
        max_length = case['max_length']
        
        source_symbol, target_symbol = pair.split('-')
        
        # Get amounts for this source token
        input_amounts = amounts_by_source.get(source_symbol, [1000000000000000000]) # Default if source not in dict
        
        print(f"\nProcessing DISJOINT for pair: {source_symbol}-{target_symbol}, max_length: {max_length}")
        
        source_id = node_index.get(node_name.get(source_symbol))
        target_id = node_index.get(node_name.get(target_symbol))

        if not source_id or not target_id:
            print(f"  Could not find node index for {source_symbol} or {target_symbol}. Skipping.")
            continue
            
        name = f'all_paths_{source_id}_{target_id}_{max_length}'

        path_analysis_file = f'results/path_analysis_df/path_analysis_{name}.csv'
        if not os.path.exists(path_analysis_file):
            path_file = f'results/path/{name}.txt'
            if not os.path.exists(path_file):
                print(f"  WARNING: Path file not found, skipping: {path_file}")
                continue
            process_and_save_paths(
                path_file=path_file,
                node_map_file=node_mapping_file,
                name=name
            )

        for input_amount in input_amounts:
            print(f"  Testing with input amount: {input_amount}")
            generate_path_allocation_json(
                path_file=path_analysis_file,
                input_amount=input_amount,
                name=name,
                price_dict=price_dict,
                token_decimals_dict=token_decimals_dict,
                weights_file=weights_file,
                capacities_file=capacities_file
            )

if __name__ == "__main__":
    main()
