#include "../network_simplex.h"  // or include the class definition in your file
#include <iostream>
#include <cstdlib>

int main(int argc, char** argv) {
    if (argc < 5) {
        std::cerr << "Usage: " << argv[0] 
                  << " <filename> <source_node> <sink_node> <max_flow>\n";
        return 1;
    }
    std::string filename = argv[1];
    uint sId = std::atoi(argv[2]);
    uint tId = std::atoi(argv[3]);
    long long maxFlow = std::atoll(argv[4]);

    Mincost mc;
    mc.readGraphFromFile(filename, sId, tId);
    mc.mincost(maxFlow);
    // mc.printFlow();
    
    return 0;
}
