import os
import subprocess
import sys
from shutil import which

def main(graph_file, test_pairs_file):
    # Check if the solver executable exists in the current directory
    solver_path = './build/solver'
    if not os.path.isfile(solver_path) or not os.access(solver_path, os.X_OK):
        print("Error: 'solver' executable not found in the current directory.")
        print("Please ensure it is built and available. To build it, follow these steps:")
        print("1. Create a build directory: mkdir build")
        print("2. Navigate to the build directory: cd build")
        print("3. Run CMake to configure the project: cmake ..")
        print("4. Build the project: make")
        print("After these steps, the 'solver' executable should be available in the project directory.")
        sys.exit(1)

    # Prepare the output file
    output_filename = f"results_{os.path.basename(graph_file)}.txt"
    with open(output_filename, 'w') as output_file:
        # Read the test pairs
        with open(test_pairs_file, 'r') as pairs_file:
            for line in pairs_file:
                src, sink = line.strip().split()
                print(f"Running solver for source: {src}, sink: {sink}")

                # Execute the solver command
                result = subprocess.run([solver_path, graph_file, src, sink],
                                        capture_output=True, text=True)

                # Write the results to the output file
                output_file.write(f"Source: {src}, Sink: {sink}\n")
                output_file.write(result.stdout)
                output_file.write("\n" + "="*40 + "\n\n")

    print(f"All tests completed. Results are saved in '{output_filename}'.")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python run_tests.py <graph_file> <test_pairs_file>")
        sys.exit(1)

    graph_file = sys.argv[1]
    test_pairs_file = sys.argv[2]
    main(graph_file, test_pairs_file)
