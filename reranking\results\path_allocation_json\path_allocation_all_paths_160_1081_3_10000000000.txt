Starting path allocation generation for all_paths_160_1081_3
Input amount: 10000000000
Loading path analysis from results/path_analysis_df/path_analysis_all_paths_160_1081_3.csv...
Loading weights from graphs/21974203_processed_pool_weights.json...
Loaded weights for 2343 token pairs
Loading capacities from graphs/21974203_processed_pool_capacities.json...
Loaded capacities for 0 token pairs
Generating pool addresses for paths...
Warning: No pool found for 0x615987d46003cc37387dbe544ff4f16fa1200077 -> 0xe0f63a424a4439cbe457d80e4f4b51ad25b2c56c
Warning: No pool found for 0x2260fac5e5542a773aa44fbcfedf7c193bc2c599 -> 0x55296f69f40ea6d20e478533c15a6b08b654e758
Generated pool addresses for 11 paths

Starting path allocation algorithm...
Using lambda threshold: 0 (0 tokens)
Path allocation completed in 0.0831 seconds
Number of paths processed: 11
Path allocation result saved to results/path_allocation_json/allocation_all_paths_160_1081_3_10000000000.json
Total fills generated: 0
Allocation log saved to: results/path_allocation_json/path_allocation_all_paths_160_1081_3_10000000000.txt
